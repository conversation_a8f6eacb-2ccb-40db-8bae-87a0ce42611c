package com.get.salecenter.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.partnercenter.dto.RegisterPartnerUserDto;
import com.get.partnercenter.enums.MailTemplateTypeEnum;
import com.get.partnercenter.feign.IPartnerCenterClient;
import com.get.partnercenter.vo.RegisterPartnerUserVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.registrationcenter.entity.User;
import com.get.registrationcenter.feign.IRegistrationCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.newissue.NewIssueStudentMapper;
import com.get.salecenter.dao.newissue.NewIssueUserAgentMapper;
import com.get.salecenter.dao.newissue.newIssueUserSuperiorMapper;
import com.get.salecenter.dao.sale.AgentContractAccountMapper;
import com.get.salecenter.dao.sale.AgentContractCompanyMapper;
import com.get.salecenter.dao.sale.AgentContractMapper;
import com.get.salecenter.dao.sale.AppAgentContactPersonMapper;
import com.get.salecenter.dao.sale.AppAgentMapper;
import com.get.salecenter.dao.sale.ContactPersonCompanyMapper;
import com.get.salecenter.dao.sale.RStudentIssueStudentMapper;
import com.get.salecenter.dto.AgentContractAccountDto;
import com.get.salecenter.dto.AgentContractDto;
import com.get.salecenter.dto.AgentContractRenewalDto;
import com.get.salecenter.dto.AgentDto;
import com.get.salecenter.dto.AgentIdCardDto;
import com.get.salecenter.dto.AppAgentAddDto;
import com.get.salecenter.dto.AppAgentApproveCommentDto;
import com.get.salecenter.dto.AppAgentChangeDataDto;
import com.get.salecenter.dto.AppAgentContactPersonAddDto;
import com.get.salecenter.dto.AppAgentContactPersonListDto;
import com.get.salecenter.dto.AppAgentContractAccountAddDto;
import com.get.salecenter.dto.AppAgentContractAccountListDto;
import com.get.salecenter.dto.AppAgentDto;
import com.get.salecenter.dto.AppAgentListDto;
import com.get.salecenter.dto.AppAgentRenewalUpdateDto;
import com.get.salecenter.dto.AppAgentUpdateDto;
import com.get.salecenter.dto.ContactPersonDto;
import com.get.salecenter.dto.EmailSendContext;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.StudentAgentBindingDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.AgentStaff;
import com.get.salecenter.entity.AppAgent;
import com.get.salecenter.entity.AppAgentApproveComment;
import com.get.salecenter.entity.AppAgentContactPerson;
import com.get.salecenter.entity.AppAgentContractAccount;
import com.get.salecenter.entity.ContactPersonType;
import com.get.salecenter.entity.NewIssueStudent;
import com.get.salecenter.entity.NewIssueUserAgent;
import com.get.salecenter.entity.NewIssueUserSuperior;
import com.get.salecenter.entity.RStudentIssueStudent;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.entity.SaleContactPersonCompany;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.entity.StaffBdCode;
import com.get.salecenter.enums.AgentAppFromEnum;
import com.get.salecenter.enums.AgentAppTypeEnum;
import com.get.salecenter.enums.AgentContractApprovalStatusEnum;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.enums.ContractTemplateModeEnum;
import com.get.salecenter.enums.MiniProgramPageEnum;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractAccountService;
import com.get.salecenter.service.IAgentContractAgentAccountService;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IAgentStaffService;
import com.get.salecenter.service.IAppAgentApproveCommentService;
import com.get.salecenter.service.IAppAgentContactPersonService;
import com.get.salecenter.service.IAppAgentContractAccountService;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.service.IContactPersonTypeService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.service.IStudentService;
import com.get.salecenter.utils.EmailSenderUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.AddAppAgentContext;
import com.get.salecenter.vo.AppAgentChangeDataVo;
import com.get.salecenter.vo.AppAgentContactPersonListVo;
import com.get.salecenter.vo.AppAgentContractAccountListVo;
import com.get.salecenter.vo.AppAgentFormDetailVo;
import com.get.salecenter.vo.AppAgentListVo;
import com.get.salecenter.vo.AppAgentSetAgreeContext;
import com.get.salecenter.vo.AppAgentVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.seata.spring.annotation.GlobalTransactional;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.get.salecenter.service.impl.AppAgentChangeStatementPdfGenerator.generatePdf;


/**
 * @author: Hardy
 * @create: 2022/11/17 17:16
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class AppAgentServiceImpl extends GetServiceImpl<AppAgentMapper, AppAgent> implements IAppAgentService {

    //授权用户IAE访问桶网址
    public final static String OSS_FILES_IAE_PRD_URL = "https://hti-ais-files-prd-**********.cos.ap-shanghai.myqcloud.com";
    //授权用户访问桶网址
    public final static String OSS_FILES_DEV_URL = "https://hti-ais-files-dev-**********.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_FILES_PRD_URL = "https://get-bms-files-prd-**********.cos.ap-shanghai.myqcloud.com";
    public final static String OSS_FILES_TEST_URL = "https://get-bms-files-test-**********.cos.ap-shanghai.myqcloud.com";

    @Resource
    private AgentContractAccountMapper agentContractAccountMapper;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IPartnerCenterClient partnerCenterClient;

    @Resource
    private EmailSenderUtils emailSenderUtils;


    @Resource
    private ContactPersonCompanyMapper contactPersonCompanyMapper;

    @Resource
    private IAgentContractAgentAccountService agentContractAgentAccountService;

    @Resource
    private AgentContractMapper agentContractMapper;

    @Resource
    private AgentContractCompanyMapper agentContractCompanyMapper;

    @Resource
    private UtilService utilService;
    @Resource
    private AppAgentMapper appAgentMapper;
    @Lazy
    @Resource
    private IAppAgentContactPersonService appAgentContactPersonService;
    @Resource
    private AppAgentContactPersonMapper appAgentContactPersonMapper;
    @Lazy
    @Resource
    private IAppAgentContractAccountService appAgentContractAccountService;
    @Lazy
    @Resource
    private IAppAgentApproveCommentService appAgentApproveCommentService;
    @Lazy
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IRegistrationCenterClient registrationCenterClient;
    @Lazy
    @Resource
    private IAgentService agentService;
    @Lazy
    @Resource
    private IContactPersonTypeService contactPersonTypeService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Lazy
    @Resource
    private IAgentContractService agentContractService;
    @Lazy
    @Resource
    private IAgentCompanyService agentCompanyService;
    @Lazy
    @Resource
    private IAgentContractAccountService agentContractAccountService;
    @Lazy
    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Lazy
    @Resource
    private IAgentStaffService agentStaffService;
    @Lazy
    @Resource
    private IContactPersonService contactPersonService;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private NewIssueUserAgentMapper newIssueUserAgentMapper;
    @Resource
    private newIssueUserSuperiorMapper newIssueUserSuperiorMapper;
    @Resource
    private NewIssueStudentMapper newIssueStudentMapper;
    @Resource
    private RStudentIssueStudentMapper rStudentIssueStudentMapper;
    @Lazy
    @Resource
    private IStudentService studentService;
    @Resource
    private GetRedis getRedis;

    /**
     * 代理申请数据校验
     *
     * @param appAgentAddDto
     */
    private void validateAppAgent(AppAgentAddDto appAgentAddDto) {
        if (GeneralTool.isEmpty(appAgentAddDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<AppAgentContactPersonAddDto> appAgentContactPersonAddVoList = appAgentAddDto
                .getAppAgentContactPersonAddVos();
        if (CollectionUtil.isEmpty(appAgentContactPersonAddVoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_contact_person_not_null"));
        }
        if (!AgentAppFromEnum.isNewType(appAgentAddDto.getAppFrom())) {
            return;
        }
        // 校验新版联系人数据
        validateNewAppAgentContractPersonList(appAgentContactPersonAddVoList);
    }

    /**
     * 校验新版联系人数据
     *
     * @param appAgentContactPersonAddVoList
     */
    private void validateNewAppAgentContractPersonList(
            List<AppAgentContactPersonAddDto> appAgentContactPersonAddVoList) {
        if (CollectionUtil.isEmpty(appAgentContactPersonAddVoList)) {
            log.error("代理联系人不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_contact_person_cannot_be_empty"));
        }
        if (appAgentContactPersonAddVoList.size() != 3) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_contact_person_count_invalid"));
        }
        // 使用ContactPersonTypeEnum中的校验方法验证联系人类型
        List<String> contactPersonTypeCodes = appAgentContactPersonAddVoList.stream()
                .map(AppAgentContactPersonAddDto::getFkContactPersonTypeKey)
                .collect(Collectors.toList());
        // 校验联系人类型是否合法
        if (!ContactPersonTypeEnum.validateRequiredContactPersonTypes(contactPersonTypeCodes)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_new_contact_person_type_invalid"));
        }

        // 校验紧急联系人不能与企业负责人/小程序管理员和佣金结算负责人是同一个人
        // 紧急联系人
        AppAgentContactPersonAddDto emergencyContact = appAgentContactPersonAddVoList.stream()
                .filter(contact -> ContactPersonTypeEnum.EMERGENCY.getCode()
                        .equals(contact.getFkContactPersonTypeKey()))
                .findFirst()
                .orElse(null);

        // 检查紧急联系人与其他两种角色是否有相同的姓名或邮箱（避免角色重复）
        boolean hasDuplicate = appAgentContactPersonAddVoList.stream()
                .filter(contact -> !ContactPersonTypeEnum.EMERGENCY.getCode()
                        .equals(contact.getFkContactPersonTypeKey()))
                .anyMatch(contact -> contact.getName().equals(emergencyContact.getName())
                        || contact.getEmail().equals(emergencyContact.getEmail()));

        if (hasDuplicate) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_emergency_contact_duplicate"));
        }
    }

    /**
     * 代理申请续签修改
     *
     * @param appAgentRenewalUpdateDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renewalUpdate(AppAgentRenewalUpdateDto appAgentRenewalUpdateDto) {
        Long id = appAgentRenewalUpdateDto.getId();
        if (ObjectUtils.isNull(id)) {
            log.error("代理申请id不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_application_id_cannot_be_empty"));
        }
        List<AppAgentContactPersonAddDto> appAgentContactPersonAddVos = appAgentRenewalUpdateDto
                .getAppAgentContactPersonAddVos();
        // 校验新版联系人数据
        this.validateNewAppAgentContractPersonList(appAgentContactPersonAddVos);

        // 更新代理申请数据
        AppAgent appAgent = this.getById(id);
        if (ObjectUtils.isNull(appAgent)) {
            log.error("代理申请数据有误");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_app_data_error"));
        }
        appAgent.setNatureNote(appAgentRenewalUpdateDto.getNatureNote());
        appAgent.setContractStartTime(appAgentRenewalUpdateDto.getContractStartTime());
        appAgent.setContractEndTime(appAgentRenewalUpdateDto.getContractEndTime());
        appAgent.setChangeStatement(appAgentRenewalUpdateDto.getChangeStatement());
        appAgent.setGmtModified(new Date());
        appAgent.setGmtModifiedUser(SecureUtil.getLoginId());

        boolean updateResult = this.updateById(appAgent);
        if (!updateResult) {
            log.error("更新失败");
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        // 2.保存联系人
        AddAppAgentContext addAppAgentContext = createAddAppAgentContext(appAgent, appAgentRenewalUpdateDto);
        doSaveAppAgentContactPerson(addAppAgentContext);

        // 4.发送邮件 添加提醒任务
        addReminderTask(addAppAgentContext);
    }

    /**
     * 续约审核通过修改
     *
     * @param appAgentAddDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renewalAgreeUpdate(AppAgentAddDto appAgentAddDto) {
        log.info("开始处理续约审批同意，参数: {}", appAgentAddDto);

        // 参数校验
        if (ObjectUtils.isNull(appAgentAddDto)) {
            log.error("续约审批数据不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Long appAgentId = appAgentAddDto.getId();
        if (ObjectUtils.isNull(appAgentId)) {
            log.error("代理申请ID不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Long fkAgentId = appAgentAddDto.getFkAgentId();
        if (ObjectUtils.isNull(fkAgentId)) {
            log.error("关联代理ID不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }

        // 获取现有的AppAgent记录
        AppAgent existingAppAgent = this.getById(appAgentId);
        if (ObjectUtils.isNull(existingAppAgent)) {
            log.error("代理申请数据不存在，ID: {}", appAgentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_app_data_error"));
        }

        // 校验是否为续约申请
        if (!AgentAppTypeEnum.RENEWAL_APPLICATION.getCode().equals(existingAppAgent.getAppType())) {
            log.error("不是续约申请类型，无法进行续约审批，AppType: {}", existingAppAgent.getAppType());
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_renewal_application"));
        }

        // ====== 第一步：更新AppAgent及其关联数据 ======
        updateAppAgentAndRelatedData(appAgentAddDto, existingAppAgent);

        // ====== 第二步：调用原来的doSetAgree方法处理审批同意 ======
        AppAgentVo appAgentVo = BeanCopyUtils.objClone(existingAppAgent, AppAgentVo::new);
        doSetAgree(appAgentVo);

        log.info("续约审批同意处理完成，AppAgentId: {}, AgentId: {}", appAgentId, fkAgentId);
    }

    /**
     * 第一步：更新AppAgent及其关联数据
     */
    private void updateAppAgentAndRelatedData(AppAgentAddDto appAgentAddDto, AppAgent existingAppAgent) {
        log.info("开始更新AppAgent及其关联数据，AppAgentId: {}", existingAppAgent.getId());

        // 1. 更新AppAgent主记录
        updateAppAgentMainRecord(appAgentAddDto, existingAppAgent);

        // 2. 更新联系人数据（采用有则更新，无则新增策略）
        updateAppAgentContactPersons(appAgentAddDto);

        // 3. 更新合同账户数据（采用有则更新，无则新增策略）
        updateAppAgentContractAccounts(appAgentAddDto);

        log.info("AppAgent及其关联数据更新完成");
    }

    /**
     * 更新AppAgent主记录
     */
    private void updateAppAgentMainRecord(AppAgentAddDto appAgentAddDto, AppAgent existingAppAgent) {
        // 使用现有数据更新，保留原有的关键字段
        BeanCopyUtils.copyProperties(appAgentAddDto, existingAppAgent,
                "id", "appType", "fkAgentId", "appStatus", "gmtCreate", "gmtCreateUser");

        utilService.setUpdateInfo(existingAppAgent);
        boolean result = this.updateById(existingAppAgent);
        if (!result) {
            log.error("更新AppAgent主记录失败，ID: {}", existingAppAgent.getId());
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        log.info("AppAgent主记录更新成功");
    }

    /**
     * 更新AppAgent联系人数据（有则更新，无则新增）- 优化批量操作
     */
    private void updateAppAgentContactPersons(AppAgentAddDto appAgentAddDto) {
        List<AppAgentContactPersonAddDto> contactPersonDtos = appAgentAddDto.getAppAgentContactPersonAddVos();
        if (CollectionUtil.isEmpty(contactPersonDtos)) {
            log.warn("没有联系人数据需要更新");
            return;
        }

        // 分组处理：有ID的更新，无ID的新增
        List<AppAgentContactPersonAddDto> toUpdateDtos = new ArrayList<>();
        List<AppAgentContactPersonAddDto> toInsertDtos = new ArrayList<>();

        for (AppAgentContactPersonAddDto dto : contactPersonDtos) {
            if (GeneralTool.isNotEmpty(dto.getId())) {
                toUpdateDtos.add(dto);
            } else {
                toInsertDtos.add(dto);
            }
        }

        // 批量更新现有记录
        if (CollectionUtil.isNotEmpty(toUpdateDtos)) {
            List<Long> updateIds = toUpdateDtos.stream().map(AppAgentContactPersonAddDto::getId)
                    .collect(Collectors.toList());
            List<AppAgentContactPerson> existingContactPersons = appAgentContactPersonService.listByIds(updateIds);
            Map<Long, AppAgentContactPerson> existingMap = existingContactPersons.stream()
                    .collect(Collectors.toMap(AppAgentContactPerson::getId, Function.identity()));

            List<AppAgentContactPerson> updateList = new ArrayList<>();
            for (AppAgentContactPersonAddDto dto : toUpdateDtos) {
                AppAgentContactPerson existing = existingMap.get(dto.getId());
                if (existing != null) {
                    BeanCopyUtils.copyProperties(dto, existing, "id", "fkAppAgentId");
                    utilService.setUpdateInfo(existing);
                    updateList.add(existing);
                }
            }

            if (CollectionUtil.isNotEmpty(updateList)) {
                boolean result = appAgentContactPersonService.updateBatchById(updateList);
                if (!result) {
                    log.error("批量更新联系人失败");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
                log.debug("批量更新联系人成功，数量: {}", updateList.size());
            }
        }

        // 批量新增记录
        if (CollectionUtil.isNotEmpty(toInsertDtos)) {
            List<AppAgentContactPerson> insertList = new ArrayList<>();
            for (AppAgentContactPersonAddDto dto : toInsertDtos) {
                AppAgentContactPerson newContactPerson = BeanCopyUtils.objClone(dto, AppAgentContactPerson::new);
                newContactPerson.setFkAppAgentId(appAgentAddDto.getId());
                utilService.setCreateInfo(newContactPerson);
                insertList.add(newContactPerson);
            }

            boolean result = appAgentContactPersonService.saveBatch(insertList);
            if (!result) {
                log.error("批量新增联系人失败");
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            log.debug("批量新增联系人成功，数量: {}", insertList.size());
        }

        log.info("AppAgent联系人数据更新完成，更新: {}, 新增: {}", toUpdateDtos.size(), toInsertDtos.size());
    }

    /**
     * 更新AppAgent合同账户数据（有则更新，无则新增）- 优化批量操作
     */
    private void updateAppAgentContractAccounts(AppAgentAddDto appAgentAddDto) {
        List<AppAgentContractAccountAddDto> contractAccountDtos = appAgentAddDto.getAppAgentContractAccountAddVos();
        if (CollectionUtil.isEmpty(contractAccountDtos)) {
            log.warn("没有合同账户数据需要更新");
            return;
        }

        // 分组处理：有ID的更新，无ID的新增
        List<AppAgentContractAccountAddDto> toUpdateDtos = new ArrayList<>();
        List<AppAgentContractAccountAddDto> toInsertDtos = new ArrayList<>();

        for (AppAgentContractAccountAddDto dto : contractAccountDtos) {
            if (GeneralTool.isNotEmpty(dto.getId())) {
                toUpdateDtos.add(dto);
            } else {
                toInsertDtos.add(dto);
            }
        }

        // 批量更新现有记录
        if (CollectionUtil.isNotEmpty(toUpdateDtos)) {
            List<Long> updateIds = toUpdateDtos.stream().map(AppAgentContractAccountAddDto::getId)
                    .collect(Collectors.toList());
            List<AppAgentContractAccount> existingContractAccounts = appAgentContractAccountService
                    .listByIds(updateIds);
            Map<Long, AppAgentContractAccount> existingMap = existingContractAccounts.stream()
                    .collect(Collectors.toMap(AppAgentContractAccount::getId, Function.identity()));

            List<AppAgentContractAccount> updateList = new ArrayList<>();
            for (AppAgentContractAccountAddDto dto : toUpdateDtos) {
                AppAgentContractAccount existing = existingMap.get(dto.getId());
                if (existing != null) {
                    BeanCopyUtils.copyProperties(dto, existing, "id", "fkAppAgentId");
                    utilService.setUpdateInfo(existing);
                    updateList.add(existing);
                }
            }

            if (CollectionUtil.isNotEmpty(updateList)) {
                boolean result = appAgentContractAccountService.updateBatchById(updateList);
                if (!result) {
                    log.error("批量更新合同账户失败");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
                log.debug("批量更新合同账户成功，数量: {}", updateList.size());
            }
        }

        // 批量新增记录
        if (CollectionUtil.isNotEmpty(toInsertDtos)) {
            List<AppAgentContractAccount> insertList = new ArrayList<>();
            for (AppAgentContractAccountAddDto dto : toInsertDtos) {
                AppAgentContractAccount newContractAccount = BeanCopyUtils.objClone(dto, AppAgentContractAccount::new);
                newContractAccount.setFkAppAgentId(appAgentAddDto.getId());
                utilService.setCreateInfo(newContractAccount);
                insertList.add(newContractAccount);
            }

            boolean result = appAgentContractAccountService.saveBatch(insertList);
            if (!result) {
                log.error("批量新增合同账户失败");
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            log.debug("批量新增合同账户成功，数量: {}", insertList.size());
        }

        log.info("AppAgent合同账户数据更新完成，更新: {}, 新增: {}", toUpdateDtos.size(), toInsertDtos.size());
    }

    /**
     * 变更申请数据获取
     *
     * @param fkAppAgentId
     * @return
     */
    @Override
    public AppAgentChangeDataVo getChangeData(Long fkAppAgentId) {
        if (ObjectUtils.isNull(fkAppAgentId)) {
            log.error("代理申请id数据不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AppAgent appAgent = this.getById(fkAppAgentId);
        if (ObjectUtils.isNull(appAgent)) {
            log.error("代理数据有误");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_exist"));
        }
        Long fkAgentId = appAgent.getFkAgentId();
        if (ObjectUtils.isNull(fkAgentId)) {
            log.error("代理管理id为空, 数据有误");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }
        AppAgentChangeDataVo appAgentChangeDataVo = new AppAgentChangeDataVo();
        appAgentChangeDataVo.setNatureNote(appAgent.getNatureNote());
        appAgentChangeDataVo.setAppStatus(appAgent.getAppStatus());
        LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()
                .eq(AgentContract::getFkAgentId, fkAgentId)
                .eq(AgentContract::getIsActive, true)
                .orderByDesc(AgentContract::getGmtCreate)
                .last("LIMIT 1");
        AgentContract agentContract = this.agentContractService.getOne(agentContractLambdaQueryWrapper);
        if (agentContract == null) {
            return appAgentChangeDataVo;
        }
        // 合同编号
        appAgentChangeDataVo.setContractNum(agentContract.getContractNum());
        return appAgentChangeDataVo;
    }

    /**
     * 变更申请数据修改
     *
     * @param appAgentChangeDataDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeData(AppAgentChangeDataDto appAgentChangeDataDto) {
        if (ObjectUtils.isNull(appAgentChangeDataDto)) {
            log.error("数据为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Long appAgentId = appAgentChangeDataDto.getId();
        if (ObjectUtils.isNull(appAgentId)) {
            log.error("数据为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AppAgent appAgent = this.getById(appAgentId);
        if (ObjectUtils.isNull(appAgent)) {
            log.error("数据有误");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_exist"));
        }
        appAgent.setChangeStatement(appAgentChangeDataDto.getChangeStatement());
        boolean result = this.updateById(appAgent);
        if (!result) {
            log.error("更新失败");
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 获取代理变更申请信息导出为Pdf
     *
     * @param appAgentDto
     * @param response
     */
    @Override
    public void getLetterForAgentInformationChangePdf(AppAgentDto appAgentDto, HttpServletResponse response) {
        if (GeneralTool.isEmpty(appAgentDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        }
        if (GeneralTool.isEmpty(appAgentDto.getChangeStatement())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        }
        String changeStatement = appAgentDto.getChangeStatement();
        changeStatement = changeStatement.replaceAll("\\\\n", "\n");
        // 创建pdf模版
        // 根据json内容填充pdf
        // 导出pdf
        try {
            generatePdf(appAgentDto, response);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        }
    }

    /**
     * 过滤续约同意合同账户数据
     */
    private void filterRenewalAgreeContractAccount(AppAgentAddDto appAgentAddDto) {
        Long appAgentAddDtoId = appAgentAddDto.getId();
        if (appAgentAddDtoId == null) {
            return;
        }
        List<AppAgentContractAccountAddDto> appAgentContractAccountAddVos = appAgentAddDto
                .getAppAgentContractAccountAddVos();
        if (CollectionUtil.isEmpty(appAgentContractAccountAddVos)) {
            return;
        }
        List<Long> idList = appAgentContractAccountAddVos.stream()
                .map(AppAgentContractAccountAddDto::getId)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        List<AppAgent> appAgents = this.appAgentMapper.selectBatchIds(idList);
        if (CollectionUtil.isEmpty(appAgents)) {
            return;
        }

        List<AppAgentContractAccountAddDto> appAgentFilter = appAgentContractAccountAddVos.stream()
                .filter(appAgentContractAccountAddDto -> appAgentContractAccountAddDto.getId() == null
                        && !appAgentAddDtoId.equals(appAgentContractAccountAddDto.getId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(appAgentFilter)) {
            appAgentAddDto.setAppAgentContractAccountAddVos(appAgentFilter);
        }
    }

    /**
     * 代理申请表单新增
     *
     * @param appAgentAddDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String addAppAgent(AppAgentAddDto appAgentAddDto) {
        AddAppAgentContext addAppAgentContext = saveOrUpdateAppAgent(appAgentAddDto);

        if (!AgentAppFromEnum.isNewType(appAgentAddDto.getAppFrom())) {
            // 发送旧版邮件
            addReminderTask(addAppAgentContext);
        } else {
            // 发送新版代理申请提交邮件
            sendApplicationSubmittedEmails(addAppAgentContext);
        }

        String encrypt = null;
        try {
            encrypt = AESUtils.Encrypt(String.valueOf(addAppAgentContext.getFkAppAgentId()), AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encryption_failed"));
        }
        return encrypt;
    }

    /**
     * 保存或更新代理管理数据
     *
     * @param appAgentAddDto
     * @return
     */
    private AddAppAgentContext saveOrUpdateAppAgent(AppAgentAddDto appAgentAddDto) {
        // 代理申请数据校验
        this.validateAppAgent(appAgentAddDto);

        // 1.保存代理申请
        AddAppAgentContext addAppAgentContext = doSaveAppAgent(appAgentAddDto);
        // 2.保存联系人
        doSaveAppAgentContactPerson(addAppAgentContext);
        // 3.保存合同账户
        doSaveAppAgentContractAccount(addAppAgentContext);
        return addAppAgentContext;
    }

    /**
     * 创建AddAppAgentContext对象用于提醒任务
     *
     * @param appAgent                 代理申请实体
     * @param appAgentRenewalUpdateDto 续签更新DTO
     * @return AddAppAgentContext对象
     */
    private AddAppAgentContext createAddAppAgentContext(AppAgent appAgent,
                                                        AppAgentRenewalUpdateDto appAgentRenewalUpdateDto) {
        // 创建AppAgentAddDto对象
        AppAgentAddDto appAgentAddDto = new AppAgentAddDto();
        appAgentAddDto.setId(appAgent.getId());
        appAgentAddDto.setFkCompanyId(appAgent.getFkCompanyId());
        appAgentAddDto.setFkStaffId(appAgent.getFkStaffId());
        appAgentAddDto.setName(appAgent.getName());
        appAgentAddDto.setFkAreaCountryId(appAgent.getFkAreaCountryId());
        appAgentAddDto.setFkAreaStateId(appAgent.getFkAreaStateId());
        appAgentAddDto.setFkAreaCityId(appAgent.getFkAreaCityId());
        appAgentAddDto.setAppAgentContactPersonAddVos(appAgentRenewalUpdateDto.getAppAgentContactPersonAddVos());

        // 创建AddAppAgentContext对象
        AddAppAgentContext addAppAgentContext = new AddAppAgentContext();
        addAppAgentContext.setFkAppAgentId(appAgent.getId());
        addAppAgentContext.setAppAgentAddVo(appAgentAddDto);

        return addAppAgentContext;
    }

    /**
     * 添加提醒任务
     *
     * @param addAppAgentContext
     */
    private void addReminderTask(AddAppAgentContext addAppAgentContext) {
        AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();
        Long fkAppAgentId = addAppAgentContext.getFkAppAgentId();
        // 获取中英文配置
        Map<Long, String> versionConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(appAgentAddDto.getFkCompanyId());
        Set<Long> staffIds = new HashSet<>();
        staffIds.add(appAgentAddDto.getFkStaffId());
        Map<Long, String> map1 = permissionCenterClient.getStaffEnNameByIds(staffIds);
        String nameEn = map1.get(appAgentAddDto.getFkStaffId());
        if (GeneralTool.isEmpty(appAgentAddDto.getId())) {
            StringBuilder taskTitle = null;
            String taskRemark = null;
            // 加提醒任务
            Map<String, String> map = new HashMap<>();
            if (versionValue2.equals("en")) {
                taskTitle = new StringBuilder("Proxy online application");
                map.put("staffName", nameEn);
                map.put("other", "，The online application form has been submitted, please refer to it.");
                map.put("agentName", appAgentAddDto.getName());
                taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER_ENGLISH);
            } else {
                taskTitle = new StringBuilder("代理在线申请");
                String staffName = permissionCenterClient.getStaffName(appAgentAddDto.getFkStaffId()).getData();
                map.put("staffName", staffName);
                map.put("other", "，已经提交在线申请表单，请查阅。");
                map.put("agentName", appAgentAddDto.getName());
                taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER);
            }
            taskTitle.append("（").append(appAgentAddDto.getName()).append("）");

            RemindTaskDto remindTaskVo = new RemindTaskDto();
            remindTaskVo.setTaskTitle(taskTitle.toString());
            remindTaskVo.setTaskRemark(taskRemark);
            // 邮件方式发送
            remindTaskVo.setRemindMethod("1");
            // 默认设置执行中
            remindTaskVo.setStatus(1);
            // 默认背景颜色
            remindTaskVo.setTaskBgColor("#3788d8");
            remindTaskVo.setFkStaffId(appAgentAddDto.getFkStaffId());
            remindTaskVo.setStartTime(new Date());
            remindTaskVo.setFkTableName(TableEnum.APP_AGENT.key);
            remindTaskVo.setFkTableId(fkAppAgentId);
            remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.APP_AGENT_ADD_NOTICE.key);
            if (versionValue2.equals("en")) {
                remindTaskVo.setLanguageCode("en");
            }
            Result<Boolean> result = reminderCenterClient.batchAdd(Lists.newArrayList(remindTaskVo));
            if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
            }
        }

        List<AppAgentContactPersonAddDto> appAgentContactPersonAddDtos = addAppAgentContext.getAppAgentAddVo()
                .getAppAgentContactPersonAddVos();

        if (GeneralTool.isNotEmpty(appAgentContactPersonAddDtos)) {
            if (GeneralTool.isNotEmpty(appAgentContactPersonAddDtos.get(0))
                    && GeneralTool.isNotEmpty(appAgentContactPersonAddDtos.get(0).getEmail())) {
                Map<String, String> params = Maps.newHashMap();
                params.put("email", appAgentContactPersonAddDtos.get(0).getEmail());
                params.put("taskRemark",
                        getTaskRemarkToContactPerson(fkAppAgentId, appAgentAddDto.getFkCompanyId(), versionValue2));
                if (versionValue2.equals("en")) {
                    params.put("title", "Online form successfully submitted");
                    params.put("taskTitle", "Dear Partner, Hello！");
                    reminderCenterClient.batchSendEnEmail(Lists.newArrayList(params),
                            ProjectKeyEnum.APP_AGENT_CONTACT_PERSON_NOTICE.key, versionValue2);
                } else {
                    params.put("title", "在线表单成功提交");
                    params.put("taskTitle", "尊敬的合作方，您好！");
                    reminderCenterClient.batchSendEmail(Lists.newArrayList(params),
                            ProjectKeyEnum.APP_AGENT_CONTACT_PERSON_NOTICE.key);
                }

            }
        }

    }

    /**
     * 提醒内容构建
     *
     * @param fkAppAgentId
     * @return
     */
    private String getTaskRemarkToContactPerson(Long fkAppAgentId, Long fkCompanyId, String versionValue2) {
        // String link =
        // ApplyAgentOnlineFormUtils.getAppAgentFormDetailLink(fkAppAgentId,
        // fkCompanyId);
        String encrypt = null;
        try {
            encrypt = AESUtils.Encrypt(String.valueOf(fkAppAgentId), AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encryption_failed"));
        }
        String link = "";
        // 根据公司获取域名
        // Map<Long, String> companyConfigMap =
        // permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key,
        // 3).getData();
        // String configValue2 = companyConfigMap.get(fkCompanyId);
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
        String configValue3 = configVo.getValue3();

        String taskRemark = null;
        link = configValue3 + "/apply-agent-online-form/?sign=" + encrypt;
        System.out.println("=============" + link);
        if (versionValue2.equals("zh")) {
            taskRemark = "<div class=\"desc\">\n" +
                    "    <div>您已经使用在线表单成功提交了合作申请。在人工审核前，若内容有所变动或需要补充，您可以点击或复制以下链接在浏览器重新打开申请资料进行修改：</div>\n" +
                    "    <a style='display:inline-block;margin-top:0' href = \"" + link + "\">" + link + "</a>\n" +
                    "    <div>谢谢您的申请，我们会尽快审核，期待与您的合作。</div>\n" +
                    "</div>";
        } else {

            taskRemark = Base64.getEncoder().encodeToString(("<div class=\"desc\">\n" +
                    "    <div>You have successfully submitted a partnership application using the online form. Before manual review, if the content has changed or needs to be supplemented, you can click or copy the following link to reopen the application in your browser for modification: </div>\n"
                    +
                    "    <a style='display:inline-block;margin-top:0' href = \"" + link + "\">" + link + "</a>\n" +
                    "    <div>Thank you for your application, we will review it as soon as possible, and look forward to working with you.</div>\n"
                    +
                    "</div>").getBytes(StandardCharsets.UTF_8));

        }

        return taskRemark;

    }

    /**
     * 列表
     *
     * @param appAgentListDto
     * @param page
     * @return
     */
    @Override
    public List<AppAgentListVo> getAppAgents(AppAgentListDto appAgentListDto, Page page) {
        // 查询列表数据
        List<AppAgentListVo> appAgentListVos = doGetAppAgentListDtos(appAgentListDto, page);

        // 设置属性
        doSetAppAgentListDtoName(appAgentListVos);

        return appAgentListVos;
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public AppAgentVo findAppAgentById(Long id) {
        // 查询详情
        AppAgentVo appAgentVo = doGetAppAgentDto(id);

        // 设置属性
        doSetAppAgentDto(appAgentVo);

        return appAgentVo;
    }

    /**
     * 编辑
     *
     * @param appAgentUpdateDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AppAgentVo updateAppAgent(AppAgentUpdateDto appAgentUpdateDto) {
        AppAgent appAgent = BeanCopyUtils.objClone(appAgentUpdateDto, AppAgent::new);
        utilService.setUpdateInfo(appAgent);
        int i = appAgentMapper.updateById(appAgent);
        if (i != 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        // 保存附件
        doSaveMediaAndAttacheds(appAgentUpdateDto, appAgent);

        return findAppAgentById(appAgent.getId());
    }

    /**
     * 保存附件
     *
     * @param appAgentUpdateDto
     * @param appAgent
     */
    private void doSaveMediaAndAttacheds(AppAgentUpdateDto appAgentUpdateDto, AppAgent appAgent) {
        String nature = appAgentUpdateDto.getNature();
        List<MediaAndAttachedDto> mediaAttachedVos = Lists.newArrayList();
        if ("1".equals(nature)) {
            MediaAndAttachedDto mediaAndAttachedDto = appAgentUpdateDto.getMediaAndAttachedVo();
            if (mediaAndAttachedDto != null) {
                mediaAndAttachedDto.setFkTableId(appAgent.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);
                mediaAndAttachedDto.setTypeKey(TableEnum.BUSINESS_LICENSE.key);
                mediaAttachedVos.add(mediaAndAttachedDto);
            }
        }
        List<AgentIdCardDto> cardVo = appAgentUpdateDto.getAgentIdCardVos();
        if (GeneralTool.isNotEmpty(cardVo)) {
            for (AgentIdCardDto idCardVo : cardVo) {
                Integer type = idCardVo.getType();
                String value = ProjectExtraEnum.getInitialValueByKey(type, ProjectExtraEnum.idCartFB);
                if (StringUtils.isBlank(value)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("ILLEGAL_FILE_TYPE"));
                }
                MediaAndAttachedDto attachedVo = idCardVo.getMediaAndAttachedVo();
                attachedVo.setFkTableId(appAgentUpdateDto.getId());
                attachedVo.setFkTableName(TableEnum.APP_AGENT.key);
                if (type == 0) {
                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_FRONT.key);
                } else {
                    attachedVo.setTypeKey(FileTypeEnum.SALE_AGENT_ID_CARD_BACK.key);
                }
                mediaAttachedVos.add(attachedVo);
            }
        }
        if (GeneralTool.isNotEmpty(mediaAttachedVos)) {
            Boolean aBoolean = mediaAndAttachedService.saveBatchMediaAndAttached(mediaAttachedVos);
            if (!aBoolean) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    /**
     * 联系人列表
     *
     * @param appAgentContactPersonListDto
     * @param page
     * @return
     */
    @Override
    public List<AppAgentContactPersonListVo> getAppAgentContactPersons(
            AppAgentContactPersonListDto appAgentContactPersonListDto, Page page) {
        // 根据条件查询列表数据
        List<AppAgentContactPersonListVo> appAgentContactPersonListVos = doGetAppAgentContactPersonListDtos(
                appAgentContactPersonListDto, page);

        // 设置属性
        doSetAppAgentContactPersonListDto(appAgentContactPersonListVos);

        return appAgentContactPersonListVos;
    }

    /**
     * 合同账户列表
     *
     * @param appAgentContractAccountListDto
     * @param page
     * @return
     */
    @Override
    public List<AppAgentContractAccountListVo> getAppAgentContractAccounts(
            AppAgentContractAccountListDto appAgentContractAccountListDto, Page page) {
        // 列表数据
        List<AppAgentContractAccountListVo> appAgentContractAccountListVos = doGetAppAgentContractAccountListDtos(
                appAgentContractAccountListDto, page);

        // 设置属性
        doSetAppAgentContractAccountListDtos(appAgentContractAccountListVos);

        return appAgentContractAccountListVos;
    }

    /**
     * 附件列表
     *
     * @param mediaAndAttachedDto
     * @param voSearchBean
     * @return
     */
    @Override
    public List<MediaAndAttachedVo> getAppAgentMedia(MediaAndAttachedDto mediaAndAttachedDto, Page voSearchBean) {
        if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);
        return mediaAndAttachedService.getMediaAndAttachedDto(mediaAndAttachedDto);
    }

    /**
     * 添加附件
     *
     * @param mediaAttachedVos
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MediaAndAttachedVo> addAgentMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        Long agentId = mediaAttachedVos.get(0).getFkTableId();
        AppAgent appAgent = this.baseMapper.selectById(agentId);
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            if (TableEnum.BUSINESS_LICENSE.key.equals(mediaAndAttachedDto.getTypeKey()) && appAgent != null
                    && !"1".equals(appAgent.getNature())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("NON_COMPANY_AGENT_CANNOT_UPLOAD_LICENSE"));
            }
            // 设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);
            mediaAndAttachedVos.add(mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    /**
     * 更新申请状态
     *
     * @param id
     * @param appStatus
     */
    @Override
    public void updateAppStatus(Long id, Integer appStatus) {
        // 查询更新的实体
        AppAgentVo appAgentVo = doGetAppAgentDto(id);

        // 更新状态
        doUpdateAppStatus(appAgentVo, appStatus);
    }

    /**
     * 申请状态下拉
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getAppStatusSelect() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.APP_STATUS_TYPE);
    }

    /**
     * 验证联系人和代理账户
     *
     * @param id
     * @return
     */
    @Override
    public String validateAgentContactPersonAndAccount(Long id) {
        Set<String> mobiles = Sets.newHashSet();
        Set<String> mails = Sets.newHashSet();
        Set<String> accountNames = Sets.newHashSet();
        Set<String> accountNums = Sets.newHashSet();
        List<AppAgentContactPerson> appAgentContactPeople = appAgentContactPersonService
                .list(Wrappers.lambdaQuery(AppAgentContactPerson.class).eq(AppAgentContactPerson::getFkAppAgentId, id));
        if (GeneralTool.isNotEmpty(appAgentContactPeople)) {
            mobiles = appAgentContactPeople.stream().map(AppAgentContactPerson::getMobile).collect(Collectors.toSet());
            mails = appAgentContactPeople.stream().map(AppAgentContactPerson::getEmail).collect(Collectors.toSet());
        }

        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService.list(
                Wrappers.lambdaQuery(AppAgentContractAccount.class).eq(AppAgentContractAccount::getFkAppAgentId, id));
        if (GeneralTool.isNotEmpty(appAgentContractAccounts)) {
            // 名称
            accountNames = appAgentContractAccounts.stream().map(AppAgentContractAccount::getBankAccount)
                    .collect(Collectors.toSet());
            // 账户
            accountNums = appAgentContractAccounts.stream().map(AppAgentContractAccount::getBankAccountNum)
                    .collect(Collectors.toSet());
        }

        // contact_person_mobile=联系人移动电话
        // contact_person_tel=联系人固话
        // contact_person_email=联系人电邮
        // bank_account_name=账户名称
        // bank_account_num=账号
        // STUDENT_EXIST
        StringBuilder sb = new StringBuilder();
        if (GeneralTool.isNotEmpty(mobiles)) {
            List<SaleContactPerson> mobileList = contactPersonService.list(Wrappers.lambdaQuery(SaleContactPerson.class)
                    .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)
                    .in(SaleContactPerson::getMobile, mobiles));
            if (GeneralTool.isNotEmpty(mobileList)) {
                StringJoiner sj = new StringJoiner(",");
                for (SaleContactPerson saleContactPerson : mobileList) {
                    sj.add(saleContactPerson.getMobile());
                }
                sb.append(LocaleMessageUtils.getMessage("contact_person_mobile")).append(":").append(sj.toString())
                        .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("；<br/>");
            }
        }
        if (GeneralTool.isNotEmpty(mobiles)) {
            List<SaleContactPerson> tels = contactPersonService.list(Wrappers.lambdaQuery(SaleContactPerson.class)
                    .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)
                    .in(SaleContactPerson::getTel, mobiles));
            if (GeneralTool.isNotEmpty(tels)) {
                StringJoiner sj = new StringJoiner(",");
                for (SaleContactPerson saleContactPerson : tels) {
                    sj.add(saleContactPerson.getTel());
                }
                sb.append(LocaleMessageUtils.getMessage("contact_person_tel")).append(":").append(sj.toString())
                        .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("；<br/>");
            }
        }
        if (GeneralTool.isNotEmpty(mails)) {
            List<SaleContactPerson> mailList = contactPersonService.list(Wrappers.lambdaQuery(SaleContactPerson.class)
                    .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)
                    .in(SaleContactPerson::getEmail, mails));
            if (GeneralTool.isNotEmpty(mailList)) {
                StringJoiner sj = new StringJoiner(",");
                for (SaleContactPerson saleContactPerson : mailList) {
                    sj.add(saleContactPerson.getEmail());
                }
                sb.append(LocaleMessageUtils.getMessage("contact_person_email")).append(":").append(sj.toString())
                        .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("；<br/>");
            }
        }

        if (GeneralTool.isNotEmpty(accountNames)) {
            List<AgentContractAccount> accountNameList = agentContractAccountService.list(Wrappers
                    .lambdaQuery(AgentContractAccount.class).in(AgentContractAccount::getBankAccount, accountNames));
            if (GeneralTool.isNotEmpty(accountNameList)) {
                StringJoiner sj = new StringJoiner(",");
                for (AgentContractAccount agentContractAccount : accountNameList) {
                    sj.add(agentContractAccount.getBankAccount());
                }
                sb.append(LocaleMessageUtils.getMessage("bank_account_name")).append(":").append(sj.toString())
                        .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("；<br/>");
            }
        }

        if (GeneralTool.isNotEmpty(accountNums)) {
            List<AgentContractAccount> accountNumList = agentContractAccountService.list(Wrappers
                    .lambdaQuery(AgentContractAccount.class).in(AgentContractAccount::getBankAccountNum, accountNums));
            if (GeneralTool.isNotEmpty(accountNumList)) {
                StringJoiner sj = new StringJoiner(",");
                for (AgentContractAccount agentContractAccount : accountNumList) {
                    sj.add(agentContractAccount.getBankAccountNum());
                }
                sb.append(LocaleMessageUtils.getMessage("bank_account_num")).append(":").append(sj.toString())
                        .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("；<br/>");
            }
        }

        if (GeneralTool.isNotEmpty(sb)) {
            return sb.toString();
        }
        return null;
    }

    @Override
    public List<BaseSelectEntity> getBdStaffSelect(Long companyId) {
        return appAgentMapper.getBdStaffSelect(companyId);
    }

    /**
     * 表单配置
     *
     * @return
     */
    @Override
    public JSONObject getAppAgentFormConfig() {
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.APPLY_AGENT_ONLINE_FORM_LIMIT.key)
                .getData();
        JSONObject jsonObject = JSONObject.parseObject(configVo.getValue1());
        return jsonObject;
    }

    /**
     * 代理申请表单回显
     *
     * @param sign
     * @return
     */
    @Override
    public AppAgentFormDetailVo getAppAgentFormDetail(String sign) {
        // 转义+变成空格的情况
        sign = sign.replaceAll(" ", "+");
        String decrypt = null;
        try {
            decrypt = AESUtils.Decrypt(sign, AESConstant.AESKEY);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("SIGN_PARSE_FAILED"));
        }
        Long id = Long.valueOf(decrypt);
        AppAgentFormDetailVo appAgentFormDetailVo = getAppAgentFormDetailById(id);

        return appAgentFormDetailVo;
    }

    /**
     * 根据代理申请id获取表单数据
     *
     * @param id
     * @return
     */
    @Override
    public AppAgentFormDetailVo getAppAgentFormDetailById(Long id) {
        if (ObjectUtils.isNull(id)) {
            return null;
        }
        AppAgent appAgent = appAgentMapper.selectById(id);
        if (GeneralTool.isEmpty(appAgent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_exist"));
        }

        AppAgentFormDetailVo appAgentFormDetailVo = BeanCopyUtils.objClone(appAgent, AppAgentFormDetailVo::new);
        assert appAgentFormDetailVo != null;

        if (appAgent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key))) {
            appAgentFormDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key);
            appAgentFormDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);
        }
        if (appAgent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key))) {
            appAgentFormDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_COMPANY.key);
            appAgentFormDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);
        }
        if (appAgent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key))) {
            appAgentFormDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_PERSONAL.key);
        }
        MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();
        mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);
        mediaAndAttachedDto.setFkTableId(appAgent.getId());
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedService
                .getMediaAndAttachedDto(mediaAndAttachedDto);
        if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
            List<MediaAndAttachedDto> mediaAndAttachedDtos = BeanCopyUtils.copyListProperties(mediaAndAttachedVos,
                    MediaAndAttachedDto::new);
            appAgentFormDetailVo.setMediaAndAttachedVos(mediaAndAttachedDtos);
        }

        List<AppAgentContactPerson> appAgentContactPersonList = appAgentContactPersonService
                .list(Wrappers.lambdaQuery(AppAgentContactPerson.class)
                        .eq(AppAgentContactPerson::getFkAppAgentId, appAgent.getId()));

        if (GeneralTool.isNotEmpty(appAgentContactPersonList)) {
            List<AppAgentContactPersonAddDto> appAgentContactPersonAddDtos = BeanCopyUtils
                    .copyListProperties(appAgentContactPersonList, AppAgentContactPersonAddDto::new);
            appAgentFormDetailVo.setAppAgentContactPersonAddVos(appAgentContactPersonAddDtos);
        } else {
            appAgentFormDetailVo.setAppAgentContactPersonAddVos(Collections.emptyList());
        }

        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService
                .list(Wrappers.lambdaQuery(AppAgentContractAccount.class)
                        .eq(AppAgentContractAccount::getFkAppAgentId, appAgent.getId()));

        if (GeneralTool.isNotEmpty(appAgentContractAccounts)) {
            List<AppAgentContractAccountAddDto> appAgentContractAccountAddDtos = BeanCopyUtils
                    .copyListProperties(appAgentContractAccounts, AppAgentContractAccountAddDto::new);
            if (GeneralTool.isNotEmpty(appAgentContractAccountAddDtos)) {
                Set<Long> fkTableIds = appAgentContractAccountAddDtos.stream().map(AppAgentContractAccountAddDto::getId)
                        .collect(Collectors.toSet());
                Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService
                        .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key, fkTableIds);
                for (AppAgentContractAccountAddDto appAgentContractAccountAddDto : appAgentContractAccountAddDtos) {
                    List<MediaAndAttachedVo> mediaAndAttachedVoList = mediaAndAttachedDtoByFkTableIds
                            .get(appAgentContractAccountAddDto.getId());
                    if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {
                        appAgentContractAccountAddDto.setMediaAndAttachedVos(
                                BeanCopyUtils.copyListProperties(mediaAndAttachedVoList, MediaAndAttachedDto::new));
                    }
                }
            }
            appAgentFormDetailVo.setAppAgentContractAccountAddVos(appAgentContractAccountAddDtos);
        } else {
            appAgentFormDetailVo.setAppAgentContractAccountAddVos(Collections.emptyList());
        }

        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)
                    || profile.equals(AppConstant.TW_CODE)) {
                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);
            } else if (profile.equals(AppConstant.IAE_CODE)) {
                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);// IAE环境下使用私密桶地址为IAE桶url
            } else if (profile.equals(AppConstant.TEST_CODE)) {
                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);
            } else {
                appAgentFormDetailVo.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);
            }
        }
        Long fkAgentId = appAgent.getFkAgentId();
        if (ObjectUtils.isNotNull(fkAgentId)) {
            LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()
                    .eq(AgentContract::getFkAgentId, fkAgentId)
                    .eq(AgentContract::getIsActive, true)
                    .orderByDesc(AgentContract::getGmtCreate)
                    .last("LIMIT 1");
            AgentContract agentContract = this.agentContractService.getOne(agentContractLambdaQueryWrapper);
            if (ObjectUtils.isNotNull(agentContract)) {
                appAgentFormDetailVo.setContractNum(agentContract.getContractNum());
            }
        }
        if (StringUtils.isBlank(appAgentFormDetailVo.getPersonalName())) {
            appAgentFormDetailVo.setPersonalName(appAgentFormDetailVo.getName());
        }
        return appAgentFormDetailVo;
    }

    /**
     * 常用国家下拉框
     *
     * @return
     */
    @Override
    public List<BaseSelectEntity> getCommonCountrySelect() {
        List<AreaCountryVo> areaCountryVos = institutionCenterClient
                .getCountryByPublicLevel(ProjectExtraEnum.PUBLIC_COUNTRY_COMMON.key);
        if (GeneralTool.isEmpty(areaCountryVos)) {
            return Collections.emptyList();
        }
        List<BaseSelectEntity> baseSelectEntities = BeanCopyUtils.copyListProperties(areaCountryVos,
                BaseSelectEntity::new);
        return baseSelectEntities;
    }

    @Override
    public void getDownloadFilePrivate(HttpServletResponse response, FileVo fileVo) {
        SaleFileDto saleFileDto = fileCenterClient.getDownloadFile(fileVo).getData();
        BufferedOutputStream outputStream = null;
        try {
            outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setHeader("Content-Disposition", "attachment;filename="
                    + URLEncoder.encode(new String(fileVo.getFileNameOrc().getBytes("utf-8"), "UTF-8")));
            byte[] bytes = null;
            bytes = saleFileDto.getBytes();
            outputStream.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 奖学金用户是否已申请
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean validatedUser(Long userId) {
        List<AppAgent> appAgents = appAgentMapper.selectList(Wrappers.lambdaQuery(AppAgent.class)
                .eq(AppAgent::getGmtCreateUser, "get_issue_" + userId));
        return GeneralTool.isNotEmpty(appAgents);
    }

    /**
     * 更新申请状态
     *
     * @param appAgentVo
     * @param appStatus
     */
    private void doUpdateAppStatus(AppAgentVo appAgentVo, Integer appStatus) {
        if (GeneralTool.isEmpty(appStatus) || GeneralTool.isEmpty(appAgentVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(appStatus)) {
            // 我要审核
            doSetReview(appAgentVo);
        }
        if (ProjectExtraEnum.APP_STATUS_AGREE.key.equals(appStatus)) {
            // 设置同意
            doSetAgree(appAgentVo);
        }
        if (ProjectExtraEnum.APP_STATUS_REJECT.key.equals(appStatus)) {
            // 设置拒绝
            doSetReject(appAgentVo);
        }
    }

    /**
     * 审批同意
     *
     * @param appAgentVo
     */
    // @Transactional(rollbackFor = Exception.class)
    // @DSTransactional
    @Override
    @GlobalTransactional
    public void doSetAgree(AppAgentVo appAgentVo) {

        // 代理查重
        validateAgent(appAgentVo);

        // 同步代理数据
        AppAgentSetAgreeContext appAgentSetAgreeContext = doCopyAppAgentToAgent(appAgentVo);

        // 同步代理绑定bd数据
        doCopyAppAgentBdToAgentBd(appAgentSetAgreeContext);

        // 生成合同
        doCreateAgentContract(appAgentSetAgreeContext);

        // 同步联系人数据
        doCopyAppAgentContactPersonToAgentContactPerson(appAgentSetAgreeContext);

        // 同步合同账户资料
        doCopyAppAgentContractAccountToAgentContractAccount(appAgentSetAgreeContext);

        // 保存合同联系人类型
        // doCreateContractPerson(appAgentSetAgreeContext);

        // 同步附件
        doCopyMediaAndAttached(appAgentSetAgreeContext);

        // 回填代理id
        doUpdateAppAgentInfo(appAgentSetAgreeContext);

        // 若是奖学金进来的，要在issue库新增关系表和更新issue学生代理id
        // doUpdateIssueStudentAndAgent(appAgentSetAgreeContext);

        // 注册伙伴中心用户
        doRegisterPartnerUsers(appAgentSetAgreeContext);

    }

    /**
     * 保存合同账户类型
     *
     * @param appAgentSetAgreeContext
     */
    private void doCreateContractAccount(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        List<AppAgentContractAccount> appAgentContractAccounts = appAgentSetAgreeContext.getAppAgentContractAccounts();
        if (CollectionUtil.isEmpty(appAgentContractAccounts)) {
            return;
        }
        // this.agentContractAgentAccountService.list(new
        // LambdaQueryWrapper<AgentContractAgentAccount>().eq(AgentContractAgentAccount
        // :: getFkAgentContractAccountId));

        // appAgentContractAccounts.stream().map(appAgentContractAccount -> {
        // AgentContractAgentAccount agentAccount = new AgentContractAgentAccount();
        // })

    }

    /**
     * 保存合同联系人类型
     *
     * @param appAgentSetAgreeContext
     */
    private void doCreateContractPerson(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        Long agentId = appAgentSetAgreeContext.getAgentId();
        if (ObjectUtils.isEmpty(agentId)) {
            return;
        }
        List<AppAgentContactPerson> appAgentContactPersons = appAgentSetAgreeContext.getAppAgentContactPersons();
        if (CollectionUtil.isEmpty(appAgentContactPersons)) {
            return;
        }
        List<SaleContactPerson> saleContactPersonList = appAgentContactPersons.stream()
                .map(appAgentContactPerson -> BeanCopyUtils.objClone(appAgentContactPerson, SaleContactPerson::new))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(saleContactPersonList)) {
            return;
        }
        for (SaleContactPerson saleContactPerson : saleContactPersonList) {
            saleContactPerson.setFkTableName(TableEnum.SALE_AGENT.key);
            // 代理管理id
            saleContactPerson.setFkTableId(agentId);
        }
        // 保存合同联系人数据
        boolean result = this.contactPersonService.saveBatch(saleContactPersonList);
    }

    /**
     * 若是奖学金进来的，要在issue库新增关系表和更新issue学生代理id
     *
     * @param appAgentSetAgreeContext
     */
    private void doUpdateIssueStudentAndAgent(AppAgentSetAgreeContext appAgentSetAgreeContext) {

        String gmtCreateUser = appAgentSetAgreeContext.getAppAgentDto().getGmtCreateUser();
        if (!gmtCreateUser.startsWith("get_issue_")) {
            return;
        }

        String userIdStr = gmtCreateUser.substring(10);
        Long userId;
        try {
            userId = Long.parseLong(userIdStr);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_type"));
        }
        Long agentId = appAgentSetAgreeContext.getAgentId();
        if (appAgentSetAgreeContext.getIsRenewal()) {
            LambdaQueryWrapper<NewIssueUserAgent> newIssueUserAgentLambdaQueryWrapper = new LambdaQueryWrapper<NewIssueUserAgent>()
                    .eq(NewIssueUserAgent::getFkAgentId, agentId);
            List<NewIssueUserAgent> newIssueUserAgents = newIssueUserAgentMapper
                    .selectList(newIssueUserAgentLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(newIssueUserAgents)) {
                this.newIssueUserAgentMapper.delete(newIssueUserAgentLambdaQueryWrapper);
            }
        }
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();

        NewIssueUserAgent newIssueUserAgent = new NewIssueUserAgent();
        newIssueUserAgent.setFkCompanyId(appAgentVo.getFkCompanyId());
        newIssueUserAgent.setFkAgentId(agentId);
        newIssueUserAgent.setFkUserId(userId);
        newIssueUserAgent.setRoleType(0);
        newIssueUserAgent.setIsSubagentPermission(false);
        newIssueUserAgent.setIsBmsPermission(true);
        newIssueUserAgent.setIsShowAppStatusPermission(true);
        utilService.setCreateInfo(newIssueUserAgent);
        int i = newIssueUserAgentMapper.insert(newIssueUserAgent);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

        User user = registrationCenterClient.getUserByAgentId(agentId).getData();
        NewIssueUserSuperior newIssueUserSuperior = new NewIssueUserSuperior();
        newIssueUserSuperior.setFkUserId(userId);
        newIssueUserSuperior.setFkUserSuperiorId(user.getId());
        newIssueUserSuperior.setFkCompanyId(appAgentSetAgreeContext.getFkCompanyId());
        utilService.setCreateInfo(newIssueUserSuperior);
        newIssueUserSuperiorMapper.insert(newIssueUserSuperior);

        List<NewIssueStudent> newIssueStudents = newIssueStudentMapper
                .selectList(Wrappers.lambdaQuery(NewIssueStudent.class)
                        .eq(NewIssueStudent::getGmtCreateUserId, userId));

        if (GeneralTool.isNotEmpty(newIssueStudents)) {
            for (NewIssueStudent newIssueStudent : newIssueStudents) {
                newIssueStudent.setFkAgentId(agentId);
                utilService.setUpdateInfo(newIssueStudent);
                newIssueStudentMapper.updateById(newIssueStudent);
            }

            List<Long> issueStudentIds = newIssueStudents.stream().map(NewIssueStudent::getId)
                    .collect(Collectors.toList());
            List<RStudentIssueStudent> rStudentIssueStudents = rStudentIssueStudentMapper
                    .selectList(Wrappers.lambdaQuery(RStudentIssueStudent.class)
                            .in(RStudentIssueStudent::getFkStudentIdIssue2, issueStudentIds));

            if (GeneralTool.isNotEmpty(rStudentIssueStudents)) {
                Set<Long> studentIds = rStudentIssueStudents.stream().map(RStudentIssueStudent::getFkStudentId)
                        .collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(studentIds)) {
                    StudentAgentBindingDto studentAgentBindingDto = new StudentAgentBindingDto();
                    studentAgentBindingDto.setFkAgentId(agentId);
                    studentAgentBindingDto.setStudentOfferAgentId(agentId);
                    studentAgentBindingDto.setAccommodationAgentId(agentId);
                    studentAgentBindingDto.setInsuranceAgentId(agentId);
                    studentAgentBindingDto.setFkStudentIds(studentIds);
                    studentAgentBindingDto.setIsCancelOriginalBinding(true);
                    if (GeneralTool.isNotEmpty(appAgentSetAgreeContext.getAppAgentContactPersons())) {
                        AppAgentContactPerson appAgentContactPerson = appAgentSetAgreeContext
                                .getAppAgentContactPersons().get(0);
                        studentAgentBindingDto.setStudentOfferEmail(appAgentContactPerson.getEmail());
                    }
                    studentService.updateStudentAgentBinding(studentAgentBindingDto);
                }
            }
        }

    }

    /**
     * 同步bd绑定数据
     *
     * @param appAgentSetAgreeContext
     */
    private void doCopyAppAgentBdToAgentBd(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        Long fkAgentId = appAgentSetAgreeContext.getAgentId();
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
        if (GeneralTool.isEmpty(fkAgentId) && GeneralTool.isEmpty(appAgentVo)) {
            return;
        }
        if (appAgentSetAgreeContext.getIsRenewal()) {
            LambdaQueryWrapper<AgentStaff> staffLambdaQueryWrapper = new LambdaQueryWrapper<AgentStaff>()
                    .eq(AgentStaff::getFkAgentId, fkAgentId);
            List<AgentStaff> agentStaffs = this.agentStaffService.list(staffLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(agentStaffs)) {
                List<Long> staffIds = agentStaffs.stream().map(AgentStaff::getId).collect(Collectors.toList());
                this.agentStaffService.removeByIds(staffIds);
            }
        }

        AgentStaff agentStaff = new AgentStaff();
        agentStaff.setFkAgentId(fkAgentId);
        agentStaff.setFkStaffId(appAgentVo.getFkStaffId());
        agentStaff.setIsActive(true);
        agentStaff.setActiveDate(new Date());
        utilService.setCreateInfo(agentStaff);
        int i = agentStaffService.getBaseMapper().insert(agentStaff);
        if (i != 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 代理查重
     *
     * @param appAgentVo
     */
    private void validateAgent(AppAgentVo appAgentVo) {
        if (GeneralTool.isEmpty(appAgentVo.getAppStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        // if (GeneralTool.isEmpty(appAgentVo.getEmail2())){
        // throw new GetServiceException(("en".equals(SecureUtil.getLocale())?"Business
        // news receiving
        // mail,":"业务新闻接收邮箱,")+LocaleMessageUtils.getMessage("parameter_missing"));
        // }
        if (!ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(appAgentVo.getAppStatus())) {
            // 如果不为申请中状态了 则不能设置成同意
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        if (!Objects.equals(appAgentVo.getAppStatusModifyUser(), SecureUtil.getLoginId())) {
            // 登录人和申请审批的人不是同一个 则不能设置同意
            throw new GetServiceException(LocaleMessageUtils.getMessage("NON_REVIEWER"));
        }
        // 是否续约
        Boolean isRenewal = AgentAppTypeEnum.RENEWAL_APPLICATION.getCode().equals(appAgentVo.getAppType())
                && appAgentVo.getFkAgentId() != null;
        // 代理申请id
        Long fkAgentId = appAgentVo.getFkAgentId();

        LambdaQueryWrapper<Agent> wrapper = Wrappers.<Agent>lambdaQuery();
        if (GeneralTool.isEmpty(appAgentVo.getNature())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("nature_is_empty"));
        }
        wrapper.eq(Agent::getNature, appAgentVo.getNature());
        // 性质=个人 + 身份证（禁止提交）
        if (String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key).equals(appAgentVo.getNature())) {
            if (GeneralTool.isEmpty(appAgentVo.getIdCardNum())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_card_num_is_empty"));
            }
            wrapper.eq(Agent::getIdCardNum, appAgentVo.getIdCardNum());
            List<Agent> agents = agentService.list(wrapper);
            // 续签过滤自己
            if (isRenewal) {
                agents = agents.stream().filter(agent -> !fkAgentId.equals(agent.getId())).collect(Collectors.toList());
            }
            if (GeneralTool.isNotEmpty(agents)) {
                Set<Long> ids = agents.stream().map(Agent::getId).collect(Collectors.toSet());
                List<AgentCompany> agentCompanies = agentCompanyService
                        .list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids)
                                .eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));
                if (GeneralTool.isNotEmpty(agentCompanies)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("agent_person_id_card_duplicate"));
                }
            }
        }
        // 性质=公司 + 公司税号（禁止提交）
        if (String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key).equals(appAgentVo.getNature())) {
            if (GeneralTool.isEmpty(appAgentVo.getTaxCode())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("tax_code_is_empty"));
            }
            wrapper.eq(Agent::getTaxCode, appAgentVo.getTaxCode());
            List<Agent> agents = agentService.list(wrapper);
            if (isRenewal) {
                agents = agents.stream().filter(agent -> !fkAgentId.equals(agent.getId())).collect(Collectors.toList());
            }
            if (GeneralTool.isNotEmpty(agents)) {
                Set<Long> ids = agents.stream().map(Agent::getId).collect(Collectors.toSet());
                List<AgentCompany> agentCompanies = agentCompanyService
                        .list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids)
                                .eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));
                if (GeneralTool.isNotEmpty(agentCompanies)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("agent_company_tax_code_duplicate"));
                }
            }
        }
        // 性质=工作室 + 身份证号码（禁止提交）|| 性质=国际学校 + 身份证号码（禁止提交）
        if (String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key).equals(appAgentVo.getNature()) || String
                .valueOf(ProjectExtraEnum.AGENT_NATURE_INTERNATIONAL_SCHOOL.key).equals(appAgentVo.getNature())) {
            if (GeneralTool.isEmpty(appAgentVo.getIdCardNum())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("id_card_num_is_empty"));
            }
            wrapper.eq(Agent::getIdCardNum, appAgentVo.getIdCardNum());
            List<Agent> agents = agentService.list(wrapper);
            if (isRenewal) {
                agents = agents.stream().filter(agent -> !fkAgentId.equals(agent.getId())).collect(Collectors.toList());
            }
            if (GeneralTool.isNotEmpty(agents)) {
                Set<Long> ids = agents.stream().map(Agent::getId).collect(Collectors.toSet());
                List<AgentCompany> agentCompanies = agentCompanyService
                        .list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, ids)
                                .eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));
                if (GeneralTool.isNotEmpty(agentCompanies)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("agent_person_id_card_duplicate"));
                }
            }
        }
        // 改为不禁止
        // //性质=其他 + 代理名称（禁止提交）
        // if
        // (String.valueOf(ProjectExtraEnum.AGENT_NATURE_OTHER.key).equals(appAgentVo.getNature())){
        // if (GeneralTool.isEmpty(appAgentVo.getName())){
        // throw new GetServiceException("name missing！");
        // }
        // wrapper.eq(Agent::getName,appAgentVo.getName());
        // List<Agent> agents = agentService.list(wrapper);
        // if (GeneralTool.isNotEmpty(agents)){
        // Set<Long> ids =
        // agents.stream().map(Agent::getId).collect(Collectors.toSet());
        // List<AgentCompany> agentCompanies =
        // agentCompanyService.list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId,
        // ids).eq(AgentCompany::getFkCompanyId, appAgentVo.getFkCompanyId()));
        // if (GeneralTool.isNotEmpty(agentCompanies)){
        // throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        // }
        // }
        // }
    }

    /**
     * 回填信息
     *
     * @param appAgentSetAgreeContext
     */
    private void doUpdateAppAgentInfo(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        Long agentId = appAgentSetAgreeContext.getAgentId();
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
        AppAgent appAgent = BeanCopyUtils.objClone(appAgentVo, AppAgent::new);
        appAgent.setFkAgentId(agentId);
        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_AGREE.key);
        appAgent.setAppStatusModifyTime(new Date());
        appAgent.setAppStatusModifyUser(SecureUtil.getLoginId());
        utilService.setUpdateInfo(appAgent);
        boolean b = updateById(appAgent);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 同步合同账户资料
     *
     * @param appAgentSetAgreeContext
     */
    private void doCopyAppAgentContractAccountToAgentContractAccount(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
        List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService
                .list(Wrappers.<AppAgentContractAccount>lambdaQuery().eq(AppAgentContractAccount::getFkAppAgentId,
                        appAgentVo.getId()));
        Long agentId = appAgentSetAgreeContext.getAgentId();
        Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();

        if (isRenewal) {
            // 续约使用智能同步策略：有则更新，无则新增，多余的删除（合同账户例外：按原有逻辑只处理新增）
            doRenewalSyncContractAccountsWithUpdateStrategy(appAgentContractAccounts, agentId);
        } else {
            // 非续约保持原有逻辑
            Set<Long> appAgentContractAccountIds = appAgentContractAccounts.stream().map(AppAgentContractAccount::getId)
                    .collect(Collectors.toSet());
            Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService
                    .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key,
                            appAgentContractAccountIds);
            List<SaleMediaAndAttached> mediaAndAttachedSaveList = Lists.newArrayList();
            if (GeneralTool.isNotEmpty(appAgentContractAccounts)) {
                for (AppAgentContractAccount appAgentContractAccount : appAgentContractAccounts) {
                    AgentContractAccountDto agentContractAccountDto = BeanCopyUtils.objClone(appAgentContractAccount,
                            AgentContractAccountDto::new);
                    agentContractAccountDto.setIsActive(true);
                    agentContractAccountDto.setFkAgentId(agentId);
                    agentContractAccountDto.setGmtModified(null);
                    agentContractAccountDto.setGmtModifiedUser(null);
                    Long agentContractAccountId = agentContractAccountService
                            .addContractAccount(agentContractAccountDto);
                    List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtoByFkTableIds
                            .get(appAgentContractAccount.getId());
                    if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
                        List<SaleMediaAndAttached> saleMediaAndAttacheds = BeanCopyUtils
                                .copyListProperties(mediaAndAttachedVos, SaleMediaAndAttached::new);
                        for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {
                            saleMediaAndAttached.setId(null);
                            saleMediaAndAttached.setFkTableName(TableEnum.AGENT_CONTRACT_ACCOUNT.key);
                            saleMediaAndAttached.setFkTableId(agentContractAccountId);
                            saleMediaAndAttached.setGmtModified(null);
                            saleMediaAndAttached.setGmtModifiedUser(null);
                            utilService.setCreateInfo(saleMediaAndAttached);
                        }
                        mediaAndAttachedSaveList.addAll(saleMediaAndAttacheds);
                    }
                }
            }

            if (GeneralTool.isNotEmpty(mediaAndAttachedSaveList)) {
                boolean b = mediaAndAttachedService.saveBatch(mediaAndAttachedSaveList);
                if (!b) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }
        }

        appAgentSetAgreeContext.setAppAgentContractAccounts(appAgentContractAccounts);
    }

    /**
     * 续约合同账户智能同步策略：有则更新，无则新增，多余的删除
     *
     * @param appAgentContractAccounts AppAgent的合同账户列表
     * @param agentId                  代理ID
     */
    private void doRenewalSyncContractAccountsWithUpdateStrategy(List<AppAgentContractAccount> appAgentContractAccounts,
                                                                 Long agentId) {
        if (CollectionUtil.isEmpty(appAgentContractAccounts)) {
            return;
        }

        // 获取当前Agent下所有现有的合同账户，建立映射关系
        List<AgentContractAccount> existingContractAccounts = agentContractAccountService.list(
                Wrappers.<AgentContractAccount>lambdaQuery()
                        .eq(AgentContractAccount::getFkAgentId, agentId)
                        .eq(AgentContractAccount::getIsActive, true));
        Map<Long, AgentContractAccount> existingContractAccountMap = existingContractAccounts.stream()
                .collect(Collectors.toMap(AgentContractAccount::getId, Function.identity()));

        Set<Long> processedContractAccountIds = new HashSet<>();
        List<AgentContractAccount> toUpdateList = new ArrayList<>();
        List<AppAgentContractAccount> toInsertList = new ArrayList<>();

        for (AppAgentContractAccount appContractAccount : appAgentContractAccounts) {
            Long fkAgentContractAccountId = appContractAccount.getFkAgentContractAccountId();

            if (GeneralTool.isNotEmpty(fkAgentContractAccountId)
                    && existingContractAccountMap.containsKey(fkAgentContractAccountId)) {
                // 有关联ID且确实存在，准备更新现有的AgentContractAccount记录
                AgentContractAccount existingContractAccount = existingContractAccountMap.get(fkAgentContractAccountId);
                prepareUpdateAgentContractAccountForRenewal(appContractAccount, existingContractAccount);
                toUpdateList.add(existingContractAccount);
                processedContractAccountIds.add(fkAgentContractAccountId);
            } else {
                // 无关联ID或关联的记录不存在，准备新增AgentContractAccount记录
                toInsertList.add(appContractAccount);
            }
        }

        // 批量更新
        if (CollectionUtil.isNotEmpty(toUpdateList)) {
            boolean updateResult = agentContractAccountService.updateBatchById(toUpdateList);
            if (!updateResult) {
                log.error("续约批量更新合同账户失败");
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
            log.debug("续约批量更新合同账户成功，数量: {}", toUpdateList.size());
        }

        // 批量新增（使用原有的addContractAccount逻辑保持一致）
        List<SaleMediaAndAttached> mediaAndAttachedSaveList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(toInsertList)) {
            // 获取附件信息
            Set<Long> appAgentContractAccountIds = toInsertList.stream().map(AppAgentContractAccount::getId)
                    .collect(Collectors.toSet());
            Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByFkTableIds = mediaAndAttachedService
                    .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key,
                            appAgentContractAccountIds);

            for (AppAgentContractAccount appAgentContractAccount : toInsertList) {
                AgentContractAccountDto agentContractAccountDto = BeanCopyUtils.objClone(appAgentContractAccount,
                        AgentContractAccountDto::new);
                agentContractAccountDto.setIsActive(true);
                agentContractAccountDto.setFkAgentId(agentId);
                agentContractAccountDto.setGmtModified(null);
                agentContractAccountDto.setGmtModifiedUser(null);
                Long agentContractAccountId = agentContractAccountService.addContractAccount(agentContractAccountDto);

                // 处理附件
                List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtoByFkTableIds
                        .get(appAgentContractAccount.getId());
                if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
                    List<SaleMediaAndAttached> saleMediaAndAttacheds = BeanCopyUtils
                            .copyListProperties(mediaAndAttachedVos, SaleMediaAndAttached::new);
                    for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {
                        saleMediaAndAttached.setId(null);
                        saleMediaAndAttached.setFkTableName(TableEnum.AGENT_CONTRACT_ACCOUNT.key);
                        saleMediaAndAttached.setFkTableId(agentContractAccountId);
                        saleMediaAndAttached.setGmtModified(null);
                        saleMediaAndAttached.setGmtModifiedUser(null);
                        utilService.setCreateInfo(saleMediaAndAttached);
                    }
                    mediaAndAttachedSaveList.addAll(saleMediaAndAttacheds);
                }
            }
            log.debug("续约批量新增合同账户成功，数量: {}", toInsertList.size());
        }

        // 保存附件
        if (GeneralTool.isNotEmpty(mediaAndAttachedSaveList)) {
            boolean b = mediaAndAttachedService.saveBatch(mediaAndAttachedSaveList);
            if (!b) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }

        log.info("续约合同账户数据同步完成，更新: {}, 新增: {}", toUpdateList.size(), toInsertList.size());
    }

    /**
     * 准备更新的AgentContractAccount记录（续约专用）
     */
    private void prepareUpdateAgentContractAccountForRenewal(AppAgentContractAccount appContractAccount,
                                                             AgentContractAccount existing) {
        BeanCopyUtils.copyProperties(appContractAccount, existing, "id", "fkAgentId", "fkAgentContractAccountId");
        utilService.setUpdateInfo(existing);
    }

    /**
     * 同步附件
     *
     * @param appAgentSetAgreeContext
     */
    private void doCopyMediaAndAttached(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
        Long appAgentDtoId = appAgentVo.getId();
        List<AppAgentContractAccount> appAgentContractAccounts = appAgentSetAgreeContext.getAppAgentContractAccounts();
        Long agentId = appAgentSetAgreeContext.getAgentId();
        if (appAgentSetAgreeContext.getIsRenewal()) {
            LambdaQueryWrapper<SaleMediaAndAttached> saleMediaAndAttachedLambdaQueryWrapper = new LambdaQueryWrapper<SaleMediaAndAttached>()
                    .eq(SaleMediaAndAttached::getFkTableId, agentId)
                    .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key);
            List<SaleMediaAndAttached> saleMediaAndAttacheds = this.mediaAndAttachedService
                    .list(saleMediaAndAttachedLambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(saleMediaAndAttacheds)) {
                this.mediaAndAttachedService.remove(saleMediaAndAttachedLambdaQueryWrapper);
            }
        }

        List<SaleMediaAndAttached> saleMediaAndAttacheds = Lists.newArrayList();
        // 代理附件、合同账户附件
        saleMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.APP_AGENT.key)
                .eq(SaleMediaAndAttached::getFkTableId, appAgentDtoId));

        // if (GeneralTool.isNotEmpty(appAgentContractAccounts)){
        // Set<Long> ids =
        // appAgentContractAccounts.stream().map(AppAgentContractAccount::getId).collect(Collectors.toSet());
        // List<SaleMediaAndAttached> saleMediaAndAttachedList =
        // mediaAndAttachedService.list(Wrappers.<SaleMediaAndAttached>lambdaQuery()
        // .eq(SaleMediaAndAttached::getFkTableName,
        // TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key)
        // .in(SaleMediaAndAttached::getFkTableId, ids));
        //
        // if (GeneralTool.isNotEmpty(saleMediaAndAttachedList)){
        // saleMediaAndAttacheds.addAll(saleMediaAndAttachedList);
        // }
        // }

        if (GeneralTool.isEmpty(saleMediaAndAttacheds)) {
            return;
        }
        for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {
            saleMediaAndAttached.setId(null);
            saleMediaAndAttached.setFkTableName(TableEnum.SALE_AGENT.key);
            saleMediaAndAttached.setFkTableId(agentId);
            saleMediaAndAttached.setGmtModified(null);
            saleMediaAndAttached.setGmtModifiedUser(null);
            utilService.setCreateInfo(saleMediaAndAttached);
        }

        boolean b = mediaAndAttachedService.saveBatch(saleMediaAndAttacheds);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 同步代理联系人数据
     *
     * @param appAgentSetAgreeContext
     */
    private void doCopyAppAgentContactPersonToAgentContactPerson(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        Long fkCompanyId = appAgentSetAgreeContext.getFkCompanyId();
        Long fkAgentId = appAgentSetAgreeContext.getAgentId();
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();

        Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();
        List<AppAgentContactPerson> appAgentContactPersons = appAgentContactPersonService.list(Wrappers
                .<AppAgentContactPerson>lambdaQuery().eq(AppAgentContactPerson::getFkAppAgentId, appAgentVo.getId()));

        if (isRenewal) {
            // 续约使用智能同步策略：有则更新，无则新增，多余的删除
            doRenewalSyncContactPersonsWithUpdateStrategy(appAgentContactPersons, fkAgentId, fkCompanyId);
        } else {
            // 非续约保持原有逻辑
            if (GeneralTool.isNotEmpty(appAgentContactPersons)) {
                List<ContactPersonDto> contactPersonVos = BeanCopyUtils.copyListProperties(appAgentContactPersons,
                        ContactPersonDto::new);
                contactPersonVos.forEach(c -> {
                    c.setFkCompanyId(fkCompanyId);
                    c.setFkTableName(TableEnum.SALE_AGENT.key);
                    c.setFkTableId(fkAgentId);
                    c.setGmtModified(null);
                    c.setGmtModifiedUser(null);
                });
                for (ContactPersonDto contactPersonVo : contactPersonVos) {
                    if (StringUtils.isBlank(contactPersonVo.getFkContactPersonTypeKey())) {
                        contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
                    }
                    agentService.addAgentContactPerson(contactPersonVo);
                }
                // 创建合同联系人的时候排除紧急联系人
                List<ContactPersonDto> excludeEmergency = contactPersonVos.stream()
                        .filter(contactPersonVo -> !(contactPersonVo.getFkContactPersonTypeKey() != null
                                && contactPersonVo.getFkContactPersonTypeKey().contains(ContactPersonTypeEnum.EMERGENCY.getCode())))
                        .collect(Collectors.toList());
                for (ContactPersonDto contactPersonVo : excludeEmergency) {
                    contactPersonVo.setFkContactPersonTypeKey("CONTACT_AGENT_CONTRACT");
                    agentService.addAgentContactPerson(contactPersonVo);
                }
            }
        }
        appAgentSetAgreeContext.setAppAgentContactPersons(appAgentContactPersons);
    }

    /**
     * 续约联系人智能同步策略：有则更新，无则新增，多余的删除
     *
     * @param appAgentContactPersons AppAgent的联系人列表
     * @param agentId                代理ID
     * @param fkCompanyId            公司ID
     */
    private void doRenewalSyncContactPersonsWithUpdateStrategy(List<AppAgentContactPerson> appAgentContactPersons,
                                                               Long agentId, Long fkCompanyId) {
        if (CollectionUtil.isEmpty(appAgentContactPersons)) {
            return;
        }

        // 获取当前Agent下所有现有的联系人，建立映射关系
        List<SaleContactPerson> existingContactPersons = contactPersonService.list(
                Wrappers.<SaleContactPerson>lambdaQuery()
                        .eq(SaleContactPerson::getFkTableId, agentId));
        Map<Long, SaleContactPerson> existingContactPersonMap = existingContactPersons.stream()
                .collect(Collectors.toMap(SaleContactPerson::getId, Function.identity()));

        Set<Long> processedContactPersonIds = new HashSet<>();
        List<SaleContactPerson> toUpdateList = new ArrayList<>();
        List<ContactPersonDto> toInsertList = new ArrayList<>();

        for (AppAgentContactPerson appContactPerson : appAgentContactPersons) {
            Long fkContactPersonId = appContactPerson.getFkContactPersonId();

            if (GeneralTool.isNotEmpty(fkContactPersonId) && existingContactPersonMap.containsKey(fkContactPersonId)) {
                // 有关联ID且确实存在，准备更新现有的SaleContactPerson记录
                SaleContactPerson existingContactPerson = existingContactPersonMap.get(fkContactPersonId);
                prepareUpdateSaleContactPersonForRenewal(appContactPerson, existingContactPerson);
                toUpdateList.add(existingContactPerson);
                processedContactPersonIds.add(fkContactPersonId);
            } else {
                // 无关联ID或关联的记录不存在，准备新增SaleContactPerson记录
                ContactPersonDto newContactPersonDto = prepareNewContactPersonDtoForRenewal(appContactPerson, agentId,
                        fkCompanyId);
                toInsertList.add(newContactPersonDto);
            }
        }

        // 批量更新 + 双重创建（排除紧急联系人）
        if (CollectionUtil.isNotEmpty(toUpdateList)) {
            boolean updateResult = contactPersonService.updateBatchById(toUpdateList);
            if (!updateResult) {
                log.error("续约批量更新联系人失败");
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
            log.debug("续约批量更新联系人成功，数量: {}", toUpdateList.size());
            
            // 为非紧急联系人创建合同副本
            List<SaleContactPerson> eligibleForContractCopy = toUpdateList.stream()
                    .filter(contact -> isEligibleForContractCopy(contact.getFkContactPersonTypeKey()))
                    .collect(Collectors.toList());
            
            if (CollectionUtil.isNotEmpty(eligibleForContractCopy)) {
                createContractContactPersonCopies(eligibleForContractCopy, agentId, fkCompanyId);
                log.info("续约为更新的联系人创建合同副本，数量: {}", eligibleForContractCopy.size());
            }
        }

        // 批量新增 + 双重创建（排除紧急联系人）
        if (CollectionUtil.isNotEmpty(toInsertList)) {
            for (ContactPersonDto contactPersonDto : toInsertList) {
                if (StringUtils.isBlank(contactPersonDto.getFkContactPersonTypeKey())) {
                    contactPersonDto.setFkContactPersonTypeKey("CONTACT_AGENT_SALES");
                }
                agentService.addAgentContactPerson(contactPersonDto);

                // 为非紧急联系人创建合同副本
                if (isEligibleForContractCopy(contactPersonDto.getFkContactPersonTypeKey())) {
                    ContactPersonDto contractContactPersonDto = BeanCopyUtils.objClone(contactPersonDto,
                            ContactPersonDto::new);
                    contractContactPersonDto.setFkContactPersonTypeKey("CONTACT_AGENT_CONTRACT");
                    agentService.addAgentContactPerson(contractContactPersonDto);
                }
            }
            
            // 统计创建合同副本的数量用于日志记录
            long contractCopyCount = toInsertList.stream()
                    .filter(contact -> isEligibleForContractCopy(contact.getFkContactPersonTypeKey()))
                    .count();
            
            log.debug("续约批量新增联系人成功，数量: {}，其中创建合同副本: {}", toInsertList.size(), contractCopyCount);
        }

        // 删除不再需要的联系人记录（在现有记录中但不在本次处理范围内的）
        Set<Long> toDeleteIds = existingContactPersonMap.keySet().stream()
                .filter(id -> !processedContactPersonIds.contains(id))
                .collect(Collectors.toSet());

        if (CollectionUtil.isNotEmpty(toDeleteIds)) {
            contactPersonService.removeByIds(toDeleteIds);

            // 删除关联的公司数据
            LambdaQueryWrapper<SaleContactPersonCompany> companyWrapper = new LambdaQueryWrapper<SaleContactPersonCompany>()
                    .in(SaleContactPersonCompany::getFkContactPersonId, toDeleteIds);
            List<SaleContactPersonCompany> saleContactPersonCompanies = this.contactPersonCompanyMapper
                    .selectList(companyWrapper);
            if (CollectionUtil.isNotEmpty(saleContactPersonCompanies)) {
                this.contactPersonCompanyMapper.delete(companyWrapper);
            }

            log.info("续约删除不再需要的联系人记录，数量: {}", toDeleteIds.size());
        }

        log.info("续约联系人数据同步完成，更新: {}, 新增: {}, 删除: {}", toUpdateList.size(), toInsertList.size(), toDeleteIds.size());
    }

    /**
     * 准备更新的SaleContactPerson记录（续约专用）
     */
    private void prepareUpdateSaleContactPersonForRenewal(AppAgentContactPerson appContactPerson,
                                                          SaleContactPerson existing) {
        BeanCopyUtils.copyProperties(appContactPerson, existing, "id", "fkTableName", "fkTableId", "fkContactPersonId");
        utilService.setUpdateInfo(existing);
    }

    /**
     * 准备新增的ContactPersonDto记录（续约专用）
     */
    private ContactPersonDto prepareNewContactPersonDtoForRenewal(AppAgentContactPerson appContactPerson, Long agentId,
                                                                  Long fkCompanyId) {
        ContactPersonDto newContactPersonDto = BeanCopyUtils.objClone(appContactPerson, ContactPersonDto::new);
        newContactPersonDto.setFkCompanyId(fkCompanyId);
        newContactPersonDto.setFkTableName(TableEnum.SALE_AGENT.key);
        newContactPersonDto.setFkTableId(agentId);
        newContactPersonDto.setGmtModified(null);
        newContactPersonDto.setGmtModifiedUser(null);
        return newContactPersonDto;
    }

    /**
     * 同步生成合同
     *
     * @param appAgentSetAgreeContext
     */
    private void doCreateAgentContract(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        Long fkAgentId = appAgentSetAgreeContext.getAgentId();
        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
        // 生成合同数据

        Long agentId = appAgentVo.getId();
        AppAgent appAgent = this.appAgentMapper.selectById(agentId);
        if (ObjectUtils.isNull(appAgent)) {
            log.error("代理申请数据有误");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_app_data_error"));
        }
        Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();

        String nature = appAgentVo.getNature();
        AgentContractDto agentContractDto = new AgentContractDto();
        agentContractDto.setContractApprovalMode(0);
        if (isRenewal) {
            agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.PMP_MAIN_CONTRACT.getCode());
        } else {
            // 申请来源
            Integer appFrom = appAgent.getAppFrom();
            if (appFrom == null || AgentAppFromEnum.getAgentAppFromEnum(appFrom) == null) {
                agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());
            }
            AgentAppFromEnum agentAppFromEnum = AgentAppFromEnum.getAgentAppFromEnum(appFrom);
            switch (agentAppFromEnum) {
                case WEB_APPLY_1:
                    agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());
                    break;
                case WEB_APPLY_2:
                case PARTNER_APPLY_2:
                    agentContractDto.setContractTemplateMode(ContractTemplateModeEnum.PMP_MAIN_CONTRACT.getCode());
                    break;
                default:
                    break;
            }
        }
        if (GeneralTool.isNotEmpty(nature)) {
            agentContractDto.setFkAgentContractTypeId(Long.valueOf((nature)));
        }
        agentContractDto.setFkAgentId(fkAgentId);
        agentContractDto.setIsActive(true);
        agentContractDto.setStartTime(appAgentVo.getContractStartTime());
        agentContractDto.setEndTime(appAgentVo.getContractEndTime());
        List<AgentCompany> agentCompanies = agentCompanyService
                .list(Wrappers.<AgentCompany>lambdaQuery().eq(AgentCompany::getFkAgentId, fkAgentId));
        if (GeneralTool.isEmpty(agentCompanies)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        Long fkCompanyId = agentCompanies.get(0).getFkCompanyId();
        agentContractDto.setFkCompanyId(fkCompanyId);
        // 合同保存
        agentContractService.addAgentContract(agentContractDto);
        appAgentSetAgreeContext.setFkCompanyId(fkCompanyId);
    }

    /**
     * 同步代理
     *
     * @param appAgentVo
     * @return
     */
    private AppAgentSetAgreeContext doCopyAppAgentToAgent(AppAgentVo appAgentVo) {
        Boolean isRenewal = AgentAppTypeEnum.RENEWAL_APPLICATION.getCode().equals(appAgentVo.getAppType())
                && appAgentVo.getFkAgentId() != null;
        AppAgentSetAgreeContext appAgentSetAgreeContext = new AppAgentSetAgreeContext();
        appAgentSetAgreeContext.setAppAgentDto(appAgentVo);
        appAgentSetAgreeContext.setIsRenewal(isRenewal);
        if (isRenewal) {
            // 续约处理
            appAgentSetAgreeContext.setAgentId(appAgentVo.getFkAgentId());
            Long id = appAgentVo.getId();
            AppAgent appAgent = this.getById(id);
            if (ObjectUtils.isNull(appAgent)) {
                log.error("代理申请数据有误");
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_app_data_error"));
            }

            // 获取现有的Agent数据
            Agent existingAgent = agentService.getById(appAgentVo.getFkAgentId());
            if (ObjectUtils.isEmpty(existingAgent)) {
                log.error("找不到对应的代理数据，AgentId: {}", appAgentVo.getFkAgentId());
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_found"));
            }

            existingAgent.setFkAreaCountryId(appAgent.getFkAreaCountryId());
            existingAgent.setFkAreaStateId(appAgent.getFkAreaStateId());
            existingAgent.setFkAreaCityId(appAgent.getFkAreaCityId());
            existingAgent.setName(appAgent.getName());
            existingAgent.setNameNote(appAgent.getNameNote());
            existingAgent.setPersonalName(appAgent.getPersonalName());
            existingAgent.setNickName(appAgent.getNickName());
            existingAgent.setNature(appAgent.getNature());
            existingAgent.setNatureNote(appAgent.getNatureNote());
            existingAgent.setLegalPerson(appAgent.getLegalPerson());
            existingAgent.setTaxCode(appAgent.getTaxCode());
            existingAgent.setIdCardNum(appAgent.getIdCardNum());
            existingAgent.setAddress(appAgent.getAddress());
            existingAgent.setRemark(appAgent.getRemark());

            // 设置修改信息
            utilService.setUpdateInfo(existingAgent);

            // 保存更新
            boolean updateResult = agentService.updateById(existingAgent);
            if (!updateResult) {
                log.error("更新Agent数据失败，AgentId: {}", existingAgent.getId());
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }

            log.info("代理续约更新成功，AgentId: {}, AppAgentId: {}", existingAgent.getId(), appAgent.getId());

        } else {
            // 新代理申请处理
            AgentDto agentDto = BeanCopyUtils.objClone(appAgentVo, AgentDto::new);
            assert agentDto != null;
            agentDto.setId(null);
            agentDto.setIsSettlementPort(true);
            agentDto.setIsActive(true);
            agentDto.setIsKeyAgent(false);
            agentDto.setGmtModified(null);
            agentDto.setGmtModifiedUser(null);
            // AgentAddDto agentAddVo = new AgentAddDto();
            // agentAddVo.setAgentVo(agentDto);
            Long fkAgentId = agentService.addAgent(agentDto);
            appAgentSetAgreeContext.setAgentId(fkAgentId);
        }

        return appAgentSetAgreeContext;
    }

    /**
     * 审批拒绝
     *
     * @param appAgentVo
     */
    private void doSetReject(AppAgentVo appAgentVo) {
        if (GeneralTool.isEmpty(appAgentVo.getAppStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (!ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(appAgentVo.getAppStatus())) {
            // 如果不为申请中状态了 则不能设置成拒绝
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        if (!Objects.equals(appAgentVo.getAppStatusModifyUser(), SecureUtil.getLoginId())) {
            // 登录人和申请审批的人不是同一个 则不能设置拒绝
            throw new GetServiceException(LocaleMessageUtils.getMessage("NON_REVIEWER"));
        }
        AppAgent appAgent = BeanCopyUtils.objClone(appAgentVo, AppAgent::new);
        assert appAgent != null;
        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_REJECT.key);
        appAgent.setAppStatusModifyUser(SecureUtil.getLoginId());
        appAgent.setAppStatusModifyTime(new Date());
        utilService.setUpdateInfo(appAgent);
        boolean b = updateById(appAgent);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        // 根据申请来源类型发送不同的拒绝邮件
        sendRejectionEmailsByType(appAgentVo);
    }

    /**
     * 发送拒绝邮件
     *
     * @param appAgentVo
     */
    private void addRejectReminderTask(AppAgentVo appAgentVo) {
        Long fkAppAgentId = appAgentVo.getId();
        if (GeneralTool.isEmpty(appAgentVo.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_id_null"));
        }

        Map<Long, String> companyConfigMap = permissionCenterClient
                .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_AGENT_APP_REJECTED.key, 1).getData();
        String value1 = companyConfigMap.get(appAgentVo.getFkCompanyId());
        // ConfigVo configDto =
        // permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_AGENT_APP_REJECTED.key).getData();
        // String value1 = configDto.getValue1();
        // JSONObject jsonObject = JSONObject.parseObject(value1);
        // Integer geaStatus = jsonObject.getInteger("OTHER");
        // Integer iaeStatus = jsonObject.getInteger("IAE");

        if (value1.equals("1")) {
            StringBuilder taskTitle = new StringBuilder("代理在线申请");
            taskTitle.append("（").append(appAgentVo.getName()).append("）").append("，审批拒绝");
            String staffName = permissionCenterClient.getStaffName(appAgentVo.getFkStaffId()).getData();
            // 加提醒任务
            Map<String, String> map = new HashMap<>();
            map.put("staffName", staffName);
            map.put("agentName", appAgentVo.getName());
            map.put("other", "，已经提交在线申请表单，审批拒绝。");
            String taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER);

            RemindTaskDto remindTaskVo = new RemindTaskDto();
            remindTaskVo.setTaskTitle(taskTitle.toString());
            remindTaskVo.setTaskRemark(taskRemark);
            // 邮件方式发送
            remindTaskVo.setRemindMethod("1");
            // 默认设置执行中
            remindTaskVo.setStatus(1);
            // 默认背景颜色
            remindTaskVo.setTaskBgColor("#3788d8");
            remindTaskVo.setFkStaffId(appAgentVo.getFkStaffId());
            remindTaskVo.setStartTime(new Date());
            remindTaskVo.setFkTableName(TableEnum.APP_AGENT.key);
            remindTaskVo.setFkTableId(fkAppAgentId);
            remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.APP_AGENT_ADD_NOTICE.key);

            Result<Boolean> result = reminderCenterClient.batchAdd(Lists.newArrayList(remindTaskVo));
            if (!result.isSuccess() || GeneralTool.isEmpty(result.getData())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
            }
        }

    }

    /**
     * 我要审核
     *
     * @param appAgentVo
     */
    private void doSetReview(AppAgentVo appAgentVo) {
        if (GeneralTool.isEmpty(appAgentVo.getAppStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (!ProjectExtraEnum.APP_STATUS_NEW.key.equals(appAgentVo.getAppStatus())) {
            // 如果不为新申请状态了 则不能设置成我要审核
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        AppAgent appAgent = BeanCopyUtils.objClone(appAgentVo, AppAgent::new);
        assert appAgent != null;
        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_REVIEW.key);
        appAgent.setAppStatusModifyUser(SecureUtil.getLoginId());
        appAgent.setAppStatusModifyTime(new Date());
        utilService.setUpdateInfo(appAgent);
        boolean b = updateById(appAgent);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

    }

    /**
     * 设置合同账户列表属性
     *
     * @param appAgentContractAccountListVos
     */
    private void doSetAppAgentContractAccountListDtos(
            List<AppAgentContractAccountListVo> appAgentContractAccountListVos) {
        if (GeneralTool.isEmpty(appAgentContractAccountListVos)) {
            return;
        }
        Set<String> currencyTypeNums = appAgentContractAccountListVos.stream()
                .map(AppAgentContractAccountListVo::getFkCurrencyTypeNum).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, String> currencyTypeNumNameMap = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums)
                .getData();
        // 附件
        Set<Long> appAgentContractAccountIds = appAgentContractAccountListVos.stream()
                .map(AppAgentContractAccountListVo::getId).collect(Collectors.toSet());
        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtosMap = mediaAndAttachedService
                .getMediaAndAttachedDtoByFkTableIds(TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key,
                        appAgentContractAccountIds);
        for (AppAgentContractAccountListVo appAgentContractAccountListVo : appAgentContractAccountListVos) {
            /* 设置币种名称 */
            if (GeneralTool.isNotEmpty(currencyTypeNumNameMap)
                    && GeneralTool.isNotEmpty(appAgentContractAccountListVo.getFkCurrencyTypeNum())) {
                appAgentContractAccountListVo.setFkCurrencyTypeNumName(
                        currencyTypeNumNameMap.get(appAgentContractAccountListVo.getFkCurrencyTypeNum()));
            }
            /* 设置币种名称 */

            // 设置附件
            List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedDtosMap
                    .get(appAgentContractAccountListVo.getId());
            appAgentContractAccountListVo.setMediaAndAttachedDtos(mediaAndAttachedVos);
        }
    }

    /**
     * 查询合同账户列表
     *
     * @param appAgentContractAccountListDto
     * @param page
     * @return
     */
    private List<AppAgentContractAccountListVo> doGetAppAgentContractAccountListDtos(
            AppAgentContractAccountListDto appAgentContractAccountListDto, Page page) {
        if (GeneralTool.isEmpty(appAgentContractAccountListDto.getFkAppAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        List<AppAgentContractAccount> appAgentContractAccountList = appAgentContractAccountService
                .getAppAgentContractAccounts(appAgentContractAccountListDto, page);
        if (GeneralTool.isEmpty(appAgentContractAccountList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(appAgentContractAccountList, AppAgentContractAccountListVo::new);
    }

    /**
     * 设置联系人列表属性
     *
     * @param appAgentContactPersonListVos
     */
    private void doSetAppAgentContactPersonListDto(List<AppAgentContactPersonListVo> appAgentContactPersonListVos) {
        if (GeneralTool.isEmpty(appAgentContactPersonListVos)) {
            return;
        }
        /* 联系人类型名称 */
        List<ContactPersonType> contactPersonTypes = contactPersonTypeService.list();
        Map<String, String> typeKeyNameMap = contactPersonTypes.stream().collect(HashMap::new,
                (m, v) -> m.put(v.getTypeKey(), v.getTypeName()), HashMap::putAll);
        /* 联系人类型名称 */

        for (AppAgentContactPersonListVo appAgentContactPersonListVo : appAgentContactPersonListVos) {
            /* 联系人类型名称 */
            if (GeneralTool.isNotEmpty(appAgentContactPersonListVo.getFkContactPersonTypeKey())) {
                StringJoiner sj = new StringJoiner(",");
                String[] contactPersonTypeKeys = appAgentContactPersonListVo.getFkContactPersonTypeKey().split(",");
                for (String contactPersonTypeKey : contactPersonTypeKeys) {
                    if (GeneralTool.isNotEmpty(typeKeyNameMap)
                            && GeneralTool.isNotEmpty(typeKeyNameMap.get(contactPersonTypeKey))) {
                        sj.add(typeKeyNameMap.get(contactPersonTypeKey));
                    }
                }
                appAgentContactPersonListVo.setContactPersonTypeName(sj.toString());
            }
            /* 联系人类型名称 */
        }

    }

    /**
     * 查询联系人列表
     *
     * @param appAgentContactPersonListDto
     * @param page
     * @return
     */
    private List<AppAgentContactPersonListVo> doGetAppAgentContactPersonListDtos(
            AppAgentContactPersonListDto appAgentContactPersonListDto, Page page) {
        if (GeneralTool.isEmpty(appAgentContactPersonListDto.getFkAppAgentId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        List<AppAgentContactPerson> appAgentContactPersonList = appAgentContactPersonService
                .getAppAgentContactPersons(appAgentContactPersonListDto, page);
        if (GeneralTool.isEmpty(appAgentContactPersonList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(appAgentContactPersonList, AppAgentContactPersonListVo::new);
    }

    /**
     * 设置属性
     *
     * @param appAgentVo
     */
    private void doSetAppAgentDto(AppAgentVo appAgentVo) {
        if (GeneralTool.isEmpty(appAgentVo)) {
            return;
        }

        String companyName = permissionCenterClient.getCompanyNameById(appAgentVo.getFkCompanyId()).getData();
        appAgentVo.setFkCompanyName(companyName);

        if (GeneralTool.isNotEmpty(appAgentVo.getFkAreaCountryId())) {
            String countryName = institutionCenterClient.getCountryNameById(appAgentVo.getFkAreaCountryId()).getData();
            appAgentVo.setFkCountryName(countryName);
        }

        if (GeneralTool.isNotEmpty(appAgentVo.getFkAreaStateId())) {
            String stateName = institutionCenterClient.getStateFullNameById(appAgentVo.getFkAreaStateId()).getData();
            appAgentVo.setFkStateName(stateName);
        }

        if (GeneralTool.isNotEmpty(appAgentVo.getFkAreaCityId())) {
            String cityName = institutionCenterClient.getCityFullNameById(appAgentVo.getFkAreaCityId()).getData();
            appAgentVo.setFkCityName(cityName);
        }

        if (GeneralTool.isNotEmpty(appAgentVo.getFkStaffId())) {
            String bdName = permissionCenterClient.getStaffName(appAgentVo.getFkStaffId()).getData();
            appAgentVo.setBdName(bdName);

            StaffBdCode staffBdCode = staffBdCodeService.getOne(
                    Wrappers.<StaffBdCode>lambdaQuery().eq(StaffBdCode::getFkStaffId, appAgentVo.getFkStaffId()));
            appAgentVo.setBdCode(staffBdCode.getBdCode());
        }

        if (GeneralTool.isNotEmpty(appAgentVo.getAppStatus())) {
            appAgentVo.setAppStatusName(
                    ProjectExtraEnum.getValueByKey(appAgentVo.getAppStatus(), ProjectExtraEnum.APP_STATUS_TYPE));
        }
        if (GeneralTool.isNotEmpty(appAgentVo.getNature())) {
            try {
                appAgentVo.setNatureName(ProjectExtraEnum.getValueByKey(Integer.valueOf(appAgentVo.getNature()),
                        ProjectExtraEnum.AGENT_NATURE_TYPE));
            } catch (Exception e) {
                throw new GetServiceException(e.getMessage());
            }
        }

    }

    /**
     * 获取AppAgentDto
     *
     * @param id
     * @return
     */
    private AppAgentVo doGetAppAgentDto(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AppAgent appAgent = appAgentMapper.selectById(id);
        AppAgentVo appAgentVo = BeanCopyUtils.objClone(appAgent, AppAgentVo::new);
        return appAgentVo;
    }

    /**
     * 设置属性
     *
     * @param appAgentListVos
     */
    private void doSetAppAgentListDtoName(List<AppAgentListVo> appAgentListVos) {
        if (GeneralTool.isEmpty(appAgentListVos)) {
            return;
        }

        // 公司ids
        Set<Long> companyIds = appAgentListVos.stream().map(AppAgentListVo::getFkCompanyId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 国家ids
        Set<Long> countryIds = appAgentListVos.stream().map(AppAgentListVo::getFkAreaCountryId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 州省ids
        Set<Long> stateIds = appAgentListVos.stream().map(AppAgentListVo::getFkAreaStateId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 城市ids
        Set<Long> cityIds = appAgentListVos.stream().map(AppAgentListVo::getFkAreaCityId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 代理ids
        Set<Long> agentIds = appAgentListVos.stream().map(AppAgentListVo::getFkAgentId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // BDids
        Set<Long> staffIds = appAgentListVos.stream().map(AppAgentListVo::getFkStaffId).filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        Map<Long, String> countryNamesMap = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
        Map<Long, String> stateFullNamesMap = institutionCenterClient.getStateFullNamesByIds(stateIds).getData();
        Map<Long, String> cityFullNamesMap = institutionCenterClient.getCityFullNamesByIds(cityIds).getData();
        Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);
        Map<Long, String> numMap = Maps.newHashMap();
        Map<Long, String> bdCodeMap = Maps.newHashMap();

        if (GeneralTool.isNotEmpty(agentIds)) {
            List<Agent> agents = agentService.list(Wrappers.<Agent>lambdaQuery().in(Agent::getId, agentIds));
            numMap = agents.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v.getNum()), HashMap::putAll);
        }
        if (GeneralTool.isNotEmpty(staffIds)) {
            List<StaffBdCode> staffBdCodes = staffBdCodeService
                    .list(Wrappers.<StaffBdCode>lambdaQuery().in(StaffBdCode::getFkStaffId, staffIds));
            if (GeneralTool.isNotEmpty(staffBdCodes)) {
                bdCodeMap = staffBdCodes.stream().collect(HashMap::new,
                        (m, v) -> m.put(v.getFkStaffId(), v.getBdCode()), HashMap::putAll);
            }
        }

        for (AppAgentListVo appAgentListVo : appAgentListVos) {
            /* 区域名称 */
            StringBuilder area = new StringBuilder();
            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAreaCountryId())
                    && GeneralTool.isNotEmpty(countryNamesMap)
                    && GeneralTool.isNotEmpty(countryNamesMap.get(appAgentListVo.getFkAreaCountryId()))) {
                area.append(countryNamesMap.get(appAgentListVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAreaStateId())
                    && GeneralTool.isNotEmpty(stateFullNamesMap)
                    && GeneralTool.isNotEmpty(stateFullNamesMap.get(appAgentListVo.getFkAreaStateId()))) {
                area.append("/").append(stateFullNamesMap.get(appAgentListVo.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAreaCityId())
                    && GeneralTool.isNotEmpty(cityFullNamesMap)
                    && GeneralTool.isNotEmpty(cityFullNamesMap.get(appAgentListVo.getFkAreaCityId()))) {
                area.append("/").append(cityFullNamesMap.get(appAgentListVo.getFkAreaCityId()));
            }
            appAgentListVo.setAreaName(area.toString());
            /* 区域名称 */

            /* 公司名称 */
            if (GeneralTool.isNotEmpty(appAgentListVo.getFkCompanyId()) && GeneralTool.isNotEmpty(companyNameMap)) {
                appAgentListVo.setFkCompanyName(companyNameMap.get(appAgentListVo.getFkCompanyId()));
            }
            /* 公司名称 */

            /* 代理编号 */
            if (GeneralTool.isNotEmpty(appAgentListVo.getFkAgentId()) && GeneralTool.isNotEmpty(numMap)) {
                appAgentListVo.setAgentNum(numMap.get(appAgentListVo.getFkAgentId()));
            }
            /* 代理编号 */

            /* bd名称&bd编号 */
            if (GeneralTool.isNotEmpty(appAgentListVo.getFkStaffId())) {
                if (GeneralTool.isNotEmpty(staffNamesMap)) {
                    appAgentListVo.setBdName(staffNamesMap.get(appAgentListVo.getFkStaffId()));
                }
                if (GeneralTool.isNotEmpty(bdCodeMap)) {
                    appAgentListVo.setBdCode(bdCodeMap.get(appAgentListVo.getFkStaffId()));
                }
            }
            // 申请状态名称
            if (GeneralTool.isNotEmpty(appAgentListVo.getAppStatus())) {
                appAgentListVo.setAppStatusName(ProjectExtraEnum.getValueByKey(appAgentListVo.getAppStatus(),
                        ProjectExtraEnum.APP_STATUS_TYPE));
            }

            // 代理性质名称
            if (GeneralTool.isNotEmpty(appAgentListVo.getNature())) {
                try {
                    appAgentListVo.setNatureName(ProjectExtraEnum.getValueByKey(
                            Integer.valueOf(appAgentListVo.getNature()), ProjectExtraEnum.AGENT_NATURE_TYPE));
                } catch (Exception e) {
                    throw new GetServiceException(e.getMessage());
                }
            }
        }

    }

    /**
     * 查询列表数据
     *
     * @param appAgentListDto
     * @param page
     * @return
     */
    private List<AppAgentListVo> doGetAppAgentListDtos(AppAgentListDto appAgentListDto, Page page) {
        IPage<Agent> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        // 获取业务下属
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffFollowerIds.add(staffId);
        // 获取分页数据
        List<AppAgentListVo> appAgentListVos = appAgentMapper.getAppAgents(iPage, staffFollowerIds, appAgentListDto,
                appAgentListDto.getAgentAnnualSummaryVo());
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(appAgentListVos)) {
            return Collections.emptyList();
        }
        return appAgentListVos;
    }

    /**
     * 保存合同账户
     *
     * @param addAppAgentContext
     */
    private void doSaveAppAgentContractAccount(AddAppAgentContext addAppAgentContext) {
        AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();
        List<AppAgentContractAccountAddDto> appAgentContractAccountAddDtos = appAgentAddDto
                .getAppAgentContractAccountAddVos();

        if (GeneralTool.isNotEmpty(appAgentAddDto.getId())) {
            // 如果是更新
            if (GeneralTool.isNotEmpty(appAgentContractAccountAddDtos)) {

                List<AppAgentContractAccount> appAgentContractAccounts = appAgentContractAccountService
                        .list(Wrappers.lambdaQuery(AppAgentContractAccount.class)
                                .eq(AppAgentContractAccount::getFkAppAgentId, appAgentAddDto.getId()));

                Set<Long> accountIds = appAgentContractAccounts.stream().map(AppAgentContractAccount::getId)
                        .collect(Collectors.toSet());

                // 先删后增
                appAgentContractAccountService.remove(Wrappers.lambdaQuery(AppAgentContractAccount.class)
                        .eq(AppAgentContractAccount::getFkAppAgentId, appAgentAddDto.getId()));

                if (GeneralTool.isNotEmpty(accountIds)) {
                    // 先删后增
                    mediaAndAttachedService.remove(Wrappers.lambdaQuery(SaleMediaAndAttached.class)
                            .eq(SaleMediaAndAttached::getFkTableName, TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key)
                            .eq(SaleMediaAndAttached::getTypeKey,
                                    FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key)
                            .in(SaleMediaAndAttached::getFkTableId, accountIds));
                }
            }
        }

        // 一个币种只能创建一个账户
        Map<String, List<AppAgentContractAccountAddDto>> currencyNumMap = appAgentContractAccountAddDtos.stream()
                .collect(Collectors.groupingBy(AppAgentContractAccountAddDto::getFkCurrencyTypeNum));
        for (List<AppAgentContractAccountAddDto> agentContractAccountAddVoList : currencyNumMap.values()) {
            if (agentContractAccountAddVoList.size() > 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("only_has_one_account"));
            }
        }

        List<MediaAndAttachedDto> saveMediaAndAttachedDtos = Lists.newArrayList();
        for (AppAgentContractAccountAddDto appAgentContractAccountAddDto : appAgentContractAccountAddDtos) {
            if (GeneralTool.isEmpty(appAgentContractAccountAddDto.getIsDefault())) {
                appAgentContractAccountAddDto.setIsDefault(false);
            }
            List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentContractAccountAddDto.getMediaAndAttachedVos();
            AppAgentContractAccount appAgentContractAccount = BeanCopyUtils.objClone(appAgentContractAccountAddDto,
                    AppAgentContractAccount::new);
            assert appAgentContractAccount != null;
            appAgentContractAccount.setFkAppAgentId(addAppAgentContext.getFkAppAgentId());
            utilService.setCreateInfo(appAgentContractAccount);
            appAgentContractAccount.setGmtCreateUser("[form]");
            boolean b = appAgentContractAccountService.save(appAgentContractAccount);
            if (!b) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
            if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {
                for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                    String tableName = TableEnum.APP_AGENT_CONTRACT_ACCOUNT.key;
                    mediaAndAttachedDto.setFkTableName(tableName);
                    mediaAndAttachedDto.setFkTableId(appAgentContractAccount.getId());
                    mediaAndAttachedDto.setTypeKey(FileTypeEnum.APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE.key);
                    saveMediaAndAttachedDtos.add(mediaAndAttachedDto);
                }
            }
        }
        if (GeneralTool.isNotEmpty(saveMediaAndAttachedDtos)) {
            Boolean batchSaveResult = mediaAndAttachedService.saveBatchMediaAndAttached(saveMediaAndAttachedDtos);
            if (!batchSaveResult) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
    }

    /**
     * 保存代理联系人
     *
     * @param addAppAgentContext
     */
    private void doSaveAppAgentContactPerson(AddAppAgentContext addAppAgentContext) {
        AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();
        List<AppAgentContactPersonAddDto> appAgentContactPersonAddDtos = appAgentAddDto
                .getAppAgentContactPersonAddVos();
        if (GeneralTool.isNotEmpty(appAgentAddDto.getId())) {
            // 如果是编辑 先删后增
            appAgentContactPersonService.remove(Wrappers.lambdaQuery(AppAgentContactPerson.class)
                    .eq(AppAgentContactPerson::getFkAppAgentId, appAgentAddDto.getId()));
        }

        // 如果是新版，设置默认的isCommissionEmail值
        if (AgentAppFromEnum.isNewType(appAgentAddDto.getAppFrom())) {
            for (AppAgentContactPersonAddDto contactPerson : appAgentContactPersonAddDtos) {
                if (contactPerson.getIsCommissionEmail() != null) {
                    continue;
                }
                // 使用枚举获取默认值
                contactPerson.setIsCommissionEmail(
                        ContactPersonTypeEnum.getDefaultIsCommissionEmail(contactPerson.getFkContactPersonTypeKey()));
            }
        }

        List<AppAgentContactPerson> appAgentContactPeoples = BeanCopyUtils
                .copyListProperties(appAgentContactPersonAddDtos, AppAgentContactPerson::new);
        appAgentContactPeoples.forEach(appAgentContactPerson -> {
            appAgentContactPerson.setFkAppAgentId(addAppAgentContext.getFkAppAgentId());
            utilService.setCreateInfo(appAgentContactPerson);
            appAgentContactPerson.setGmtCreateUser("[form]");
        });
        boolean b = appAgentContactPersonService.saveBatch(appAgentContactPeoples);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 新增代理申请
     *
     * @param appAgentAddDto
     * @return
     */
    private AddAppAgentContext doSaveAppAgent(AppAgentAddDto appAgentAddDto) {
        AppAgent appAgent = BeanCopyUtils.objClone(appAgentAddDto, AppAgent::new);
        assert appAgent != null;
        // 设置名称
        if (ProjectExtraEnum.APP_AGENT_COMPANY.key.equals(appAgentAddDto.getNatureType())) {
            if (ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key.equals(appAgentAddDto.getCooperationType())) {
                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key));
            } else {
                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key));
            }
        } else if (ProjectExtraEnum.APP_AGENT_PERSONAL.key.equals(appAgentAddDto.getNatureType())) {
            appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key));
        }

        boolean isNewAdd = GeneralTool.isEmpty(appAgentAddDto.getId());

        // 若是奖学金过来的申请 创建人要改变
        String createUser = GeneralTool.isNotEmpty(appAgentAddDto.getUserId())
                ? "get_issue_" + appAgentAddDto.getUserId()
                : "[form]";
        // 新申请
        appAgent.setAppType(AgentAppTypeEnum.NEW_APPLICATION.getCode());
        if (isNewAdd) {
            // 默认新申请
            appAgent.setAppStatus(0);
            utilService.setCreateInfo(appAgent);
            appAgent.setGmtCreateUser(createUser);

            int i = appAgentMapper.insert(appAgent);
            if (i != 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        } else {
            if (appAgent.getAppStatus() == 3) { // 拒绝状态重新提交表单，则修改状态为新申请
                appAgent.setAppStatus(0);
            }
            utilService.setUpdateInfo(appAgent);
            appAgent.setGmtModifiedUser(createUser);
            int i = appAgentMapper.updateById(appAgent);
            if (i != 1) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }

        // 保存营业执照
        List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentAddDto.getMediaAndAttachedVos();
        if (!isNewAdd) {
            // 如果是编辑 先删后增
            mediaAndAttachedService.deleteMediaAndAttachedByTableId(appAgent.getId(), TableEnum.APP_AGENT.key);
        }
        if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                mediaAndAttachedDto.setFkTableId(appAgent.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);
                if (GeneralTool.isEmpty(mediaAndAttachedDto.getTypeKey())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
                }
            }
            Boolean flag = mediaAndAttachedService.saveBatchMediaAndAttached(mediaAndAttachedDtos);
            if (!flag) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
        AddAppAgentContext addAppAgentContext = new AddAppAgentContext();
        addAppAgentContext.setFkAppAgentId(appAgent.getId());
        addAppAgentContext.setAppAgentAddVo(appAgentAddDto);
        return addAppAgentContext;
    }

    /**
     * 注册合作伙伴用户
     * 新申请和续约申请统一注册管理员类型，根据业务类型发送不同的邮件模板
     *
     * @param appAgentSetAgreeContext 代理审核通过上下文
     */
private void doRegisterPartnerUsers(AppAgentSetAgreeContext appAgentSetAgreeContext) {
        Long agentId = appAgentSetAgreeContext.getAgentId();
        log.info("开始注册合作伙伴用户，代理ID: {}", agentId);

        AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
        List<AppAgentContactPerson> contactPersons = appAgentSetAgreeContext.getAppAgentContactPersons();

        if (GeneralTool.isEmpty(contactPersons)) {
            log.info("联系人列表为空，跳过合作伙伴用户注册，代理ID: {}", agentId);
            return;
        }

        try {
            // 获取BD员工邮件信息
            StaffVo bdStaff = getBdStaffInfoHasEmail(appAgentVo.getFkStaffId());
            Boolean isRenewal = appAgentSetAgreeContext.getIsRenewal();
            
            // 统一注册管理员类型，邮件模板根据续约状态区分
            ContactPersonTypeEnum targetPersonType = ContactPersonTypeEnum.ADMIN;
            
            log.info("业务类型: {}, 注册联系人类型: {}, 代理ID: {}", 
                isRenewal ? "续约申请" : "新申请", targetPersonType.getMsg(), agentId);
                
            // 统一的注册和邮件发送流程
            registerAndSendEmails(contactPersons, bdStaff, appAgentVo, appAgentSetAgreeContext, targetPersonType, isRenewal);
            
        } catch (Exception e) {
            log.error("注册合作伙伴用户异常，代理ID: {}", agentId, e);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("partner_user_register_process_failed", agentId));
        }
    }

    /**
     * 统一的注册和邮件发送流程
     *
     * @param contactPersons 联系人列表
     * @param bdStaff BD员工信息
     * @param appAgentVo 代理信息
     * @param context 上下文
     * @param targetPersonType 目标联系人类型
     * @param isRenewal 是否为续约
     */
    private void registerAndSendEmails(List<AppAgentContactPerson> contactPersons, StaffVo bdStaff,
                                     AppAgentVo appAgentVo, AppAgentSetAgreeContext context,
                                     ContactPersonTypeEnum targetPersonType, Boolean isRenewal) {
        Long agentId = context.getAgentId();
        Long appAgentId = appAgentVo.getId();
        
        // 参数校验
        validateRegisterParameters(contactPersons, bdStaff, appAgentVo, targetPersonType, agentId);
        
        // 过滤并构建注册用户列表
        List<RegisterPartnerUserDto> registerList = contactPersons.stream()
                .filter(contactPerson -> isEligibleContactPerson(contactPerson, targetPersonType))
                .map(contactPerson -> buildRegisterDto(contactPerson, bdStaff, appAgentVo, context))
                .collect(Collectors.toList());

        if (GeneralTool.isEmpty(registerList)) {
            log.info("没有符合条件的联系人需要注册，代理ID: {}, 目标类型: {}", agentId, targetPersonType.getMsg());
            return;
        }

        // 验证邮箱唯一性
        validateEmailUniqueness(registerList, agentId);

        log.info("准备注册合作伙伴用户，代理ID: {}, 目标类型: {}, 注册用户数: {}", 
            agentId, targetPersonType.getMsg(), registerList.size());

        try {
            // 调用公共方法进行用户注册
            List<RegisterPartnerUserVo> registerSuccessAccounts = callPartnerCenterRegister(registerList, agentId);

            // 发送合作伙伴用户审批通过邮件
            sendPartnerUserApprovalEmails(registerList, registerSuccessAccounts, agentId, appAgentId, isRenewal, bdStaff);
            
        } catch (Exception e) {
            log.error("注册和发送邮件流程异常，代理ID: {}, 目标类型: {}", agentId, targetPersonType.getMsg(), e);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("partner_user_register_and_email_failed", agentId));
        }
    }

    /**
     * 调用合作伙伴中心注册用户的公共方法
     *
     * @param registerList 注册用户列表
     * @param agentId 代理ID（用于日志记录）
     * @return 注册结果列表
     */
    @Override
    public List<RegisterPartnerUserVo> callPartnerCenterRegister(List<RegisterPartnerUserDto> registerList, Long agentId) {
        log.info("开始调用合作伙伴中心注册用户，代理ID: {}, 注册用户数: {}", agentId, registerList.size());
        
        if (CollectionUtil.isEmpty(registerList)) {
            log.warn("注册用户列表为空，跳过注册，代理ID: {}", agentId);
            return new ArrayList<>();
        }

        try {
            // 调用远程服务注册用户
            Result<List<RegisterPartnerUserVo>> registerResult = this.partnerCenterClient.registerPartnerUser(registerList);
            if (!registerResult.isSuccess()) {
                log.error("调用合作伙伴中心注册用户接口失败，代理ID: {}, 错误信息: {}", agentId, registerResult.getCode());
                throw new GetServiceException(LocaleMessageUtils.getFormatMessage("partner_user_register_failed", registerResult.getCode()));
            }

            List<RegisterPartnerUserVo> registerResults = registerResult.getData();
            log.info("成功注册合作伙伴用户，代理ID: {}, 注册用户数: {}", agentId, registerList.size());
            
            return registerResults != null ? registerResults : new ArrayList<>();
            
        } catch (Exception e) {
            log.error("注册合作伙伴用户异常，代理ID: {}", agentId, e);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("partner_user_register_error", agentId));
        }
    }

    /**
     * 验证注册参数
     *
     * @param contactPersons 联系人列表
     * @param bdStaff BD员工信息
     * @param appAgentVo 代理信息
     * @param targetPersonType 目标联系人类型
     * @param agentId 代理ID
     */
    private void validateRegisterParameters(List<AppAgentContactPerson> contactPersons, StaffVo bdStaff,
                                          AppAgentVo appAgentVo, ContactPersonTypeEnum targetPersonType, Long agentId) {
        if (GeneralTool.isEmpty(contactPersons)) {
            log.error("联系人列表为空，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("contact_persons_empty", agentId));
        }

        if (bdStaff == null) {
            log.error("BD员工信息为空，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_null", agentId));
        }

        if (appAgentVo == null) {
            log.error("代理申请信息为空，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("app_agent_null", agentId));
        }

        if (targetPersonType == null) {
            log.error("目标联系人类型为空，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("target_person_type_null", agentId));
        }

        // 验证BD员工邮件信息完整性
        if (StringUtils.isBlank(bdStaff.getEmail())) {
            log.error("BD员工邮件信息不完整，代理ID: {}, 邮箱: {}",
                agentId, bdStaff.getEmail());
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_email_incomplete", agentId));
        }
    }

    /**
     * 验证邮箱地址唯一性
     *
     * @param registerList 注册用户列表
     * @param agentId 代理ID
     */
    private void validateEmailUniqueness(List<RegisterPartnerUserDto> registerList, Long agentId) {
        Set<String> emailSet = new HashSet<>();
        List<String> duplicateEmails = new ArrayList<>();

        for (RegisterPartnerUserDto register : registerList) {
            String email = register.getToEmail();
            if (StringUtils.isBlank(email)) {
                log.warn("发现空邮箱地址，代理ID: {}, 联系人: {}", agentId, register.getToUser());
                continue;
            }

            if (!emailSet.add(email.toLowerCase())) {
                duplicateEmails.add(email);
            }
        }

        if (!duplicateEmails.isEmpty()) {
            log.error("发现重复邮箱地址，代理ID: {}, 重复邮箱: {}", agentId, duplicateEmails);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("duplicate_emails_found", 
                agentId, String.join(", ", duplicateEmails)));
        }
    }

    /**
     * 发送合作伙伴用户审批通过邮件
     *
     * @param registerList 注册用户列表
     * @param registerResults 注册结果列表
     * @param agentId 代理ID
     * @param appAgentId 代理申请ID
     * @param isRenewal 是否为续约申请
     * @param bdStaff BD员工信息
     */
    private void sendPartnerUserApprovalEmails(List<RegisterPartnerUserDto> registerList,
                                             List<RegisterPartnerUserVo> registerResults,
                                             Long agentId, Long appAgentId, Boolean isRenewal,
                                             StaffVo bdStaff) {
        if (GeneralTool.isEmpty(registerList)) {
            log.warn("注册用户列表为空，跳过邮件发送，代理ID: {}", agentId);
            return;
        }

        try {
            log.info("开始发送合作伙伴用户审批通过邮件，代理ID: {}, 邮件数量: {}", agentId, registerList.size());

            // 创建结果映射以提高查找性能
            Map<String, RegisterPartnerUserVo> resultMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(registerResults)) {
                resultMap = registerResults.stream()
                        .collect(Collectors.toMap(RegisterPartnerUserVo::getAccount, Function.identity(), (a, b) -> a));
            }

            List<EmailSendContext> hasAccountContexts = new ArrayList<>();
            List<EmailSendContext> noAccountContexts = new ArrayList<>();

            // 处理每个注册用户的邮件发送
            for (RegisterPartnerUserDto register : registerList) {
                String email = register.getToEmail();
                RegisterPartnerUserVo result = resultMap.get(email);

                boolean hasPassword = result != null && StringUtils.isNotBlank(result.getPassword());
                String password = hasPassword ? result.getPassword() : null;
                
                // 根据是否有密码确定邮件模板
                EmailTemplateEnum emailTemplate = determineEmailTemplate(isRenewal, hasPassword);
                
                // 构建邮件上下文
                EmailSendContext context = buildEmailSendContext(register, password, bdStaff.getId(), appAgentId, emailTemplate);
                
                if (hasPassword) {
                    hasAccountContexts.add(context);
                    log.debug("构建有账号邮件上下文，收件人: {}, 代理ID: {}", email, agentId);
                } else {
                    noAccountContexts.add(context);
                    log.debug("构建无账号邮件上下文，收件人: {}, 代理ID: {}", email, agentId);
                }
            }


            // 给BD员工发送相同内容的邮件副本
            sendBdEmailCopiesForRegistration(hasAccountContexts, noAccountContexts, agentId, appAgentId, bdStaff);

            // 批量发送邮件
            if (!hasAccountContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(hasAccountContexts, agentId);
                log.info("成功发送有账号邮件，代理ID: {}, 邮件数量: {}", agentId, hasAccountContexts.size());
            }

            if (!noAccountContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(noAccountContexts, agentId);
                log.info("成功发送无账号邮件，代理ID: {}, 邮件数量: {}", agentId, noAccountContexts.size());
            }

        } catch (Exception e) {
            log.error("发送合作伙伴用户审批通过邮件异常，代理ID: {}", agentId, e);
            // 不抛异常，避免影响主流程
        }
    }

    /**
     * 获取BD员工信息
     *
     * @param bdStaffId BD员工ID
     * @return BD员工信息
     */
    @Override
    public StaffVo getBdStaffInfoHasEmail(Long bdStaffId) {
        log.info("开始获取BD员工信息，员工ID: {}", bdStaffId);

        if (GeneralTool.isEmpty(bdStaffId)) {
            log.error("BD员工ID为空，无法获取发件人信息");
            throw new GetServiceException(LocaleMessageUtils.getMessage("bd_staff_id_null"));
        }

        Result<StaffVo> staffResult = permissionCenterClient.getStaffById(bdStaffId);
        if (!staffResult.isSuccess() || GeneralTool.isEmpty(staffResult.getData())) {
            log.error("获取BD员工信息失败，员工ID: {}, 错误信息: {}", bdStaffId, staffResult.getCode());
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_info_get_failed", bdStaffId));
        }

        StaffVo staffVo = staffResult.getData();
        if (StringUtils.isBlank(staffVo.getEmail())) {
            log.error("BD员工邮箱为空，员工ID: {}", bdStaffId);
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_email_null", bdStaffId));
        }

//        if (StringUtils.isBlank(staffVo.getEmailPassword())) {
//            log.error("BD员工邮件密码为空，员工ID: {}", bdStaffId);
//            throw new GetServiceException(
//                    LocaleMessageUtils.getFormatMessage("bd_staff_email_password_null", bdStaffId));
//        }

        log.info("成功获取BD员工信息，员工ID: {}, 邮箱: {}", bdStaffId, staffVo.getEmail());
        return staffVo;
    }

    /**
     * 批量获取BD员工信息, 跳过邮箱密码为空的staff数据
     *
     * @param staffIds BD员工ID集合
     * @return Map<Long, StaffVo> key为员工ID，value为BD员工信息
     */
    private Map<Long, StaffVo> getStaffInfoSkipEmptyEmailByStaffIds(Set<Long> staffIds) {
        log.info("开始批量获取BD员工信息，员工ID: {}", staffIds);

        Map<Long, StaffVo> resultMap = new HashMap<>();

        if (GeneralTool.isEmpty(staffIds)) {
            log.error("BD员工ID集合为空，无法获取员工信息");
            return resultMap;
        }

        List<StaffVo> staffList = permissionCenterClient.getStaffByIds(staffIds);
        if (CollectionUtil.isEmpty(staffList)) {
            log.warn("未获取到BD员工信息，员工ID集合: {}", staffIds);
            return resultMap;
        }

        // 转换为Map并验证员工信息完整性
        for (StaffVo staffVo : staffList) {
            if (staffVo == null) {
                continue;
            }

            if (StringUtils.isBlank(staffVo.getEmail())) {
                log.error("BD员工邮箱为空，跳过该员工，员工ID: {}", staffVo.getId());
                continue;
            }

            resultMap.put(staffVo.getId(), staffVo);
            log.debug("成功获取BD员工信息，员工ID: {}, 邮箱: {}", staffVo.getId(), staffVo.getEmail());
        }

        log.info("批量获取BD员工信息完成，成功获取 {}/{} 个员工信息", resultMap.size(), staffIds.size());
        return resultMap;
    }

    /**
     * 根据代理ID集合批量获取BD员工信息, 跳过邮箱密码为空的staff数据
     *
     * @param agentIds 代理ID集合
     * @return Map<Long, StaffVo> key为代理ID，value为BD员工信息
     */
    @Override
    public Map<Long, StaffVo> getStaffInfoSkipEmptyEmailByAgentIds(Set<Long> agentIds) {
        log.info("开始批量获取BD员工信息，代理ID集合: {}", agentIds);

        Map<Long, StaffVo> resultMap = new HashMap<>();

        // 参数校验
        if (GeneralTool.isEmpty(agentIds)) {
            log.error("代理ID集合为空，无法获取BD员工信息");
            return resultMap;
        }

        // 第一步：通过代理IDs获取AgentStaff映射关系
        Map<Long, AgentStaff> agentStaffMap = this.agentStaffService.getAgentStaffByAgentIds(agentIds);
        if (GeneralTool.isEmpty(agentStaffMap)) {
            log.warn("未找到代理对应的BD员工关系，代理ID集合: {}", agentIds);
            return resultMap;
        }

        // 第二步：提取所有BD员工IDs
        Set<Long> staffIds = agentStaffMap.values().stream()
                .map(AgentStaff::getFkStaffId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (GeneralTool.isEmpty(staffIds)) {
            log.warn("未找到有效的BD员工ID，代理ID集合: {}", agentIds);
            return resultMap;
        }

        // 第三步：批量获取BD员工信息
        Map<Long, StaffVo> staffInfoMap = this.getStaffInfoSkipEmptyEmailByStaffIds(staffIds);
        if (GeneralTool.isEmpty(staffInfoMap)) {
            log.warn("未获取到BD员工信息，员工ID集合: {}", staffIds);
            return resultMap;
        }

        // 第四步：组装结果Map（代理ID -> BD员工信息）
        for (Map.Entry<Long, AgentStaff> entry : agentStaffMap.entrySet()) {
            Long agentId = entry.getKey();
            Long staffId = entry.getValue().getFkStaffId();

            if (staffId != null && staffInfoMap.containsKey(staffId)) {
                StaffVo staffVo = staffInfoMap.get(staffId);
                resultMap.put(agentId, staffVo);
                log.debug("成功映射代理ID: {} -> BD员工: {}", agentId, staffVo.getName());
            } else {
                log.warn("代理ID: {} 对应的BD员工信息获取失败，员工ID: {}", agentId, staffId);
            }
        }

        log.info("批量获取BD员工信息完成，成功获取 {}/{} 个代理的BD信息",
                resultMap.size(), agentIds.size());

        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renewalAdd(AppAgentAddDto appAgentAddDto) {
        log.info("开始保存续约申请数据，参数: {}", appAgentAddDto);

        // 保存续约申请数据（专门为续约申请设计，不复用saveOrUpdateAppAgent以保持兼容性）
        AddAppAgentContext addAppAgentContext = saveRenewalAppAgent(appAgentAddDto);

        // 发送续约申请提交邮件（参考addAppAgent的新邮件模板逻辑）
        sendRenewalSubmittedEmails(addAppAgentContext);

        log.info("续约申请数据保存完成，申请ID: {}", addAppAgentContext.getFkAppAgentId());
    }

    /**
     * 专门为续约申请保存数据
     * 不能复用saveOrUpdateAppAgent，因为那个方法会强制设置appType为新申请，需要保持原有逻辑的兼容性
     *
     * @param appAgentAddDto 续约申请数据
     * @return AddAppAgentContext 保存上下文
     */
    private AddAppAgentContext saveRenewalAppAgent(AppAgentAddDto appAgentAddDto) {
        // 续约申请数据校验（复用现有校验逻辑）
        this.validateAppAgent(appAgentAddDto);

        // 1.保存续约申请主记录
        AddAppAgentContext addAppAgentContext = doSaveRenewalAppAgent(appAgentAddDto);

        // 2.保存联系人（复用现有逻辑）
        doSaveAppAgentContactPerson(addAppAgentContext);

        // 3.保存合同账户（复用现有逻辑）
        doSaveAppAgentContractAccount(addAppAgentContext);

        Long fkAgentId = appAgentAddDto.getFkAgentId();
        String key = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + fkAgentId;
        String token = this.getRedis.get(key);
        if (StringUtils.isNotBlank(token)) {
            this.getRedis.del(key);
        }
        return addAppAgentContext;
    }

    /**
     * 保存续约申请主记录
     * 基于doSaveAppAgent方法，但专门为续约申请设计
     *
     * @param appAgentAddDto 续约申请数据
     * @return AddAppAgentContext 保存上下文
     */
    private AddAppAgentContext doSaveRenewalAppAgent(AppAgentAddDto appAgentAddDto) {
        AppAgent appAgent = BeanCopyUtils.objClone(appAgentAddDto, AppAgent::new);
        if (appAgent == null) {
            log.error("续约申请数据转换失败，appAgentAddDto: {}", appAgentAddDto);
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        // 设置代理性质
        setAgentNature(appAgent, appAgentAddDto);

        // 判断是新增还是更新
        boolean isUpdate = GeneralTool.isNotEmpty(appAgentAddDto.getId());

        // 设置创建人
        String createUser = buildCreateUser(appAgentAddDto);

        // 设置续约申请特有属性
        appAgent.setAppType(AgentAppTypeEnum.RENEWAL_APPLICATION.getCode());

        // 保存或更新主记录
        if (isUpdate) {
            updateRenewalAppAgent(appAgent, createUser);
        } else {
            insertRenewalAppAgent(appAgent, createUser);
        }

        // 保存附件
        saveRenewalAppAgentAttachments(appAgent.getId(), appAgentAddDto, isUpdate);

        // 构建返回上下文
        AddAppAgentContext addAppAgentContext = new AddAppAgentContext();
        addAppAgentContext.setFkAppAgentId(appAgent.getId());
        addAppAgentContext.setAppAgentAddVo(appAgentAddDto);
        return addAppAgentContext;
    }

    /**
     * 设置代理性质
     */
    private void setAgentNature(AppAgent appAgent, AppAgentAddDto appAgentAddDto) {
        if (ProjectExtraEnum.APP_AGENT_COMPANY.key.equals(appAgentAddDto.getNatureType())) {
            if (ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key.equals(appAgentAddDto.getCooperationType())) {
                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key));
            } else {
                appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key));
            }
        } else if (ProjectExtraEnum.APP_AGENT_PERSONAL.key.equals(appAgentAddDto.getNatureType())) {
            appAgent.setNature(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key));
        }
    }

    /**
     * 构建创建人信息
     */
    private String buildCreateUser(AppAgentAddDto appAgentAddDto) {
        return GeneralTool.isNotEmpty(appAgentAddDto.getUserId())
                ? "get_issue_" + appAgentAddDto.getUserId()
                : "[form]";
    }

    /**
     * 新增续约申请记录
     */
    private void insertRenewalAppAgent(AppAgent appAgent, String createUser) {
        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key);
        utilService.setCreateInfo(appAgent);
        appAgent.setGmtCreateUser(createUser);

        int result = appAgentMapper.insert(appAgent);
        if (result != 1) {
            log.error("续约申请新增失败，appAgent: {}", appAgent);
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
    }

    /**
     * 更新续约申请记录
     */
    private void updateRenewalAppAgent(AppAgent appAgent, String createUser) {
        // 如果是拒绝状态重新提交，修改为新申请状态
        if (appAgent.getAppStatus() != null && appAgent.getAppStatus() == 3) {
            appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key);
        }

        utilService.setUpdateInfo(appAgent);
        appAgent.setGmtModifiedUser(createUser);

        int result = appAgentMapper.updateById(appAgent);
        if (result != 1) {
            log.error("续约申请更新失败，appAgent: {}", appAgent);
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 保存续约申请附件
     */
    private void saveRenewalAppAgentAttachments(Long appAgentId, AppAgentAddDto appAgentAddDto, boolean isUpdate) {
        List<MediaAndAttachedDto> mediaAndAttachedDtos = appAgentAddDto.getMediaAndAttachedVos();

        if (isUpdate) {
            // 更新时先删除原有附件
            mediaAndAttachedService.deleteMediaAndAttachedByTableId(appAgentId, TableEnum.APP_AGENT.key);
        }

        if (GeneralTool.isNotEmpty(mediaAndAttachedDtos)) {
            // 设置附件关联信息
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtos) {
                mediaAndAttachedDto.setFkTableId(appAgentId);
                mediaAndAttachedDto.setFkTableName(TableEnum.APP_AGENT.key);

                if (GeneralTool.isEmpty(mediaAndAttachedDto.getTypeKey())) {
                    log.error("附件类型为空，mediaAndAttachedDto: {}", mediaAndAttachedDto);
                    throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
                }
            }

            Boolean result = mediaAndAttachedService.saveBatchMediaAndAttached(mediaAndAttachedDtos);
            if (!result) {
                log.error("续约申请附件保存失败，appAgentId: {}, attachments: {}", appAgentId, mediaAndAttachedDtos);
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
    }

    /**
     * 判断联系人是否符合注册条件
     * 检查联系人的角色类型是否包含指定的联系人类型
     *
     * @param contactPerson 联系人信息
     * @param personTypeEnum 目标联系人类型枚举
     * @return true表示符合条件，false表示不符合条件
     */
    /**
     * 判断联系人是否符合注册条件
     *
     * @param contactPerson 联系人
     * @param personTypeEnum 联系人类型枚举
     * @return 是否符合条件
     */
    protected boolean isEligibleContactPerson(AppAgentContactPerson contactPerson, ContactPersonTypeEnum personTypeEnum) {
        if (contactPerson == null || personTypeEnum == null) {
            return false;
        }
        String roleKey = contactPerson.getFkContactPersonTypeKey();
        return isEligibleContactPersonByRoleKey(roleKey, personTypeEnum);
    }

    /**
     * 判断联系人是否符合注册条件（SaleContactPerson版本）
     *
     * @param contactPerson 联系人
     * @param personTypeEnum 联系人类型枚举
     * @return 是否符合条件
     */
    @Override
    public boolean isEligibleContactPerson(SaleContactPerson contactPerson, ContactPersonTypeEnum personTypeEnum) {
        if (contactPerson == null || personTypeEnum == null) {
            return false;
        }
        String roleKey = contactPerson.getFkContactPersonTypeKey();
        return isEligibleContactPersonByRoleKey(roleKey, personTypeEnum);
    }

    /**
     * 根据角色键判断联系人是否符合注册条件
     *
     * @param roleKey 角色键（逗号分隔）
     * @param personTypeEnum 联系人类型枚举
     * @return 是否符合条件
     */
    protected boolean isEligibleContactPersonByRoleKey(String roleKey, ContactPersonTypeEnum personTypeEnum) {
        if (StringUtils.isBlank(roleKey) || personTypeEnum == null) {
            return false;
        }

        // 解析逗号分隔的联系人类型字符串，检查是否包含目标类型
        Set<String> roleSet = Arrays.stream(roleKey.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        return roleSet.contains(personTypeEnum.getCode());
    }

    /**
     * 构建注册用户DTO
     *
     * @param contactPerson 联系人
     * @param bdStaff BD员工
     * @param appAgentVo 代理申请信息
     * @param context 上下文
     * @return 注册用户DTO
     */
    private RegisterPartnerUserDto buildRegisterDto(AppAgentContactPerson contactPerson, StaffVo bdStaff,
                                                    AppAgentVo appAgentVo, AppAgentSetAgreeContext context) {
        if (contactPerson == null || bdStaff == null || appAgentVo == null || context == null) {
            log.error("构建注册DTO的参数不能为空，contactPerson存在: {}, bdStaff存在: {}, appAgentVo存在: {}, context存在: {}", 
                contactPerson != null, bdStaff != null, appAgentVo != null, context != null);
            throw new GetServiceException(LocaleMessageUtils.getMessage("build_register_dto_params_null"));
        }

        String roleKey = contactPerson.getFkContactPersonTypeKey();
        ContactPersonTypeEnum contactType = determineContactType(roleKey);

        RegisterPartnerUserDto registerDto = new RegisterPartnerUserDto();

        // 设置发件人信息
        registerDto.setFromUser(bdStaff.getName());
        registerDto.setFromEmail(bdStaff.getEmail());
        registerDto.setEmailPassword(bdStaff.getEmailPassword());

        // 设置收件人信息
        registerDto.setToUser(contactPerson.getName());
        String[] split = contactPerson.getEmail().split("; ");
        if (ArrayUtil.isNotEmpty(split)) {
            registerDto.setToEmail(split[0]);
        }
        // 设置业务信息
        registerDto.setAgentId(context.getAgentId());
        registerDto.setCompanyId(appAgentVo.getFkCompanyId());
        registerDto.setAgentName(appAgentVo.getName());
        registerDto.setApplyAgentName(appAgentVo.getName());
        registerDto.setLoginId(SecureUtil.getLoginId());

        // 设置角色信息
        registerDto.setPartnerRoleCode(contactType.getPartnerRoleCode());
        registerDto.setPartnerRoleId(contactType.getPartnerRoleId());
        registerDto.setTemplateKey(getTemplateKey(contactType));

        log.debug("构建注册DTO成功，代理ID: {}, 联系人: {}, 角色类型: {}", 
            context.getAgentId(), contactPerson.getName(), contactType.getMsg());

        return registerDto;
    }

    /**
     * 确定联系人类型
     *
     * @param roleKey 角色键
     * @return 联系人类型
     */
    private ContactPersonTypeEnum determineContactType(String roleKey) {
        if (StringUtils.isBlank(roleKey)) {
            log.error("联系人角色键为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("contact_person_role_key_empty"));
        }

        if (roleKey.contains(ContactPersonTypeEnum.ADMIN.getCode())) {
            return ContactPersonTypeEnum.ADMIN;
        } else if (roleKey.contains(ContactPersonTypeEnum.COMMISSION.getCode())) {
            return ContactPersonTypeEnum.COMMISSION;
        }
        
        log.error("不支持的联系人类型: {}", roleKey);
        throw new GetServiceException(LocaleMessageUtils.getFormatMessage("unsupported_contact_person_type", roleKey));
    }

    /**
     * 确定邮件模板类型
     *
     * @param isRenewal 是否为续约
     * @param hasPassword 是否有密码
     * @return 邮件模板枚举
     */
    private EmailTemplateEnum determineEmailTemplate(Boolean isRenewal, boolean hasPassword) {
        if (isRenewal) {
            return hasPassword ? 
                EmailTemplateEnum.AGENT_RENEWAL_APPROVED_HAS_ACCOUNT :
                EmailTemplateEnum.AGENT_RENEWAL_APPROVED_NO_ACCOUNT;
        } else {
            return hasPassword ?
                EmailTemplateEnum.AGENT_APPLICATION_APPROVED_HAS_ACCOUNT :
                EmailTemplateEnum.AGENT_APPLICATION_APPROVED_NO_ACCOUNT;
        }
    }

    /**
     * 获取邮件模板键
     *
     * @param contactType 联系人类型
     * @return 模板键
     */
    private String getTemplateKey(ContactPersonTypeEnum contactType) {
        switch (contactType) {
            case ADMIN:
            case COMMISSION:
                return MailTemplateTypeEnum.INVITE_TO_REGISTER.getCode();
            default:
                log.error("不支持的联系人类型: {}", contactType);
                return null;
        }
    }

    /**
     * 根据AgentContractRenewalDto组装续约申请数据回显，不保存到数据库
     * 参考AgentServiceImpl的renewalContract方法逻辑
     *
     * @param agentContractRenewalDto 代理合同续约DTO（包含代理ID和联系人ID）
     * @return 组装好的代理申请数据
     */
    @Override
    public AppAgentFormDetailVo getOrBuildRenewalApplicationData(AgentContractRenewalDto agentContractRenewalDto) {
        log.info("开始获取或构建代理续约申请数据，参数: {}", agentContractRenewalDto);

        // 参数校验
        if (Objects.isNull(agentContractRenewalDto)) {
            log.error("代理合同续约参数为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        Long agentId = agentContractRenewalDto.getAgentId();

        if (GeneralTool.isEmpty(agentId)) {
            log.error("代理ID为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }

        // 获取代理信息
        Agent agent = agentService.getById(agentId);
        if (Objects.isNull(agent)) {
            log.error("代理信息不存在，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_not_exist"));
        }
        String key = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + agentId;
        String token = this.getRedis.get(key);
        if (StringUtils.isNotBlank(token) && StringUtils.isNotBlank(agentContractRenewalDto.getRenewalToken()) && token.equals(agentContractRenewalDto.getRenewalToken())) {
            return buildRenewalApplicationDataFromScratch(agentContractRenewalDto, agent);
        }

        // 优化逻辑：先检查是否已有现成的续约申请数据，有则直接复用（不需要重新校验）
        AppAgent existingRenewalApplication = this.getOne(
                Wrappers.<AppAgent>lambdaQuery()
                        .eq(AppAgent::getFkAgentId, agentId)
                        .eq(AppAgent::getAppType, AgentAppTypeEnum.RENEWAL_APPLICATION.getCode()) // 续约申请类型
                        .orderByDesc(AppAgent::getGmtCreate)
                        .last("LIMIT 1"));

        if (existingRenewalApplication != null) {
            // 如果已存在续约申请数据，直接使用getAppAgentFormDetailById复用现成逻辑
            log.info("发现已存在的续约申请数据，复用现成数据，代理ID: {}, 申请ID: {}", agentId, existingRenewalApplication.getId());
            return getAppAgentFormDetailById(existingRenewalApplication.getId());
        }

        // 如果不存在现成数据，才进行合同状态校验并构建新数据
        // 校验代理合同审批状态 - 获取最新的有效合同状态进行校验
        AgentContract latestActiveAgentContract = agentContractService.latestActiveAgentContract(agentId);
        if (latestActiveAgentContract != null) {
            // 只有存在合同时才进行状态校验，状态不是"未签署、待审核、已驳回"才允许续约
            Integer contractApprovalStatus = latestActiveAgentContract.getContractApprovalStatus();
            if (!AgentContractApprovalStatusEnum.isRenewalApplicationAllowed(contractApprovalStatus)) {
                log.error("代理最新合同审批状态不允许续约，当前状态: {}({}), 代理ID: {}, 合同ID: {}",
                        contractApprovalStatus, contractApprovalStatus, agentId, latestActiveAgentContract.getId());
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_status_not_allow_renewal"));
            }
        }
        // 如果不存在现成数据，继续使用构建逻辑
        log.info("未发现现成的续约申请数据，开始构建新数据，代理ID: {}", agentId);
        return buildRenewalApplicationDataFromScratch(agentContractRenewalDto, agent);
    }

    /**
     * 从头构建代理续约申请数据
     * 当数据库中不存在现成的续约申请数据时，使用此方法从代理基础数据构建完整的续约申请表单数据
     *
     * @param agentContractRenewalDto 续约请求参数
     * @param agent                   代理基础信息
     * @return 构建完成的续约申请表单数据
     */
    private AppAgentFormDetailVo buildRenewalApplicationDataFromScratch(AgentContractRenewalDto agentContractRenewalDto,
                                                                        Agent agent) {
        Long agentId = agentContractRenewalDto.getAgentId();

        // 获取代理BD信息
        AgentStaff agentStaff = agentStaffService.getAgentStaffByAgentId(agentId);
        if (agentStaff == null) {
            log.error("代理BD信息不存在，代理ID: {}", agentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("bd_not_found"));
        }

        // 构造AppAgentFormDetailVo
        AppAgentFormDetailVo formDetailVo = buildAppAgentFormDetailFromAgent(agent, agentStaff.getFkStaffId());

        // 组装联系人数据
        List<AppAgentContactPersonAddDto> contactPersonList = buildContactPersonListFromAgent(agentId);
        formDetailVo.setAppAgentContactPersonAddVos(contactPersonList);

        // 组装合同账户数据
        List<AppAgentContractAccountAddDto> contractAccountList = buildContractAccountListFromAgent(agentId);
        formDetailVo.setAppAgentContractAccountAddVos(contractAccountList);

        // 组装附件数据
        List<MediaAndAttachedDto> attachmentList = buildAttachmentListFromAgent(agentId);
        formDetailVo.setMediaAndAttachedVos(attachmentList);

        // 设置bmsPrivateFilesUrl - 参考getAppAgentFormDetailById方法逻辑
        Properties props = System.getProperties();
        String profile = props.getProperty("spring.profiles.active");
        if (GeneralTool.isNotEmpty(profile)) {
            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.GRAY_CODE)
                    || profile.equals(AppConstant.TW_CODE)) {
                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_PRD_URL);
            } else if (profile.equals(AppConstant.IAE_CODE)) {
                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_IAE_PRD_URL);// IAE环境下使用私密桶地址为IAE桶url
            } else if (profile.equals(AppConstant.TEST_CODE)) {
                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_TEST_URL);
            } else {
                formDetailVo.setBmsPrivateFilesUrl(OSS_FILES_DEV_URL);
            }
        }

        LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()
                .eq(AgentContract::getFkAgentId, agentId)
                .eq(AgentContract::getIsActive, true)
                .orderByDesc(AgentContract::getGmtCreate)
                .last("LIMIT 1");
        AgentContract agentContract = this.agentContractService.getOne(agentContractLambdaQueryWrapper);
        if (agentContract != null) {
            formDetailVo.setContractNum(agentContract.getContractNum());
        }
        log.info("代理续约申请数据构建完成，代理ID: {}", agentId);
        return formDetailVo;
    }

    /**
     * 从Agent构造AppAgentFormDetailVo主要信息
     * 参考报告第3章"主表数据转换：buildAppAgentFromAgent"
     * <p>
     * 关键字段映射（与报告表格一致）：
     * - Agent.fkCompanyId → AppAgent.fkCompanyId (直接复制)
     * - Agent.name → AppAgent.name (直接复制)
     * - Agent.nature → AppAgent.nature (直接复制)
     * - Agent.taxCode → AppAgent.taxCode (直接复制)
     * - 固定值2 → AppAgent.appType (续约申请)
     * - 固定值0 → AppAgent.appStatus (待审核状态)
     * - Agent.id → AppAgent.fkAgentId (关联关系)
     *
     * @param agent     代理信息
     * @param fkStaffId BD员工ID（从AgentStaff查询）
     * @return AppAgentFormDetailVo对象
     */
    private AppAgentFormDetailVo buildAppAgentFormDetailFromAgent(Agent agent, Long fkStaffId) {
        if (agent == null || agent.getId() == null) {
            return null;
        }
        Result<StaffVo> staffResult = permissionCenterClient.getStaffById(fkStaffId);
        if (!staffResult.isSuccess() || GeneralTool.isEmpty(staffResult.getData())) {
            log.error("获取BD员工信息失败，员工ID: {}, 错误信息: {}", fkStaffId, staffResult.getCode());
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_info_get_failed", fkStaffId));
        }
        StaffVo staffInfo = staffResult.getData();
        AppAgentFormDetailVo formDetailVo = new AppAgentFormDetailVo();

        // 基本信息复制
        formDetailVo.setFkCompanyId(staffInfo.getFkCompanyId());
        formDetailVo.setFkAreaCountryId(agent.getFkAreaCountryId());
        formDetailVo.setFkAreaStateId(agent.getFkAreaStateId());
        formDetailVo.setFkAreaCityId(agent.getFkAreaCityId());
        formDetailVo.setName(agent.getName());
        formDetailVo.setNameNote(agent.getNameNote());
        formDetailVo.setPersonalName(StringUtils.isNotBlank(agent.getPersonalName()) ? agent.getPersonalName() : agent.getName());
        formDetailVo.setNickName(agent.getNickName());
        formDetailVo.setNature(agent.getNature());
        formDetailVo.setNatureNote(agent.getNatureNote());
        formDetailVo.setLegalPerson(agent.getLegalPerson());
        formDetailVo.setTaxCode(agent.getTaxCode());
        formDetailVo.setIdCardNum(agent.getIdCardNum());
        formDetailVo.setAddress(agent.getAddress());
        formDetailVo.setRemark(agent.getRemark());

        // BD员工信息
        formDetailVo.setFkStaffId(fkStaffId);

        // 根据nature设置natureType和cooperationType - 参考getAppAgentFormDetailById方法逻辑
        if (agent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_STUDIO.key))) {
            formDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_PERSONAL.key);
            formDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);
        }
        if (agent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_COMPANY.key))) {
            formDetailVo.setCooperationType(ProjectExtraEnum.APP_AGENT_BY_COMPANY.key);
            formDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_COMPANY.key);
        }
        if (agent.getNature().equals(String.valueOf(ProjectExtraEnum.AGENT_NATURE_PERSON.key))) {
            formDetailVo.setNatureType(ProjectExtraEnum.APP_AGENT_PERSONAL.key);
        }

        // 续约申请特有字段 - 根据报告中的固定值设置
        formDetailVo.setAppType(AgentAppTypeEnum.RENEWAL_APPLICATION.getCode()); // 续约申请 (固定值2)
        formDetailVo.setAppStatus(ProjectExtraEnum.APP_STATUS_NEW.key); // 待审核状态 (固定值0)
        formDetailVo.setFkAgentId(agent.getId()); // 关联原代理ID

        return formDetailVo;
    }

    /**
     * 从代理构造联系人列表
     * <p>
     * 转换逻辑：SaleContactPerson → AppAgentContactPerson
     * 字段映射：基本信息（姓名、性别、部门、职位）+ 联系方式（手机、邮箱）
     * 业务字段：联系人类型、是否佣金邮箱、是否新闻邮箱
     *
     * @param agentId 代理ID
     * @return 联系人列表
     */
    private List<AppAgentContactPersonAddDto> buildContactPersonListFromAgent(Long agentId) {
        // 查询代理的所有联系人
        List<SaleContactPerson> allContactPersons = contactPersonService.list(
                Wrappers.<SaleContactPerson>lambdaQuery()
                        .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)
                        .eq(SaleContactPerson::getFkTableId, agentId));

        // 防御性编程：检查查询结果
        if (CollectionUtil.isEmpty(allContactPersons)) {
            log.warn("代理ID: {} 没有查询到任何联系人信息", agentId);
            return new ArrayList<>();
        }

        // 在业务层过滤排除合同联系人类型，保持与前端显示逻辑一致
        // 增加空对象过滤，防止空指针异常
        List<SaleContactPerson> contactPersons = allContactPersons.stream()
                .filter(Objects::nonNull) // 过滤null对象
                .filter(person -> !isContractContactPerson(person.getFkContactPersonTypeKey()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(contactPersons)) {
            log.warn("代理ID: {} 没有找到非合同联系人信息", agentId);
            return new ArrayList<>();
        }

        log.info("代理ID: {} 查询到 {} 个非合同联系人", agentId, contactPersons.size());

        // 转换为DTO
        List<AppAgentContactPersonAddDto> contactPersonDtoList = new ArrayList<>();
        for (SaleContactPerson contactPerson : contactPersons) {
            // 双重检查：虽然已经过滤了，但为了保险起见再次检查
            if (contactPerson == null) {
                log.warn("发现空的联系人对象，跳过处理");
                continue;
            }

            AppAgentContactPersonAddDto dto = new AppAgentContactPersonAddDto();
            // 复制联系人基本信息 - 参考AgentServiceImpl.createAppAgentContactPersonList
            dto.setFkContactPersonTypeKey(contactPerson.getFkContactPersonTypeKey());
            dto.setName(contactPerson.getName());
            dto.setGender(contactPerson.getGender());
            dto.setDepartment(contactPerson.getDepartment());
            dto.setTitle(contactPerson.getTitle());
            dto.setMobileAreaCode(contactPerson.getMobileAreaCode());
            dto.setMobile(contactPerson.getMobile());
            dto.setEmail(contactPerson.getEmail());
            // 关联代理联系人id
            dto.setFkContactPersonId(contactPerson.getId());

            // 复制邮件相关字段
            dto.setIsCommissionEmail(contactPerson.getIsCommissionEmail());
            dto.setIsNewsEmail(contactPerson.getIsNewsEmail());
            dto.setFkAreaCountryIdsNews(contactPerson.getFkAreaCountryIdsNews());
            dto.setRemark(contactPerson.getRemark());

            contactPersonDtoList.add(dto);
        }

        return contactPersonDtoList;
    }

    /**
     * 从代理构造合同账户列表
     * 参考报告第6章"合同账户转换：copyAgentContractAccountToAppAgent"
     * <p>
     * 转换逻辑：AgentContractAccount → AppAgentContractAccount
     * 字段映射：银行信息（账户名、账号、银行名称、支行名称）+ 币种信息 + 地址信息
     *
     * @param agentId 代理ID
     * @return 合同账户列表
     */
    private List<AppAgentContractAccountAddDto> buildContractAccountListFromAgent(Long agentId) {
        List<AgentContractAccount> contractAccounts = agentContractAccountService.list(
                Wrappers.<AgentContractAccount>lambdaQuery()
                        .eq(AgentContractAccount::getFkAgentId, agentId)
                        .eq(AgentContractAccount::getIsActive, true));

        if (CollectionUtil.isEmpty(contractAccounts)) {
            log.warn("代理ID: {} 没有找到合同账户信息", agentId);
            return new ArrayList<>();
        }

        log.info("代理ID: {} 查询到 {} 个合同账户", agentId, contractAccounts.size());

        // 转换为DTO
        List<AppAgentContractAccountAddDto> accountDtoList = new ArrayList<>();
        for (AgentContractAccount account : contractAccounts) {
            AppAgentContractAccountAddDto dto = new AppAgentContractAccountAddDto();
            dto.setFkCurrencyTypeNum(account.getFkCurrencyTypeNum());
            dto.setBankAccount(account.getBankAccount());
            dto.setBankAccountNum(account.getBankAccountNum());
            dto.setBankName(account.getBankName());
            dto.setBankBranchName(account.getBankBranchName());
            dto.setFkAreaCountryId(account.getFkAreaCountryId());
            dto.setFkAreaStateId(account.getFkAreaStateId());
            dto.setFkAreaCityId(account.getFkAreaCityId());
            dto.setFkAreaCityDivisionId(account.getFkAreaCityDivisionId());
            dto.setBankAddress(account.getBankAddress());
            dto.setBankCodeType(account.getBankCodeType());
            dto.setBankCode(account.getBankCode());
            dto.setAreaCountryCode(account.getAreaCountryCode());
            dto.setIsDefault(account.getIsDefault());
            dto.setRemark(account.getRemark());
            // 关联代理合同账户id
            dto.setFkAgentContractAccountId(account.getId());

            accountDtoList.add(dto);
        }

        return accountDtoList;
    }

    /**
     * 从代理构造附件列表
     * 参考报告第7章"附件数据转换：copyAgentAttachmentsToAppAgent"
     * <p>
     * 转换逻辑：SaleMediaAndAttached 表关联关系转换
     * 原关联关系: fkTableName="m_agent", fkTableId=agentId
     * 新关联关系: fkTableName="m_app_agent", fkTableId=appAgentId (数据回显时不涉及)
     *
     * @param agentId 代理ID
     * @return 附件列表
     */
    private List<MediaAndAttachedDto> buildAttachmentListFromAgent(Long agentId) {
        List<SaleMediaAndAttached> attachments = mediaAndAttachedService.list(
                Wrappers.<SaleMediaAndAttached>lambdaQuery()
                        .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_AGENT.key)
                        .eq(SaleMediaAndAttached::getFkTableId, agentId));

        if (CollectionUtil.isEmpty(attachments)) {
            log.warn("代理ID: {} 没有找到附件信息", agentId);
            return new ArrayList<>();
        }

        log.info("代理ID: {} 查询到 {} 个附件", agentId, attachments.size());

        // 转换为DTO
        List<MediaAndAttachedDto> attachmentDtoList = BeanCopyUtils.copyListProperties(attachments,
                MediaAndAttachedDto::new);

        // 通过fkFileGuid从文件中心获取完整文件信息 - 修复filePath、fileNameOrc、fileKey为null的问题
        List<String> guidList = attachments.stream()
                .map(SaleMediaAndAttached::getFkFileGuid)
                .filter(GeneralTool::isNotEmpty)
                .collect(Collectors.toList());

        if (GeneralTool.isNotEmpty(guidList)) {
            try {
                // 调用文件中心服务获取完整文件信息
                Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
                guidListWithTypeMap.put(LoggerModulesConsts.SALECENTER, guidList);
                Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);

                if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
                    List<FileDto> fileDtos = fileDtoResult.getData();

                    // 根据fileGuid匹配，填充完整文件信息
                    for (MediaAndAttachedDto attachmentDto : attachmentDtoList) {
                        String fileGuid = attachmentDto.getFkFileGuid();
                        if (GeneralTool.isNotEmpty(fileGuid)) {
                            fileDtos.stream()
                                    .filter(fileDto -> fileGuid.equals(fileDto.getFileGuid()))
                                    .findFirst()
                                    .ifPresent(fileDto -> {
                                        attachmentDto.setFilePath(fileDto.getFilePath());
                                        attachmentDto.setFileNameOrc(fileDto.getFileNameOrc());
                                        attachmentDto.setFileKey(fileDto.getFileKey());
                                    });
                        }
                        attachmentDto.setFkTableName(null);
                        attachmentDto.setFkTableId(null);
                        attachmentDto.setGmtModified(null);
                        attachmentDto.setGmtModifiedUser(null);
                    }
                    log.info("成功获取 {} 个附件的完整文件信息", fileDtos.size());
                } else {
                    log.warn("从文件中心获取文件信息失败，代理ID: {}, 错误: {}", agentId, fileDtoResult.getCode());
                }
            } catch (Exception e) {
                log.error("调用文件中心服务异常，代理ID: {}, 错误: {}", agentId, e.getMessage(), e);
            }
        }

        return attachmentDtoList;
    }

    /**
     * 判断是否为合同联系人类型
     * 检查联系人类型字符串中是否包含 'CONTACT_AGENT_CONTRACT'
     *
     * @param contactPersonTypeKey 联系人类型字符串（逗号分隔）
     * @return true表示是合同联系人，false表示不是
     */
    private boolean isContractContactPerson(String contactPersonTypeKey) {
        if (StringUtils.isBlank(contactPersonTypeKey)) {
            return false;
        }
        // 分割逗号分隔的类型字符串，检查是否包含合同联系人类型
        return Arrays.asList(contactPersonTypeKey.split(","))
                .stream()
                .map(String::trim)
                .anyMatch("CONTACT_AGENT_CONTRACT"::equals);
    }

    /**
     * 构建邮件发送上下文
     *
     * @param register 注册用户信息
     * @param password 用户密码（可为null）
     * @param staffId 员工ID
     * @param appAgentId 代理申请ID（用于生成二维码）
     * @param emailTemplateEnum 邮件模板类型
     * @return 邮件发送上下文
     */
    private EmailSendContext buildEmailSendContext(RegisterPartnerUserDto register, String password, Long staffId, Long appAgentId, EmailTemplateEnum emailTemplateEnum) {
        Long agentId = register.getAgentId();

        // 构建二维码路径（将在邮件处理组件中转换为实际二维码）
        String qrcodePath = MiniProgramPageEnum.LOGIN.getPath();

        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", register.getAgentName()); // 收件人姓名
        emailParams.put("name", register.getAgentName()); // 代理名称
        emailParams.put("account", register.getToEmail()); // 账号
        emailParams.put("password", password); // 密码
        emailParams.put("id", String.valueOf(agentId)); // ID参数，对应模板中的${id}
        emailParams.put("qrcode", qrcodePath); // 小程序二维码路径
        // 添加staffId参数用于语言配置
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.SALE_AGENT)
                .tableId(agentId)
                .recipient(register.getToEmail())
                .emailTemplate(emailTemplateEnum)
                .parameters(emailParams)
                .build();
    }


    /**
     * 发送代理申请提交邮件
     * 包括给ADMIN联系人和BD员工发送邮件通知
     *
     * @param addAppAgentContext 代理申请上下文
     */
    private void sendApplicationSubmittedEmails(AddAppAgentContext addAppAgentContext) {
        try {
            AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();
            Long agentId = addAppAgentContext.getFkAppAgentId();
            Long staffId = appAgentAddDto.getFkStaffId();
            
            List<EmailSendContext> emailContexts = new ArrayList<>();
            
            // 1. 发送邮件给ADMIN联系人
            sendApplicationSubmittedEmailsToAdmins(agentId, staffId, emailContexts);
            
            
            // 2. 给BD员工发送相同内容的邮件副本
            sendBdEmailCopiesForSubmitted(emailContexts, agentId, staffId);

            // 3. 批量发送邮件
            if (!emailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(emailContexts, agentId);
                log.info("代理申请提交邮件发送成功，代理ID: {}, 邮件数量: {}", agentId, emailContexts.size());
            }
            
        } catch (Exception e) {
            log.error("发送代理申请提交邮件异常", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
        }
    }

    /**
     * 发送代理申请提交邮件给ADMIN联系人
     *
     * @param agentId 代理ID
     * @param staffId 员工ID
     * @param emailContexts 邮件上下文列表
     */
    private void sendApplicationSubmittedEmailsToAdmins(Long agentId, Long staffId, List<EmailSendContext> emailContexts) {
        try {
            // 查询包含ADMIN类型的联系人（联系人类型Key是多值逗号隔开）
            List<AppAgentContactPerson> allContacts = appAgentContactPersonService.list(
                    Wrappers.<AppAgentContactPerson>lambdaQuery()
                            .eq(AppAgentContactPerson::getFkAppAgentId, agentId)
            );
            
            if (CollectionUtil.isEmpty(allContacts)) {
                log.warn("未找到任何联系人，代理ID: {}", agentId);
                return;
            }
            
            // 筛选包含ADMIN类型的联系人
            List<AppAgentContactPerson> adminContacts = allContacts.stream()
                    .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))
                    .collect(Collectors.toList());
            
            if (CollectionUtil.isEmpty(adminContacts)) {
                log.warn("未找到ADMIN类型联系人，代理ID: {}", agentId);
                return;
            }
            
            for (AppAgentContactPerson adminContact : adminContacts) {
                String[] emails = adminContact.getEmail().split("; ");
                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {
                    continue;
                }
                String email = emails[0];
                EmailSendContext emailContext = buildApplicationSubmittedEmailContext(
                        email,
                        agentId
                );
                emailContexts.add(emailContext);
                log.info("添加ADMIN联系人邮件上下文，收件人: {}, 代理ID: {}", email, agentId);
            }
            
        } catch (Exception e) {
            log.error("构建ADMIN联系人邮件上下文异常，代理ID: {}", agentId, e);
        }
    }


    /**
     * 根据申请来源类型发送不同的拒绝邮件
     *
     * @param appAgentVo 代理申请信息
     */
    private void sendRejectionEmailsByType(AppAgentVo appAgentVo) {
        try {
            // 先查询AppAgent实体获取appFrom字段
            AppAgent appAgent = getById(appAgentVo.getId());
            if (appAgent == null) {
                log.error("未找到代理申请信息，ID: {}", appAgentVo.getId());
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }

            // 判断是否为新类型申请来源
            if (!AgentAppFromEnum.isNewType(appAgent.getAppFrom())) {
                // 发送旧版拒绝邮件
                addRejectReminderTask(appAgentVo);
            } else {
                // 发送新版拒绝邮件（参照saveCommentAndSendRejectEmail的逻辑）
                sendNewTypeRejectionEmails(appAgentVo);
            }
        } catch (Exception e) {
            log.error("发送拒绝邮件异常，代理申请ID: {}", appAgentVo.getId(), e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_emil_send_fail"));
        }
    }

    /**
     * 发送新类型拒绝邮件
     * 参照saveCommentAndSendRejectEmail中的邮件发送逻辑
     *
     * @param appAgentVo 代理申请信息
     */
    private void sendNewTypeRejectionEmails(AppAgentVo appAgentVo) {
        Long appAgentId = appAgentVo.getId();
        Long staffId = appAgentVo.getFkStaffId();
        Integer appType = appAgentVo.getAppType();

        // 获取ADMIN类型的联系人
        List<AppAgentContactPerson> adminContactPersons = getAdminContactPersonsForRejection(appAgentId);

        // 获取BD员工信息
        StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);

        // 根据申请类型确定邮件模板
        EmailTemplateEnum emailTemplate;
        Long agentId = null; // 用于续约申请

        if (ObjectUtils.isNull(appType) || AgentAppTypeEnum.NEW_APPLICATION.getCode().equals(appType)) {
            // 新申请拒绝邮件
            emailTemplate = EmailTemplateEnum.AGENT_APPLICATION_REJECTED;
        } else {
            // 续约申请拒绝邮件
            emailTemplate = EmailTemplateEnum.AGENT_RENEWAL_REJECTED;

            // 查找关联的Agent
            Long fkAgentId = appAgentVo.getFkAgentId();
            if (fkAgentId == null) {
                log.warn("续约申请没有关联的代理ID，跳过邮件发送，代理申请ID: {}", appAgentId);
                return;
            }

            Agent agent = agentService.getAgentById(fkAgentId);
            if (agent == null) {
                log.warn("找不到关联的代理记录，跳过邮件发送，代理申请ID: {}, 代理ID: {}", appAgentId, fkAgentId);
                return;
            }

            agentId = agent.getId();
            log.info("续约拒绝邮件将使用代理ID: {} (从代理申请ID: {})", agentId, appAgentId);
        }

        // 发送拒绝邮件给所有收件人
        sendRejectionEmailsToAllRecipients(adminContactPersons, bdStaff, appAgentId, agentId, staffId, emailTemplate);
    }

    /**
     * 获取ADMIN类型的联系人（用于拒绝邮件发送）
     *
     * @param appAgentId 代理申请ID
     * @return ADMIN类型的联系人列表
     */
    private List<AppAgentContactPerson> getAdminContactPersonsForRejection(Long appAgentId) {
        // 查询所有联系人
        List<AppAgentContactPerson> allContactPersons = appAgentContactPersonService.list(
                Wrappers.<AppAgentContactPerson>lambdaQuery()
                        .eq(AppAgentContactPerson::getFkAppAgentId, appAgentId)
        );

        if (CollectionUtil.isEmpty(allContactPersons)) {
            log.info("代理申请没有关联的联系人，代理申请ID: {}", appAgentId);
            return new ArrayList<>();
        }

        // 筛选包含ADMIN类型的联系人
        List<AppAgentContactPerson> adminContacts = allContactPersons.stream()
                .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(adminContacts)) {
            log.info("代理申请没有ADMIN类型的联系人，代理申请ID: {}", appAgentId);
        } else {
            log.info("找到{}个ADMIN类型联系人，代理申请ID: {}", adminContacts.size(), appAgentId);
        }

        return adminContacts;
    }

    /**
     * 发送拒绝邮件给所有收件人（ADMIN联系人和BD员工）
     *
     * @param adminContactPersons ADMIN联系人列表
     * @param bdStaff BD员工信息
     * @param appAgentId 代理申请ID
     * @param agentId 代理ID（用于续约申请，可为null）
     * @param staffId 员工ID
     * @param emailTemplate 邮件模板
     */
    private void sendRejectionEmailsToAllRecipients(List<AppAgentContactPerson> adminContactPersons,
                                                   StaffVo bdStaff, Long appAgentId, Long agentId, Long staffId, EmailTemplateEnum emailTemplate) {
        try {
            List<EmailSendContext> emailContexts = new ArrayList<>();
            int adminCount = 0;

            // 构建ADMIN联系人邮件
            if (CollectionUtil.isNotEmpty(adminContactPersons)) {
                for (AppAgentContactPerson contactPerson : adminContactPersons) {
                    if (GeneralTool.isNotEmpty(contactPerson.getEmail())) {
                        EmailSendContext context = buildRejectionEmailContext(
                                contactPerson.getEmail(),
                                contactPerson.getName(),
                                appAgentId,
                                agentId,
                                staffId,
                                emailTemplate
                        );
                        emailContexts.add(context);
                        adminCount++;
                        log.debug("构建ADMIN联系人拒绝邮件上下文，收件人: {}, 代理申请ID: {}", contactPerson.getEmail(), appAgentId);
                    }
                }
            }


            // 给BD员工发送相同内容的邮件副本
            sendBdEmailCopiesForRejection(emailContexts, bdStaff, appAgentId, agentId);

            // 批量发送邮件
            if (!emailContexts.isEmpty()) {
                log.info("开始批量发送拒绝邮件，代理申请ID: {}, ADMIN联系人: {}个, 总计: {}个",
                        appAgentId, adminCount, emailContexts.size());
                emailSenderUtils.sendBatchEmails(emailContexts, appAgentId);
                log.info("成功批量发送拒绝邮件，代理申请ID: {}, 邮件数量: {}", appAgentId, emailContexts.size());
            } else {
                log.warn("没有有效的收件人，跳过邮件发送，代理申请ID: {}", appAgentId);
            }

        } catch (Exception e) {
            log.error("批量发送拒绝邮件异常，代理申请ID: {}", appAgentId, e);
            // 不抛异常，避免影响主流程
        }
    }

    /**
     * 构建拒绝邮件上下文
     *
     * @param recipientEmail 收件人邮箱
     * @param recipientName 收件人姓名
     * @param appAgentId 代理申请ID
     * @param agentId 代理ID（用于续约申请，可为null）
     * @param staffId 员工ID
     * @param emailTemplate 邮件模板
     * @return 邮件发送上下文
     */
    private EmailSendContext buildRejectionEmailContext(String recipientEmail, String recipientName,
                                                       Long appAgentId, Long agentId, Long staffId, EmailTemplateEnum emailTemplate) {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", recipientName); // 收件人姓名
        emailParams.put("name", recipientName); // 收件人姓名（与personalName相同）

        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 根据邮件模板类型选择使用的ID和构建二维码路径
        String idForEmail;
        String qrcodePath;

        if (EmailTemplateEnum.AGENT_APPLICATION_REJECTED.equals(emailTemplate)) {
            // 新申请拒绝邮件 - 使用加密的appAgentId（逻辑不变）
            String encryptedAppAgentId;
            try {
                encryptedAppAgentId = AESUtils.Encrypt(String.valueOf(appAgentId), AESConstant.AESKEY);
            } catch (Exception e) {
                log.error("代理申请ID加密失败", e);
                throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encryption_failed"));
            }
            idForEmail = encryptedAppAgentId;
            qrcodePath = MiniProgramPageEnum.NEW_APPAGENT_ADD.buildFullPath(URLUtil.encodeAll(encryptedAppAgentId));
        } else if (EmailTemplateEnum.AGENT_RENEWAL_REJECTED.equals(emailTemplate)) {
            // 续约拒绝邮件 - 使用agentId
            idForEmail = String.valueOf(agentId);
            qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(agentId.toString());
        } else {
            // 其他情况使用默认逻辑
            idForEmail = String.valueOf(appAgentId);
            qrcodePath = MiniProgramPageEnum.LOGIN.getPath();
        }

        emailParams.put("id", idForEmail);
        emailParams.put("qrcode", qrcodePath);

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.APP_AGENT)
                .tableId(appAgentId)
                .recipient(recipientEmail)
                .emailTemplate(emailTemplate)
                .parameters(emailParams)
                .build();
    }

    /**
     * 构建代理申请提交邮件上下文
     * AGENT_APPLICATION_SUBMITTED模板使用id和qrcode参数
     *
     * @param recipientEmail 收件人邮箱
     * @param appAgentId 代理申请ID
     * @return 邮件发送上下文
     */
    private EmailSendContext buildApplicationSubmittedEmailContext(String recipientEmail, Long appAgentId) {
        // 对appAgentId进行AES加密（仿照旧版邮件逻辑）
        String encryptedAppAgentId;
        try {
            encryptedAppAgentId = AESUtils.Encrypt(String.valueOf(appAgentId), AESConstant.AESKEY);
        } catch (Exception e) {
            log.error("代理申请ID加密失败", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("sign_encryption_failed"));
        }
        
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("id", encryptedAppAgentId);
        
        // 使用加密后的字符串直接构建qrcode路径
        String qrcodePath = MiniProgramPageEnum.NEW_APPAGENT_ADD.buildFullPath(URLUtil.encodeAll(encryptedAppAgentId));
        emailParams.put("qrcode", qrcodePath);

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.SALE_AGENT)
                .tableId(appAgentId)
                .recipient(recipientEmail)
                .emailTemplate(EmailTemplateEnum.AGENT_APPLICATION_SUBMITTED)
                .parameters(emailParams)
                .build();
    }

    /**
     * 给BD员工发送注册邮件副本
     *
     * @param hasAccountContexts 有账号邮件上下文列表
     * @param noAccountContexts 无账号邮件上下文列表
     * @param agentId 代理ID
     * @param appAgentId 代理申请ID
     * @param bdStaff BD员工信息
     */
    private void sendBdEmailCopiesForRegistration(List<EmailSendContext> hasAccountContexts, List<EmailSendContext> noAccountContexts, 
                                                Long agentId, Long appAgentId, StaffVo bdStaff) {
        try {
            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制有账号邮件给BD
            for (EmailSendContext originalContext : hasAccountContexts) {
                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 复制无账号邮件给BD
            for (EmailSendContext originalContext : noAccountContexts) {
                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, agentId);
                log.info("成功发送BD注册邮件副本，代理ID: {}, 邮件数量: {}", agentId, bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD注册邮件副本异常，代理ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }

    /**
     * 给BD员工发送申请提交邮件副本
     *
     * @param originalContexts 原始邮件上下文列表
     * @param agentId 代理ID
     * @param staffId BD员工ID
     */
    private void sendBdEmailCopiesForSubmitted(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {
        try {
            if (staffId == null) {
                log.warn("BD员工ID为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            // 获取BD员工信息
            StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);
            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制申请提交邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, agentId);
                log.info("成功发送BD申请提交邮件副本，代理ID: {}, 邮件数量: {}", agentId, bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD申请提交邮件副本异常，代理ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }

    /**
     * 给BD员工发送拒绝邮件副本
     *
     * @param originalContexts 原始邮件上下文列表
     * @param bdStaff BD员工信息
     * @param appAgentId 代理申请ID
     * @param agentId 代理ID
     */
    private void sendBdEmailCopiesForRejection(List<EmailSendContext> originalContexts, StaffVo bdStaff, Long appAgentId, Long agentId) {
        try {
            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工信息或邮箱为空，跳过BD邮件副本发送，代理申请ID: {}", appAgentId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制拒绝邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 发送BD邮件副本
            if (!bdEmailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(bdEmailContexts, appAgentId);
                log.info("成功发送BD拒绝邮件副本，代理申请ID: {}, 邮件数量: {}", appAgentId, bdEmailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送BD拒绝邮件副本异常，代理申请ID: {}, 错误: {}", appAgentId, e.getMessage());
        }
    }

    /**
     * 创建BD员工的邮件副本（AppAgent场景）
     *
     * @param originalContext 原始邮件上下文
     * @param bdStaff BD员工信息
     * @return BD员工邮件上下文
     */
    private EmailSendContext createBdEmailCopyForApp(EmailSendContext originalContext, StaffVo bdStaff) {
        // 完全复制原始邮件参数，不做任何修改
        Map<String, String> bdEmailParams = new HashMap<>(originalContext.getParameters());

        // 构建BD员工邮件上下文
        return EmailSendContext.builder()
                .projectKey(originalContext.getProjectKey())
                .tableName(originalContext.getTableName())
                .tableId(originalContext.getTableId())
                .recipient(bdStaff.getEmail()) // 只改变收件人邮箱
                .emailTemplate(originalContext.getEmailTemplate()) // 使用相同的邮件模板
                .parameters(bdEmailParams) // 参数完全不变
                .build();
    }

    /**
     * 判断联系人是否符合创建合同副本的条件（排除紧急联系人）
     * 支持多值联系人类型（逗号分隔）的处理
     *
     * @param fkContactPersonTypeKey 联系人类型标识（可能包含多个逗号分隔的类型）
     * @return true表示需要创建合同副本，false表示不需要
     */
    private boolean isEligibleForContractCopy(String fkContactPersonTypeKey) {
        if (StringUtils.isBlank(fkContactPersonTypeKey)) {
            return true;
        }
        // 检查是否包含紧急联系人代码，包含则排除
        return !fkContactPersonTypeKey.contains(ContactPersonTypeEnum.EMERGENCY.getCode());
    }

    /**
     * 为销售联系人创建对应的合同联系人副本（续约专用）
     *
     * @param saleContactPerson 销售联系人
     * @param agentId          代理ID
     * @param fkCompanyId      公司ID
     */
    private void createContractContactPersonCopy(SaleContactPerson saleContactPerson, Long agentId, Long fkCompanyId) {
        ContactPersonDto contractContactPersonDto = BeanCopyUtils.objClone(saleContactPerson, ContactPersonDto::new);
        contractContactPersonDto.setFkCompanyId(fkCompanyId);
        contractContactPersonDto.setFkTableName(TableEnum.SALE_AGENT.key);
        contractContactPersonDto.setFkTableId(agentId);
        contractContactPersonDto.setFkContactPersonTypeKey("CONTACT_AGENT_CONTRACT");
        contractContactPersonDto.setGmtModified(null);
        contractContactPersonDto.setGmtModifiedUser(null);
        
        agentService.addAgentContactPerson(contractContactPersonDto);
    }

    /**
     * 批量为销售联系人创建对应的合同联系人副本（续约专用）
     *
     * @param saleContactPersons 销售联系人列表
     * @param agentId           代理ID
     * @param fkCompanyId       公司ID
     */
    private void createContractContactPersonCopies(List<SaleContactPerson> saleContactPersons, Long agentId, Long fkCompanyId) {
        if (CollectionUtil.isEmpty(saleContactPersons)) {
            return;
        }
        
        for (SaleContactPerson saleContactPerson : saleContactPersons) {
            createContractContactPersonCopy(saleContactPerson, agentId, fkCompanyId);
        }
        
        log.debug("续约批量创建合同联系人副本成功，数量: {}", saleContactPersons.size());
    }

    /**
     * 发送续约申请提交邮件
     * 包括给ADMIN联系人和BD员工发送邮件通知
     * 参考addAppAgent中的sendApplicationSubmittedEmails方法逻辑
     *
     * @param addAppAgentContext 代理申请上下文
     */
    private void sendRenewalSubmittedEmails(AddAppAgentContext addAppAgentContext) {
        try {
            AppAgentAddDto appAgentAddDto = addAppAgentContext.getAppAgentAddVo();
            Long appAgentId = addAppAgentContext.getFkAppAgentId();
            Long agentId = appAgentAddDto.getFkAgentId(); // 使用agentId，不需要加密
            Long staffId = appAgentAddDto.getFkStaffId();

            List<EmailSendContext> emailContexts = new ArrayList<>();

            // 1. 发送邮件给ADMIN联系人
            sendRenewalSubmittedEmailsToAdmins(appAgentId, agentId, staffId, emailContexts);

            // 2. 给BD员工发送相同内容的邮件副本
            sendBdEmailCopiesForRenewalSubmitted(emailContexts, agentId, staffId);

            // 3. 批量发送邮件
            if (!emailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(emailContexts, agentId);
                log.info("成功发送续约申请提交邮件，代理ID: {}, 邮件数量: {}", agentId, emailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送续约申请提交邮件异常，申请ID: {}, 错误: {}", addAppAgentContext.getFkAppAgentId(), e.getMessage());
        }
    }

    /**
     * 发送续约申请提交邮件给ADMIN联系人
     * 参考sendApplicationSubmittedEmailsToAdmins方法逻辑
     *
     * @param appAgentId 代理申请ID
     * @param agentId 代理ID
     * @param staffId BD员工ID（用于邮件国际化）
     * @param emailContexts 邮件上下文列表
     */
    private void sendRenewalSubmittedEmailsToAdmins(Long appAgentId, Long agentId, Long staffId, List<EmailSendContext> emailContexts) {
        try {
            // 查询包含ADMIN类型的联系人（联系人类型Key是多值逗号隔开）
            List<AppAgentContactPerson> allContacts = appAgentContactPersonService.list(
                    Wrappers.<AppAgentContactPerson>lambdaQuery()
                            .eq(AppAgentContactPerson::getFkAppAgentId, appAgentId)
            );

            if (CollectionUtil.isEmpty(allContacts)) {
                log.warn("未找到任何联系人，代理申请ID: {}", appAgentId);
                return;
            }

            // 筛选包含ADMIN类型的联系人
            List<AppAgentContactPerson> adminContacts = allContacts.stream()
                    .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(adminContacts)) {
                log.warn("未找到ADMIN类型联系人，代理申请ID: {}", appAgentId);
                return;
            }

            for (AppAgentContactPerson adminContact : adminContacts) {
                String[] emails = adminContact.getEmail().split("; ");
                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {
                    continue;
                }
                String email = emails[0];
                EmailSendContext emailContext = buildRenewalSubmittedEmailContext(
                        email,
                        agentId, // 使用agentId，不需要加密
                        staffId  // 添加staffId用于邮件国际化
                );
                emailContexts.add(emailContext);
                log.info("添加ADMIN联系人续约邮件上下文，收件人: {}, 代理ID: {}", email, agentId);
            }

        } catch (Exception e) {
            log.error("构建ADMIN联系人续约邮件上下文异常，代理申请ID: {}", appAgentId, e);
        }
    }

    /**
     * 给BD员工发送续约申请提交邮件副本
     * 参考sendBdEmailCopiesForSubmitted方法逻辑
     *
     * @param originalContexts 原始邮件上下文列表
     * @param agentId 代理ID
     * @param staffId BD员工ID
     */
    private void sendBdEmailCopiesForRenewalSubmitted(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {
        try {
            if (staffId == null) {
                log.warn("BD员工ID为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            // 获取BD员工信息
            StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);
            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制续约申请提交邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 将BD邮件上下文添加到原始列表中
            originalContexts.addAll(bdEmailContexts);
            log.info("成功添加BD续约邮件副本，代理ID: {}, BD邮件数量: {}", agentId, bdEmailContexts.size());

        } catch (Exception e) {
            log.error("发送BD续约邮件副本异常，代理ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }

    /**
     * 构建续约申请提交邮件上下文
     * 参考buildApplicationSubmittedEmailContext方法，但使用AGENT_RENEWAL_SUBMITTED模板和RENEWAL_APPLY页面
     *
     * @param recipientEmail 收件人邮箱
     * @param agentId 代理ID（直接使用，不需要加密）
     * @param staffId BD员工ID（用于邮件国际化）
     * @return 邮件发送上下文
     */
    private EmailSendContext buildRenewalSubmittedEmailContext(String recipientEmail, Long agentId, Long staffId) {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("id", String.valueOf(agentId)); // 直接使用agentId，不需要加密

        // 使用RENEWAL_APPLY页面构建qrcode路径，拼接agentId
        String qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(agentId.toString());
        emailParams.put("qrcode", qrcodePath);

        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.SALE_AGENT)
                .tableId(agentId)
                .recipient(recipientEmail)
                .emailTemplate(EmailTemplateEnum.AGENT_RENEWAL_SUBMITTED) // 使用续约提交邮件模板
                .parameters(emailParams)
                .build();
    }

    /**
     * 续约审核拒绝修改并发送邮件
     *
     * @param appAgentApproveCommentDto 审批意见DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renewalRejectAndSendEmail(AppAgentApproveCommentDto appAgentApproveCommentDto) {
        log.info("开始处理续约审核拒绝并发送邮件，参数: {}", appAgentApproveCommentDto);

        // 参数校验
        if (ObjectUtils.isNull(appAgentApproveCommentDto)) {
            log.error("审批意见数据不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Long appAgentId = appAgentApproveCommentDto.getFkAppAgentId();
        if (ObjectUtils.isNull(appAgentId)) {
            log.error("代理申请ID不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        String approveComment = appAgentApproveCommentDto.getApproveComment();
        if (StringUtils.isBlank(approveComment)) {
            log.error("审批意见不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("approve_comment_null"));
        }

        // 1. 获取代理申请信息
        AppAgent appAgent = this.getById(appAgentId);
        if (ObjectUtils.isNull(appAgent)) {
            log.error("代理申请数据不存在，ID: {}", appAgentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_app_data_error"));
        }

        // 1.1. 检查申请状态，只有审核中状态才能执行拒绝操作
        Integer currentStatus = appAgent.getAppStatus();
        if (!ProjectExtraEnum.APP_STATUS_REVIEW.key.equals(currentStatus)) {
            log.error("代理申请状态不是审核中，无法执行拒绝操作，当前状态: {}, AppAgentId: {}", currentStatus, appAgentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("app_status_not_review"));
        }
        log.info("代理申请状态检查通过，当前状态: 审核中，AppAgentId: {}", appAgentId);

        // 获取关联的代理ID
        Long agentId = appAgent.getFkAgentId();
        if (ObjectUtils.isNull(agentId)) {
            log.error("代理申请对应的代理ID为空，无法发送邮件，AppAgentId: {}", appAgentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
        }

        // 2. 更新代理申请状态为拒绝
        appAgent.setAppStatus(ProjectExtraEnum.APP_STATUS_REJECT.key);
        utilService.setUpdateInfo(appAgent);
        boolean updateResult = this.updateById(appAgent);
        if (!updateResult) {
            log.error("更新代理申请状态失败，AppAgentId: {}", appAgentId);
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        log.info("成功更新代理申请状态为拒绝，AppAgentId: {}", appAgentId);

        // 3. 保存审批意见（参考AppAgentApproveCommentServiceImpl的saveAndSendEmail方法）
        Long appAgentApproveCommentId = appAgentApproveCommentService.addAppAgentApproveCommentVo(appAgentApproveCommentDto);
        log.info("成功保存续约拒绝审批意见，AppAgentId: {}, CommentId: {}", appAgentId, appAgentApproveCommentId);

        // 4. 发送拒绝邮件给admin和BD
        sendRenewalRejectionEmails(appAgentId, agentId, appAgent.getFkStaffId(), approveComment);

        // 5. 更新审批意见的邮件发送时间和人
        updateAppAgentApproveCommentEmailTime(appAgentApproveCommentId);

        log.info("续约审核拒绝处理完成，AppAgentId: {}, AgentId: {}", appAgentId, agentId);
    }

    /**
     * 发送续约拒绝邮件给ADMIN联系人和BD员工
     *
     * @param appAgentId 代理申请ID
     * @param agentId 代理ID
     * @param staffId BD员工ID
     * @param rejectMessage 拒绝原因
     */
    private void sendRenewalRejectionEmails(Long appAgentId, Long agentId, Long staffId, String rejectMessage) {
        try {
            List<EmailSendContext> emailContexts = new ArrayList<>();

            // 1. 发送邮件给ADMIN联系人
            sendRenewalRejectionEmailsToAdmins(appAgentId, agentId, rejectMessage, staffId, emailContexts);

            // 2. 给BD员工发送相同内容的邮件
            sendBdEmailCopiesForRenewalRejection(emailContexts, agentId, staffId);

            // 3. 批量发送邮件
            if (!emailContexts.isEmpty()) {
                emailSenderUtils.sendBatchEmails(emailContexts, agentId);
                log.info("成功发送续约拒绝邮件，代理ID: {}, 邮件数量: {}", agentId, emailContexts.size());
            }

        } catch (Exception e) {
            log.error("发送续约拒绝邮件异常，代理申请ID: {}, 代理ID: {}, 错误: {}", appAgentId, agentId, e.getMessage());
        }
    }

    /**
     * 发送续约拒绝邮件给ADMIN联系人
     *
     * @param appAgentId 代理申请ID
     * @param agentId 代理ID
     * @param rejectMessage 拒绝原因
     * @param staffId BD员工ID（用于邮件国际化）
     * @param emailContexts 邮件上下文列表
     */
    private void sendRenewalRejectionEmailsToAdmins(Long appAgentId, Long agentId, String rejectMessage, Long staffId, List<EmailSendContext> emailContexts) {
        try {
            // 查询包含ADMIN类型的联系人
            List<AppAgentContactPerson> allContacts = appAgentContactPersonService.list(
                    Wrappers.<AppAgentContactPerson>lambdaQuery()
                            .eq(AppAgentContactPerson::getFkAppAgentId, appAgentId)
            );

            if (CollectionUtil.isEmpty(allContacts)) {
                log.warn("未找到任何联系人，代理申请ID: {}", appAgentId);
                return;
            }

            // 筛选包含ADMIN类型的联系人
            List<AppAgentContactPerson> adminContacts = allContacts.stream()
                    .filter(contact -> isEligibleContactPerson(contact, ContactPersonTypeEnum.ADMIN))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(adminContacts)) {
                log.warn("未找到ADMIN类型联系人，代理申请ID: {}", appAgentId);
                return;
            }

            for (AppAgentContactPerson adminContact : adminContacts) {
                String[] emails = adminContact.getEmail().split("; ");
                if (ArrayUtil.isEmpty(emails) || StringUtils.isBlank(emails[0])) {
                    continue;
                }
                String email = emails[0];
                String contactName = adminContact.getName();

                EmailSendContext emailContext = buildRenewalRejectionEmailContext(
                        email,
                        contactName,
                        agentId,
                        rejectMessage,
                        staffId  // 添加staffId用于邮件国际化
                );
                emailContexts.add(emailContext);
                log.info("添加ADMIN联系人续约拒绝邮件上下文，收件人: {}, 姓名: {}, 代理ID: {}", email, contactName, agentId);
            }

        } catch (Exception e) {
            log.error("构建ADMIN联系人续约拒绝邮件上下文异常，代理申请ID: {}", appAgentId, e);
        }
    }

    /**
     * 给BD员工发送续约拒绝邮件副本
     *
     * @param originalContexts 原始邮件上下文列表
     * @param agentId 代理ID
     * @param staffId BD员工ID
     */
    private void sendBdEmailCopiesForRenewalRejection(List<EmailSendContext> originalContexts, Long agentId, Long staffId) {
        try {
            if (staffId == null) {
                log.warn("BD员工ID为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            // 获取BD员工信息
            StaffVo bdStaff = getBdStaffInfoHasEmail(staffId);
            if (bdStaff == null || StringUtils.isBlank(bdStaff.getEmail())) {
                log.warn("BD员工信息或邮箱为空，跳过BD邮件副本发送，代理ID: {}", agentId);
                return;
            }

            List<EmailSendContext> bdEmailContexts = new ArrayList<>();

            // 复制续约拒绝邮件给BD
            for (EmailSendContext originalContext : originalContexts) {
                EmailSendContext bdContext = createBdEmailCopyForApp(originalContext, bdStaff);
                bdEmailContexts.add(bdContext);
            }

            // 将BD邮件上下文添加到原始列表中
            originalContexts.addAll(bdEmailContexts);
            log.info("成功添加BD续约拒绝邮件副本，代理ID: {}, BD邮件数量: {}", agentId, bdEmailContexts.size());

        } catch (Exception e) {
            log.error("发送BD续约拒绝邮件副本异常，代理ID: {}, 错误: {}", agentId, e.getMessage());
        }
    }

    /**
     * 构建续约拒绝邮件上下文
     * 参考AgentServiceImpl中的buildRenewalEmailContext方法
     *
     * @param recipientEmail 收件人邮箱
     * @param recipientName 收件人姓名
     * @param agentId 代理ID
     * @param rejectMessage 拒绝原因
     * @param staffId BD员工ID（用于邮件国际化）
     * @return 邮件发送上下文
     */
    private EmailSendContext buildRenewalRejectionEmailContext(String recipientEmail, String recipientName, Long agentId, String rejectMessage, Long staffId) {
        // 生成或获取renewalToken（参考AgentServiceImpl的buildRenewalEmailContext方法）
        String tokenKey = CacheKeyConstants.SALE_AGENT_RENEWAL_PREFIX + agentId;
        String token = this.getRedis.get(tokenKey);
        if (StringUtils.isBlank(token)) {
            token = UUID.randomUUID().toString().replace("-", "");
            this.getRedis.set(tokenKey, token);
        }

        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", recipientName); // 收件人姓名
        emailParams.put("name", recipientName); // name 和 personalName取同一个值，就是admin的名字
        emailParams.put("id", String.valueOf(agentId)); // 代理ID
        emailParams.put("rejectMessage", rejectMessage); // 拒绝原因（就是approveComment）
        emailParams.put("renewalToken", token); // 续约token

        // 构建二维码路径（参考AgentServiceImpl的buildRenewalEmailContext方法）
        String qrcodePath = MiniProgramPageEnum.RENEWAL_APPLY.buildFullPath(agentId.toString());
        if (StringUtils.isNotBlank(token)) {
            qrcodePath = qrcodePath + "&renewalToken=" + token;
        }
        emailParams.put("qrcode", qrcodePath);

        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.SALE_AGENT)
                .tableId(agentId)
                .recipient(recipientEmail)
                .emailTemplate(EmailTemplateEnum.AGENT_RENEWAL_REJECTED_WITH_MSG) // 使用带审批意见的续约拒绝邮件模板
                .parameters(emailParams)
                .build();
    }

    /**
     * 更新审批意见的邮件发送时间和人
     *
     * @param appAgentApproveCommentId 审批意见ID
     */
    private void updateAppAgentApproveCommentEmailTime(Long appAgentApproveCommentId) {
        try {
            AppAgentApproveComment appAgentApproveComment = appAgentApproveCommentService.getById(appAgentApproveCommentId);
            if (appAgentApproveComment == null) {
                log.error("审批意见不存在，ID: {}", appAgentApproveCommentId);
                return;
            }

            // 更新邮件发送时间和人
            appAgentApproveComment.setEmailTime(new Date());
            utilService.setUpdateInfo(appAgentApproveComment);
            boolean updateFlag = appAgentApproveCommentService.updateById(appAgentApproveComment);

            if (updateFlag) {
                log.info("成功更新审批意见邮件发送时间，ID: {}", appAgentApproveCommentId);
            } else {
                log.error("更新审批意见邮件发送时间失败，ID: {}", appAgentApproveCommentId);
            }

        } catch (Exception e) {
            log.error("更新审批意见邮件发送时间异常，ID: {}, 错误: {}", appAgentApproveCommentId, e.getMessage());
        }
    }

}
