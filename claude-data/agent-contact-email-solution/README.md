# 代理申请联系人邮件模板和账号创建解决方案

## 📋 项目概述

本方案为HTI Java AIS v1系统实现代理申请审核通过时，根据不同联系人类型发送对应邮件模板并创建相应小程序账号的功能。

## 🎯 业务需求

### 联系人类型处理策略
- **ADMIN类型（企业负责人/小程序管理员）**：代理申请审核通过时发送邀请邮件 + 创建小程序账号
- **COMMISSION类型（佣金结算负责人）**：合同审核通过时发送邮件 + 创建账号（预留功能）
- **EMERGENCY类型（紧急联系人）**：仅发送通知邮件，不创建账号

## 📁 文件结构

```
claude-data/agent-contact-email-solution/
├── README.md                          # 项目说明文档
├── docs/                              # 文档目录
│   └── 技术方案设计文档.md               # 详细技术方案
├── code/                              # 代码文件
│   ├── MailTemplateTypeEnum.java       # 邮件模板枚举扩展
│   └── AppAgentServiceImpl_代理审核逻辑修改.java  # 审核逻辑代码
├── sql/                               # 数据库脚本
│   └── 邮件模板插入脚本.sql             # 邮件模板数据插入
└── tests/                             # 测试文档
    └── 测试验证方案.md                  # 完整测试方案
```

## 🚀 快速开始

### 1. 查看技术方案
阅读 `docs/技术方案设计文档.md` 了解完整的技术实现方案。

### 2. 数据库准备
执行 `sql/邮件模板插入脚本.sql` 创建所需的邮件模板。

### 3. 代码修改
参考 `code/` 目录下的文件进行代码修改：
- 扩展 `MailTemplateTypeEnum.java` 枚举
- 修改 `AppAgentServiceImpl.java` 审核逻辑

### 4. 测试验证
按照 `tests/测试验证方案.md` 进行功能测试。

## 🔧 技术要点

### 主要修改文件
1. **MailTemplateTypeEnum.java**
   - 路径：`biz-service-ap/ais-partner-center-ap/src/main/java/com/get/partnercenter/enums/MailTemplateTypeEnum.java`
   - 新增：3种邮件模板类型枚举

2. **AppAgentServiceImpl.java**
   - 路径：`biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java`
   - 修改：`doSetAgree()` 方法，添加联系人处理逻辑

3. **数据库表：u_mail_template**
   - 插入：3条新的邮件模板记录

### 集成方式
- 复用现有的 `/partnerUser/sendInviteMail` 接口
- 通过Feign Client调用邮件发送服务
- 保持与现有系统的完全兼容

## 📊 实施计划

### 开发阶段
1. ✅ 技术方案设计
2. ✅ 代码结构准备
3. ✅ 数据库脚本编写
4. ✅ 测试方案制定

### 执行阶段
1. 📋 代码修改实施
2. 📋 数据库脚本执行
3. 📋 功能测试验证
4. 📋 用户验收测试
5. 📋 生产环境部署

## ⚠️ 重要提醒

### 数据库连接
- **Host**: localhost:3316
- **Username**: root  
- **Password**: fzhmysql 或 aa545554555..

### 安全考虑
- 邮件模板变量：`{account}` 和 `{password}`
- 账号创建采用随机密码
- 首次登录建议修改密码

### 兼容性
- 保持现有代理审核流程不变
- 仅对新申请类型（AgentAppFromEnum.NEW_TYPE）生效
- 向后兼容老版本申请

## 🔍 验证检查

### 功能验证
- [ ] ADMIN类型联系人收到邮件并获得账号
- [ ] EMERGENCY类型联系人收到通知邮件
- [ ] COMMISSION类型暂不处理（符合预期）
- [ ] 邮件模板格式正确
- [ ] 账号可正常登录

### 系统验证
- [ ] 原有功能不受影响
- [ ] 异常处理机制完善
- [ ] 日志记录完整
- [ ] 性能影响可控

## 📞 支持联系

如有任何问题或需要技术支持，请：
1. 查看 `docs/技术方案设计文档.md` 的详细说明
2. 参考 `tests/测试验证方案.md` 的测试步骤
3. 检查代码实现是否与提供的示例一致

---

**版本**：v1.0  
**创建时间**：2025-07-07  
**创建人**：Claude Code Assistant  
**项目状态**：方案设计完成，待实施