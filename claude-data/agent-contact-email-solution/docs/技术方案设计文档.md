# 代理申请联系人邮件模板和账号创建技术方案

## 1. 需求概述

### 1.1 业务背景
在HTI Java AIS v1系统中，代理申请审核通过时需要根据不同的联系人类型发送对应的邮件模板并创建相应的小程序账号。

### 1.2 联系人类型及处理策略
- **ADMIN类型（企业负责人/小程序管理员）**：代理申请审核通过时发送邀请邮件 + 创建小程序账号
- **COMMISSION类型（佣金结算负责人）**：合同审核通过时发送邮件 + 创建账号（预留功能）
- **EMERGENCY类型（紧急联系人）**：仅发送通知邮件，不创建账号

## 2. 现有系统分析

### 2.1 相关代码文件
1. **代理申请审核逻辑**：`AppAgentServiceImpl.java`
   - 文件路径：`/mnt/e/ideaworkspace-work-1/hti-java-ais-v1/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java`
   - 关键方法：`doSetAgree()` (第891行)

2. **联系人类型枚举**：`ContactPersonTypeEnum.java`
   - 文件路径：`/mnt/e/ideaworkspace-work-1/hti-java-ais-v1/biz-service/ais-sale-center/src/main/java/com/get/salecenter/enums/ContactPersonTypeEnum.java`
   - 包含：ADMIN、COMMISSION、EMERGENCY 三种新类型

3. **邮件模板系统**：`PartnerUserServiceImpl.java`
   - 文件路径：`/mnt/e/ideaworkspace-work-1/hti-java-ais-v1/biz-service/ais-partner-center/src/main/java/com/get/partnercenter/service/impl/PartnerUserServiceImpl.java`
   - 参考接口：`/partnerUser/sendInviteMail`
   - 关键方法：`sendMail()` (第368行)

### 2.2 邮件模板枚举
- 文件路径：`/mnt/e/ideaworkspace-work-1/hti-java-ais-v1/biz-service-ap/ais-partner-center-ap/src/main/java/com/get/partnercenter/enums/MailTemplateTypeEnum.java`
- 现有类型：
  - `INVITE_TO_REGISTER` (type=1) - 邀请邮件模板
  - `RESET_PASSWORD` (type=2) - 密码重置邮件模板

### 2.3 邮件模板数据库表
- 表名：`u_mail_template`
- 关键字段：
  - `typeKey` - 类型键值
  - `title` - 邮件标题
  - `emailTemplate` - 中文邮件模板
  - `emailTemplateEn` - 英文邮件模板
  - `remark` - 备注

## 3. 技术实施方案

### 3.1 扩展邮件模板枚举
**修改文件**：`MailTemplateTypeEnum.java`

**新增枚举值**：
```java
AGENT_ADMIN_INVITE("AGENT_ADMIN_INVITE", "代理管理员邀请邮件", 3),
AGENT_COMMISSION_INVITE("AGENT_COMMISSION_INVITE", "佣金负责人邀请邮件", 4), 
AGENT_EMERGENCY_NOTIFY("AGENT_EMERGENCY_NOTIFY", "紧急联系人通知邮件", 5);
```

### 3.2 数据库邮件模板
**需要插入的模板记录**：
1. **AGENT_ADMIN_INVITE** - 包含账号密码的管理员邀请邮件
2. **AGENT_COMMISSION_INVITE** - 包含账号密码的佣金负责人邀请邮件  
3. **AGENT_EMERGENCY_NOTIFY** - 仅通知的紧急联系人邮件（无账号信息）

### 3.3 修改代理申请审核逻辑
**修改文件**：`AppAgentServiceImpl.java`
**修改位置**：`doSetAgree()` 方法（第891行）

**新增处理逻辑**：
1. 在现有代理同步完成后，调用新的联系人处理方法
2. 获取所有新类型联系人（ADMIN、COMMISSION、EMERGENCY）
3. 根据联系人类型调用不同的邮件发送逻辑

### 3.4 复用现有邮件服务
**方式**：通过Feign Client调用partner-center的sendInviteMail接口
- 扩展该接口支持新的邮件模板类型
- 确保ADMIN类型会创建账号，EMERGENCY类型不创建账号

## 4. 具体实施步骤

### 步骤1：扩展邮件模板枚举
1. 修改 `MailTemplateTypeEnum.java`
2. 添加三种新的邮件模板类型

### 步骤2：插入邮件模板数据
1. 连接数据库
2. 向 `u_mail_template` 表插入新的邮件模板记录

### 步骤3：修改代理审核逻辑
1. 在 `AppAgentServiceImpl.doSetAgree()` 方法中添加新的联系人处理逻辑
2. 创建新的方法 `processNewContactPersons()`

### 步骤4：集成邮件发送服务
1. 调用partner-center的邮件发送接口
2. 根据联系人类型选择对应的邮件模板

### 步骤5：测试验证
1. 创建测试用例
2. 验证不同联系人类型的邮件发送和账号创建

## 5. 关键技术点

### 5.1 事务控制
- 使用 `@DSTransactional` 注解确保数据一致性
- 邮件发送失败时不影响代理审核流程

### 5.2 邮件模板变量替换
- 支持 `{account}` 和 `{password}` 变量
- 根据联系人信息动态替换邮件内容

### 5.3 账号创建逻辑
- 复用现有的 `savePartnerUser()` 方法
- 根据联系人邮箱和姓名创建对应账号

### 5.4 向后兼容
- 保持现有代理审核流程不变
- 新功能仅对新申请类型生效

## 6. 预期效果

1. **ADMIN类型联系人**：在代理申请通过时收到邀请邮件并获得小程序账号
2. **EMERGENCY类型联系人**：收到通知邮件但不获得账号
3. **COMMISSION类型联系人**：等待合同审核通过后处理
4. 每种类型使用专门的邮件模板，内容更精准
5. 邮件发送和账号创建过程可追踪、可回滚

## 7. 风险评估

### 7.1 技术风险
- **邮件发送失败**：不影响代理审核主流程
- **账号创建失败**：可通过重新发送邮件恢复

### 7.2 业务风险
- **重复发送邮件**：通过数据库记录避免
- **账号重复创建**：现有系统已有唯一性校验

## 8. 后续扩展

### 8.1 合同审核功能
为COMMISSION类型联系人预留合同审核通过时的邮件发送和账号创建功能。

### 8.2 邮件模板管理
可考虑增加邮件模板的在线编辑和预览功能。

---

**文档版本**：v1.0  
**创建时间**：2025-07-07  
**创建人**：Claude Code Assistant