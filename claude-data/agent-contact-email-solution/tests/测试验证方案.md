# 代理联系人邮件模板和账号创建 - 测试验证方案

## 1. 测试环境准备

### 1.1 数据库环境
- **数据库服务器**：localhost:3316
- **测试数据库**：确认使用正确的数据库名
- **邮件模板表**：u_mail_template

### 1.2 应用服务
- **sales-center服务**：代理申请审核功能
- **partner-center服务**：邮件发送功能
- **registration-center服务**：用户账号创建

### 1.3 测试数据准备
1. 创建测试代理申请记录
2. 添加三种类型的联系人：
   - ADMIN类型联系人
   - COMMISSION类型联系人
   - EMERGENCY类型联系人

## 2. 单元测试用例

### 2.1 邮件模板枚举测试
**测试文件**：`MailTemplateTypeEnumTest.java`

```java
@Test
public void testNewMailTemplateTypes() {
    // 测试新增的邮件模板类型
    assertEquals("AGENT_ADMIN_INVITE", MailTemplateTypeEnum.AGENT_ADMIN_INVITE.getCode());
    assertEquals("AGENT_COMMISSION_INVITE", MailTemplateTypeEnum.AGENT_COMMISSION_INVITE.getCode());
    assertEquals("AGENT_EMERGENCY_NOTIFY", MailTemplateTypeEnum.AGENT_EMERGENCY_NOTIFY.getCode());
    
    // 测试通过type获取枚举
    assertEquals(MailTemplateTypeEnum.AGENT_ADMIN_INVITE, MailTemplateTypeEnum.getEnumByType(3));
    assertEquals(MailTemplateTypeEnum.AGENT_COMMISSION_INVITE, MailTemplateTypeEnum.getEnumByType(4));
    assertEquals(MailTemplateTypeEnum.AGENT_EMERGENCY_NOTIFY, MailTemplateTypeEnum.getEnumByType(5));
}
```

### 2.2 数据库邮件模板测试
**测试SQL**：

```sql
-- 验证邮件模板是否正确插入
SELECT 
    type_key,
    title,
    CASE 
        WHEN email_template IS NOT NULL THEN '有内容'
        ELSE '无内容'
    END as template_status,
    CASE 
        WHEN email_template_en IS NOT NULL THEN '有内容'
        ELSE '无内容'
    END as template_en_status
FROM u_mail_template 
WHERE type_key IN ('AGENT_ADMIN_INVITE', 'AGENT_COMMISSION_INVITE', 'AGENT_EMERGENCY_NOTIFY');

-- 预期结果：3条记录，均有中英文模板内容
```

### 2.3 联系人处理逻辑测试
**测试类**：`AppAgentServiceTest.java`

```java
@Test
public void testProcessNewContactPersons() {
    // 准备测试数据
    AppAgentSetAgreeContext context = createTestContext();
    
    // 模拟代理审核通过
    appAgentService.doSetAgree(context.getAppAgentDto());
    
    // 验证邮件发送记录
    // 验证账号创建记录（仅ADMIN类型）
    // 验证EMERGENCY类型没有创建账号
}
```

## 3. 集成测试方案

### 3.1 完整流程测试
**测试步骤**：

1. **创建代理申请**
   ```bash
   POST /sale/appAgent/add
   {
     "appFrom": "NEW_APP", 
     "appAgentContactPersonAddVos": [
       {
         "name": "张三",
         "email": "<EMAIL>",
         "fkContactPersonTypeKey": "ADMIN"
       },
       {
         "name": "李四", 
         "email": "<EMAIL>",
         "fkContactPersonTypeKey": "COMMISSION"
       },
       {
         "name": "王五",
         "email": "<EMAIL>", 
         "fkContactPersonTypeKey": "EMERGENCY"
       }
     ]
   }
   ```

2. **审核申请通过**
   ```bash
   POST /sale/appAgent/updateAppStatus
   {
     "id": 申请ID,
     "appStatus": 2  // 审核通过
   }
   ```

3. **验证结果**
   - 检查邮件发送记录表
   - 检查用户账号创建记录
   - 验证邮件内容是否正确

### 3.2 邮件发送测试
**验证点**：

1. **ADMIN类型邮件**
   - ✅ 邮件标题：代理申请审核通过 - 小程序账号开通通知
   - ✅ 邮件内容包含账号和密码
   - ✅ 创建了对应的用户账号
   - ✅ 邮件发送状态为成功

2. **COMMISSION类型处理**
   - ✅ 暂时不发送邮件（等待合同审核）
   - ✅ 暂时不创建账号
   - ✅ 日志记录处理状态

3. **EMERGENCY类型邮件**
   - ✅ 邮件标题：代理申请审核通过通知
   - ✅ 邮件内容仅为通知，无账号信息
   - ✅ 没有创建用户账号
   - ✅ 邮件发送状态为成功

### 3.3 异常场景测试

1. **邮件发送失败**
   - 断网环境下的邮件发送
   - 验证主流程不受影响
   - 检查错误日志记录

2. **账号创建失败**
   - 重复邮箱的账号创建
   - 验证错误处理机制

3. **模板不存在**
   - 删除邮件模板后的处理
   - 验证异常提示信息

## 4. 性能测试

### 4.1 批量处理测试
- 同时处理100个代理申请审核
- 验证邮件发送性能
- 验证系统资源占用

### 4.2 并发测试
- 并发审核多个代理申请
- 验证数据一致性
- 验证邮件发送的并发处理

## 5. 用户验收测试

### 5.1 邮件接收验证
**测试账号**：
- <EMAIL>（ADMIN类型）
- <EMAIL>（EMERGENCY类型）

**验证内容**：
1. 邮件是否正常接收
2. 邮件格式是否正确显示
3. 账号密码是否可正常登录（仅ADMIN类型）
4. 中英文邮件模板切换

### 5.2 小程序登录测试
**测试步骤**：
1. 使用邮件中的账号密码登录小程序
2. 验证登录成功
3. 验证账号权限正确
4. 测试密码修改功能

## 6. 回归测试

### 6.1 现有功能验证
- 原有代理申请流程不受影响
- 原有邮件发送功能正常
- 原有账号创建功能正常

### 6.2 兼容性测试
- 老版本申请的处理
- 新老联系人类型的兼容
- 数据库升级的兼容性

## 7. 测试数据清理

### 7.1 测试后清理
```sql
-- 清理测试邮件记录
DELETE FROM mail_log WHERE subject LIKE '%测试%';

-- 清理测试用户账号
DELETE FROM users WHERE email LIKE '%test.com';

-- 清理测试代理申请
DELETE FROM app_agent WHERE name LIKE '%测试%';
```

## 8. 验收标准

### 8.1 功能验收标准
- ✅ ADMIN类型联系人收到邀请邮件并获得账号
- ✅ EMERGENCY类型联系人收到通知邮件但无账号
- ✅ COMMISSION类型联系人暂不处理（符合预期）
- ✅ 邮件模板内容正确，中英文版本完整
- ✅ 账号创建成功，可正常登录

### 8.2 性能验收标准
- ✅ 单个代理审核处理时间 < 30秒
- ✅ 邮件发送成功率 > 95%
- ✅ 系统响应时间无明显增加

### 8.3 稳定性验收标准
- ✅ 异常情况下主流程不受影响
- ✅ 邮件发送失败有适当的重试机制
- ✅ 完整的日志记录和错误追踪

## 9. 测试时间计划

| 测试阶段 | 预计时间 | 责任人 |
|---------|---------|--------|
| 单元测试 | 1天 | 开发人员 |
| 集成测试 | 2天 | 测试人员 |
| 性能测试 | 1天 | 测试人员 |
| 用户验收测试 | 1天 | 产品经理 |
| 回归测试 | 1天 | 测试人员 |

**总计**：6个工作日

---

**文档版本**：v1.0  
**创建时间**：2025-07-07  
**测试负责人**：待定