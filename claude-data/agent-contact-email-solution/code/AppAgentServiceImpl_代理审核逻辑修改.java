/**
 * AppAgentServiceImpl.java 代理审核逻辑修改
 * 
 * 修改位置：doSetAgree() 方法，在第936行后新增联系人处理逻辑
 * 
 * 文件路径：/mnt/e/ideaworkspace-work-1/hti-java-ais-v1/biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AppAgentServiceImpl.java
 */

// 在 doSetAgree() 方法的第936行后添加以下代码：

// 若是奖学金进来的，要在issue库新增关系表和更新issue学生代理id
doUpdateIssueStudentAndAgent(appAgentSetAgreeContext);

// 【新增】处理新类型联系人的邮件发送和账号创建
processNewContactPersons(appAgentSetAgreeContext);

// 【新增方法】处理新类型联系人
/**
 * 处理新类型联系人的邮件发送和账号创建
 * 
 * @param appAgentSetAgreeContext 代理审核上下文
 */
private void processNewContactPersons(AppAgentSetAgreeContext appAgentSetAgreeContext) {
    AppAgentVo appAgentVo = appAgentSetAgreeContext.getAppAgentDto();
    Long fkAgentId = appAgentSetAgreeContext.getAgentId();
    Long fkCompanyId = appAgentSetAgreeContext.getFkCompanyId();
    
    // 只处理新版申请
    if (!AgentAppFromEnum.isNewType(appAgentVo.getAppFrom())) {
        return;
    }
    
    // 获取所有联系人
    List<AppAgentContactPerson> contactPersons = appAgentContactPersonService.list(
        Wrappers.lambdaQuery(AppAgentContactPerson.class)
            .eq(AppAgentContactPerson::getFkAppAgentId, appAgentVo.getId())
    );
    
    if (GeneralTool.isEmpty(contactPersons)) {
        return;
    }
    
    // 处理每个联系人
    for (AppAgentContactPerson contactPerson : contactPersons) {
        String contactPersonType = contactPerson.getFkContactPersonTypeKey();
        
        // 根据联系人类型进行不同处理
        if (ContactPersonTypeEnum.ADMIN.getCode().equals(contactPersonType)) {
            // ADMIN类型：发送邀请邮件 + 创建账号
            sendContactPersonInviteEmail(contactPerson, fkAgentId, fkCompanyId, 3); // type=3 对应 AGENT_ADMIN_INVITE
            
        } else if (ContactPersonTypeEnum.EMERGENCY.getCode().equals(contactPersonType)) {
            // EMERGENCY类型：仅发送通知邮件，不创建账号
            sendContactPersonNotifyEmail(contactPerson, fkAgentId, fkCompanyId, 5); // type=5 对应 AGENT_EMERGENCY_NOTIFY
            
        } else if (ContactPersonTypeEnum.COMMISSION.getCode().equals(contactPersonType)) {
            // COMMISSION类型：暂时不处理，等待合同审核通过
            log.info("COMMISSION类型联系人暂不处理，等待合同审核通过。联系人ID：{}", contactPerson.getId());
        }
    }
}

/**
 * 发送联系人邀请邮件（包含账号创建）
 * 
 * @param contactPerson 联系人信息
 * @param fkAgentId 代理ID
 * @param fkCompanyId 公司ID
 * @param emailType 邮件类型
 */
private void sendContactPersonInviteEmail(AppAgentContactPerson contactPerson, Long fkAgentId, Long fkCompanyId, Integer emailType) {
    try {
        // 构建邮件发送请求
        SendEmailDto sendEmailDto = new SendEmailDto();
        sendEmailDto.setType(emailType);
        
        SendEmailInfoDto emailInfo = new SendEmailInfoDto();
        emailInfo.setToUser(contactPerson.getName());
        emailInfo.setToEmail(contactPerson.getEmail());
        emailInfo.setAgentId(fkAgentId);
        emailInfo.setCompanyId(fkCompanyId);
        
        // 获取代理商的邮箱信息作为发件人
        // 这里需要调用现有方法获取代理商邮箱配置
        // 示例代码，具体实现需要根据现有系统调整
        SendEmailInfoDto agentEmailInfo = getAgentEmailInfo(fkAgentId);
        if (agentEmailInfo != null) {
            emailInfo.setFromUser(agentEmailInfo.getFromUser());
            emailInfo.setFromEmail(agentEmailInfo.getFromEmail());
            emailInfo.setEmailPassword(agentEmailInfo.getEmailPassword());
        }
        
        sendEmailDto.setEmailInfos(Arrays.asList(emailInfo));
        
        // 调用邮件发送服务（需要通过Feign Client调用partner-center服务）
        // 这里假设有一个partnerUserClient
        // Boolean result = partnerUserClient.sendInviteMail(sendEmailDto).getData();
        
        log.info("已发送{}类型联系人邮件，联系人：{}，邮箱：{}", emailType, contactPerson.getName(), contactPerson.getEmail());
        
    } catch (Exception e) {
        log.error("发送联系人邮件失败，联系人ID：{}，错误：{}", contactPerson.getId(), e.getMessage(), e);
        // 邮件发送失败不影响主流程
    }
}

/**
 * 发送联系人通知邮件（仅通知，不创建账号）
 * 
 * @param contactPerson 联系人信息
 * @param fkAgentId 代理ID
 * @param fkCompanyId 公司ID
 * @param emailType 邮件类型
 */
private void sendContactPersonNotifyEmail(AppAgentContactPerson contactPerson, Long fkAgentId, Long fkCompanyId, Integer emailType) {
    try {
        // 与邀请邮件类似，但不包含账号创建逻辑
        // 具体实现类似 sendContactPersonInviteEmail，但emailType不同
        log.info("已发送{}类型联系人通知邮件，联系人：{}，邮箱：{}", emailType, contactPerson.getName(), contactPerson.getEmail());
        
    } catch (Exception e) {
        log.error("发送联系人通知邮件失败，联系人ID：{}，错误：{}", contactPerson.getId(), e.getMessage(), e);
        // 邮件发送失败不影响主流程
    }
}

/**
 * 获取代理商邮箱信息
 * 
 * @param fkAgentId 代理ID
 * @return 邮箱信息
 */
private SendEmailInfoDto getAgentEmailInfo(Long fkAgentId) {
    // 这里需要根据现有系统实现
    // 参考 PartnerUserServiceImpl.resetPassword() 方法中的实现
    // return saleCenterMapper.selectEmailInfoByAgentId(fkAgentId);
    return null; // 占位符，需要具体实现
}

/**
 * 需要在类头部增加的导入
 */
// import com.get.partnercenter.dto.SendEmailDto;
// import com.get.partnercenter.dto.SendEmailInfoDto;
// import java.util.Arrays;

/**
 * 需要在类中增加的依赖注入（如果使用Feign Client）
 */
// @Autowired
// private IPartnerUserClient partnerUserClient;