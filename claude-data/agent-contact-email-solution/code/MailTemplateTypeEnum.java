package com.get.partnercenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 邮件模板类型枚举
 * 
 * 修改说明：
 * 1. 新增三种代理联系人邮件模板类型
 * 2. AGENT_ADMIN_INVITE - 代理管理员邀请邮件（包含账号密码）
 * 3. AGENT_COMMISSION_INVITE - 佣金负责人邀请邮件（包含账号密码）
 * 4. AGENT_EMERGENCY_NOTIFY - 紧急联系人通知邮件（仅通知，无账号）
 * 
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum MailTemplateTypeEnum {

    // 原有邮件模板类型
    INVITE_TO_REGISTER("INVITE_TO_REGISTER", "邀请邮件模板", 1),
    RESET_PASSWORD("RESET_PASSWORD", "密码邮件模板", 2),
    
    // 新增：代理联系人邮件模板类型
    AGENT_ADMIN_INVITE("AGENT_ADMIN_INVITE", "代理管理员邀请邮件", 3),
    AGENT_COMMISSION_INVITE("AGENT_COMMISSION_INVITE", "佣金负责人邀请邮件", 4),
    AGENT_EMERGENCY_NOTIFY("AGENT_EMERGENCY_NOTIFY", "紧急联系人通知邮件", 5);

    private String code;
    private String msg;
    private Integer type;

    public static MailTemplateTypeEnum getEnumByType(Integer type) {
        for (MailTemplateTypeEnum value : MailTemplateTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}