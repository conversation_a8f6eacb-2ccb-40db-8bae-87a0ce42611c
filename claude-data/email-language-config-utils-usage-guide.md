# EmailLanguageConfigUtils 使用指南

## 概述

`EmailLanguageConfigUtils` 是一个专门用于处理邮件语言配置的工具类，提供了统一的语言代码获取、模板选择、父模板处理等功能。该工具类旨在消除代码重复，提供一致的多语言邮件处理能力。

## 设计理念

### 静态工具类设计
- **无状态设计**：所有方法都是静态方法，线程安全
- **依赖注入**：通过参数传递所需的依赖，避免隐式依赖
- **异常安全**：提供默认值和优雅的异常处理

### 优先级策略
1. **显式参数优先**：`versionValue` 参数最高优先级
2. **员工配置次之**：通过 `staffId` 获取公司配置
3. **默认值保底**：确保始终有可用的语言配置

## 核心功能

### 1. 语言代码确定

#### 方法签名
```java
public static String determineLanguageCode(Map<String, String> params, IPermissionCenterClient permissionCenterClient)
```

#### 使用示例
```java
// 构建邮件参数
Map<String, String> params = new HashMap<>();
params.put("personalName", "张三");
params.put("staffId", "12345");        // 用于获取语言配置
params.put("versionValue", "en");      // 显式指定语言（可选）

// 确定语言代码
String languageCode = EmailLanguageConfigUtils.determineLanguageCode(params, permissionCenterClient);
// 返回：en（因为versionValue优先级最高）
```

#### 优先级说明
```java
// 优先级1：显式指定的versionValue
params.put("versionValue", "en");    // 最高优先级

// 优先级2：通过staffId获取的公司配置
params.put("staffId", "12345");      // 次高优先级

// 优先级3：默认值
// 如果以上都没有，返回 "zh"（中文）
```

### 2. 模板选择

#### 方法签名
```java
public static String selectEmailTemplate(String languageCode, String chineseTemplate, String englishTemplate)
```

#### 使用示例
```java
String chineseTemplate = "尊敬的${personalName}，您的申请已通过审核。";
String englishTemplate = "Dear ${personalName}, your application has been approved.";

String selectedTemplate = EmailLanguageConfigUtils.selectEmailTemplate(
    "en", 
    chineseTemplate, 
    englishTemplate
);
// 返回：Dear ${personalName}, your application has been approved.
```

#### 容错处理
```java
// 如果英文模板为空，自动降级到中文模板
String selectedTemplate = EmailLanguageConfigUtils.selectEmailTemplate(
    "en", 
    "中文模板内容", 
    null  // 英文模板为空
);
// 返回：中文模板内容
```

### 3. 父模板处理

#### 方法签名
```java
public static String processParentTemplate(String languageCode, String childTemplate, EmailTemplate parentTemplate, Map<String, String> params)
```

#### 使用示例
```java
// 准备参数
Map<String, String> params = new HashMap<>();
params.put("companyName", "测试公司");

// 处理父模板
String finalTemplate = EmailLanguageConfigUtils.processParentTemplate(
    "zh",                   // 语言代码
    "子模板内容",           // 子模板内容
    parentTemplate,         // 父模板对象
    params                  // 额外参数
);
```

### 4. 公司配置获取

#### 通过公司ID获取
```java
String languageCode = EmailLanguageConfigUtils.getLanguageCodeByCompanyId(
    companyId, 
    permissionCenterClient
);
```

#### 通过员工ID获取
```java
String languageCode = EmailLanguageConfigUtils.getLanguageCodeByStaffId(
    staffId, 
    permissionCenterClient
);
```

### 5. 完整流程处理

#### 方法签名
```java
public static String processCompleteEmailTemplate(
    Map<String, String> params, 
    String chineseTemplate, 
    String englishTemplate,
    EmailTemplate parentTemplate,
    IPermissionCenterClient permissionCenterClient
)
```

#### 使用示例
```java
// 一站式处理：语言确定 + 模板选择 + 参数替换 + 父模板处理
String finalTemplate = EmailLanguageConfigUtils.processCompleteEmailTemplate(
    params,                 // 包含staffId等的参数Map
    chineseTemplate,        // 中文模板
    englishTemplate,        // 英文模板
    parentTemplate,         // 父模板（可选）
    permissionCenterClient  // 权限中心客户端
);
```

## 在EmailHelper中的应用

### 重构前的代码
```java
// 原有重复代码
private String determineLanguageCode(Map<String, String> parsedMap) {
    String versionValue = parsedMap.get("versionValue");
    if (GeneralTool.isNotEmpty(versionValue)) {
        return EmailLanguageEnum.getLanguageCodeByConfigValue(versionValue);
    }
    
    String staffIdStr = parsedMap.get("staffId");
    if (GeneralTool.isNotEmpty(staffIdStr)) {
        try {
            Long staffId = Long.valueOf(staffIdStr);
            return getLanguageCodeByStaffId(staffId);
        } catch (NumberFormatException e) {
            log.warn("staffId格式错误: {}", staffIdStr);
        }
    }
    
    return EmailLanguageEnum.CHINESE.getCode();
}
```

### 重构后的代码
```java
// 使用工具类，代码简化
String languageCode = EmailLanguageConfigUtils.determineLanguageCode(parsedMap, permissionCenterClient);
```

### 模板选择重构
```java
// 重构前
String emailTemplate;
if (EmailLanguageEnum.isEnglish(emailDto.getLanguageCode())) {
    emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
} else {
    emailTemplate = remindTemplates.get(0).getEmailTemplate();
}

// 重构后
String emailTemplate = EmailLanguageConfigUtils.selectEmailTemplate(
    emailDto.getLanguageCode(),
    remindTemplates.get(0).getEmailTemplate(),
    remindTemplates.get(0).getEmailTemplateEn()
);
```

## 最佳实践

### 1. 参数构建建议
```java
// 推荐：明确的参数构建
Map<String, String> params = new HashMap<>();
params.put("personalName", userInfo.getName());
params.put("staffId", String.valueOf(currentStaffId));  // 用于语言配置
params.put("companyName", companyInfo.getName());
```

### 2. 异常处理
```java
// 工具类已内置异常处理，无需额外处理
String languageCode = EmailLanguageConfigUtils.determineLanguageCode(params, permissionCenterClient);
// 即使发生异常，也会返回默认值 "zh"
```

### 3. 日志记录
```java
// 工具类内置详细日志，包括：
// - 参数验证警告
// - 配置获取失败警告  
// - 网络异常错误日志
// - 性能相关信息
```

### 4. 缓存考虑
```java
// 当前版本暂未实现缓存，后续可考虑添加
// 高频调用场景建议在上层业务中实现缓存
```

## 扩展使用场景

### 1. 在业务服务中使用
```java
@Service
public class MyEmailService {
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    public void sendNotificationEmail(Long staffId, String content) {
        // 获取语言配置
        String languageCode = EmailLanguageConfigUtils.getLanguageCodeByStaffId(
            staffId, 
            permissionCenterClient
        );
        
        // 选择合适的模板
        String template = EmailLanguageConfigUtils.selectEmailTemplate(
            languageCode,
            "中文通知：" + content,
            "English notification: " + content
        );
        
        // 继续邮件发送逻辑...
    }
}
```

### 2. 在其他EmailHelper中使用
```java
@Component
public class CustomEmailHelper extends EmailAbstractHelper {
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        // 解析参数
        Map<String, String> params = parseEmailParameters(emailSenderQueue);
        
        // 使用工具类确定语言
        String languageCode = EmailLanguageConfigUtils.determineLanguageCode(
            params, 
            permissionCenterClient
        );
        
        // 后续处理...
    }
}
```

### 3. 在控制器中使用
```java
@RestController
public class EmailController {
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    @PostMapping("/preview-email")
    public String previewEmail(@RequestParam Long staffId, 
                              @RequestParam String templateType) {
        // 获取员工的语言配置
        String languageCode = EmailLanguageConfigUtils.getLanguageCodeByStaffId(
            staffId, 
            permissionCenterClient
        );
        
        // 根据语言返回对应的预览内容
        return EmailLanguageConfigUtils.selectEmailTemplate(
            languageCode,
            getChineseTemplate(templateType),
            getEnglishTemplate(templateType)
        );
    }
}
```

## 工具类方法快速参考

| 方法名 | 用途 | 入参 | 返回值 |
|--------|------|------|--------|
| `determineLanguageCode` | 智能确定语言代码 | params, client | String |
| `getLanguageCodeByStaffId` | 通过员工ID获取语言 | staffId, client | String |
| `getLanguageCodeByCompanyId` | 通过公司ID获取语言 | companyId, client | String |
| `selectEmailTemplate` | 选择邮件模板 | languageCode, zh, en | String |
| `processParentTemplate` | 处理父模板 | languageCode, child, parent, params | String |
| `processCompleteEmailTemplate` | 完整模板处理 | params, zh, en, parent, client | String |
| `getStaffInfo` | 获取员工信息 | staffId, client | StaffVo |
| `isEnglish` | 判断是否英文 | languageCode | boolean |
| `isChinese` | 判断是否中文 | languageCode | boolean |
| `isValidLanguageCode` | 验证语言代码 | languageCode | boolean |

## 注意事项

1. **线程安全**：工具类所有方法都是静态无状态的，完全线程安全
2. **异常处理**：所有方法都有内置的异常处理，返回安全的默认值
3. **性能考虑**：涉及网络调用，高频场景建议上层缓存
4. **日志输出**：工具类会输出详细的日志，便于调试和监控
5. **向后兼容**：完全兼容原有的 `versionValue` 参数使用方式

通过使用 `EmailLanguageConfigUtils` 工具类，可以大大简化邮件语言配置相关的代码，提高代码的可维护性和一致性。