# 现有中英文处理逻辑分析

## 1. 核心语言配置获取方式

### 1.1 通过公司ID获取语言配置
```java
// 标准模式：通过公司ID获取语言配置
Map<Long, String> versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
String versionValue2 = versionConfigMap.get(companyId);
```

### 1.2 配置项说明
- **配置键**: `ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key`
- **配置值**: `"en"` 表示英文，其他值（如`"zh"`或null）表示中文
- **获取方式**: 通过公司ID从配置Map中获取对应的语言设置

## 2. 语言判断逻辑

### 2.1 标准判断方式
```java
if (versionValue2.equals("en")) {
    // 英文逻辑
} else {
    // 中文逻辑（默认）
}
```

### 2.2 在邮件Helper中的应用
```java
// PartnerUserEmailHelper.java 中的模式
String languageCode = parsedMap.getOrDefault("versionValue", "zh");
emailDto.setLanguageCode(languageCode);

// 模板选择逻辑
if ("en".equals(emailDto.getLanguageCode())) {
    emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
} else {
    emailTemplate = remindTemplates.get(0).getEmailTemplate();
}
```

## 3. 员工信息获取模式

### 3.1 通过staffId获取员工信息
```java
// AppAgentServiceImpl.getBdStaffInfo 方法
public StaffVo getBdStaffInfo(Long bdStaffId) {
    Result<StaffVo> staffResult = permissionCenterClient.getStaffById(bdStaffId);
    if (!staffResult.isSuccess() || GeneralTool.isEmpty(staffResult.getData())) {
        throw new GetServiceException(LocaleMessageUtils.getFormatMessage("bd_staff_info_get_failed", bdStaffId));
    }
    return staffResult.getData();
}
```

### 3.2 StaffVo包含的关键信息
- `staffVo.getFkCompanyId()` - 公司ID，用于获取语言配置
- `staffVo.getEmail()` - 员工邮箱
- `staffVo.getEmailPassword()` - 邮箱密码
- 其他员工基础信息

## 4. 从参数Map中获取值的模式

### 4.1 在Helper类中的参数解析
```java
// PartnerUserEmailHelper.java 中的模式
Map<String, String> parsedMap = new HashMap<>();
if (GeneralTool.isNotEmpty(emailSenderQueue.getEmailParameter())) {
    ObjectMapper mapper = new ObjectMapper();
    parsedMap = mapper.readValue(emailSenderQueue.getEmailParameter(), Map.class);
}

// 设置各种参数
emailDto.setPersonalName(parsedMap.getOrDefault("personalName", ""));
emailDto.setAgentName(parsedMap.getOrDefault("name", ""));
emailDto.setAccount(parsedMap.getOrDefault("account", ""));
emailDto.setPassword(parsedMap.getOrDefault("password", ""));
emailDto.setTaskLink(parsedMap.getOrDefault("taskLink", ""));
```

### 4.2 语言设置的传递
```java
// 在AppAgentApproveCommentServiceImpl.sendEmail方法中
Map<String, String> map = new HashMap<>();
map.put("staffName", staffName);
map.put("agentName", appAgent.getName());
map.put("approveComment", appAgentApproveComment.getApproveComment());
map.put("taskLink", link);

// 在RemindTaskDto中设置语言
if (versionValue2.equals("en")) {
    remindTaskDto.setLanguageCode("en");
}
```

## 5. 枚举类存放位置和命名规范

### 5.1 存放位置
- **API模块**: `biz-service-ap/ais-{service-name}-ap/src/main/java/com/get/{service}/enums/`
- **示例**: `/biz-service-ap/ais-sale-center-ap/src/main/java/com/get/salecenter/enums/ContactPersonTypeEnum.java`

### 5.2 命名规范
- **类名**: `{业务名称}Enum`，如`ContactPersonTypeEnum`
- **静态Map**: `{枚举名去掉Enum后缀大写}_MAP`，如`CONTACT_PERSON_TYPE_MAP`
- **获取方法**: `get{枚举名}ByCode(String code)`

### 5.3 标准枚举结构
```java
@Getter
@AllArgsConstructor
public enum ContactPersonTypeEnum {
    SALES("CONTACT_AGENT_SALES", "顾问（Counselor）", false);
    
    private final String code;
    private final String msg;
    private final Boolean defaultIsCommissionEmail;
    
    private static final Map<String, ContactPersonTypeEnum> CONTACT_PERSON_TYPE_MAP = new HashMap<>();
    
    static {
        for (ContactPersonTypeEnum enumItem : ContactPersonTypeEnum.values()) {
            CONTACT_PERSON_TYPE_MAP.put(enumItem.getCode(), enumItem);
        }
    }
    
    public static ContactPersonTypeEnum getContactPersonTypeByCode(String code) {
        return CONTACT_PERSON_TYPE_MAP.get(code);
    }
}
```

## 6. 邮件模板处理模式

### 6.1 邮件模板的语言选择
```java
// 在Helper类中的模板选择逻辑
if ("en".equals(emailDto.getLanguageCode())) {
    emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
} else {
    emailTemplate = remindTemplates.get(0).getEmailTemplate();
}
```

### 6.2 父模板处理
```java
// 父模板的语言处理
if ("en".equals(emailDto.getLanguageCode())) {
    parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(
        parentMap, parentTemplate.getEmailTemplateEn()
    );
} else {
    parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(
        parentMap, parentTemplate.getEmailTemplate()
    );
}
```

## 7. 语言配置的业务流程

### 7.1 完整的语言处理流程
1. **获取公司ID**: 从业务实体（如Agent、Student等）中获取`fkCompanyId`
2. **获取语言配置**: 通过`permissionCenterClient.getCompanyConfigMap()`获取配置
3. **判断语言**: 检查配置值是否等于"en"
4. **设置语言代码**: 在DTO或参数Map中设置对应的语言代码
5. **选择模板**: 根据语言代码选择对应的邮件模板
6. **变量替换**: 使用`ReminderTemplateUtils.getReminderTemplate()`进行模板变量替换

### 7.2 不同业务场景的应用
- **代理审批通知**: `AppAgentApproveCommentServiceImpl.sendEmail()`
- **学生Offer通知**: `StudentOfferItemServiceImpl` 中的多处应用
- **自定义任务**: `CustomTaskServiceImpl` 中的处理
- **工作流提醒**: `WorkLeaveReminderEmailHelper` 等Helper类

## 8. 关键技术点总结

### 8.1 配置获取模式
- 使用`ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key`作为统一的配置键
- 通过公司ID获取对应的语言配置
- 默认值处理：非"en"的值都被视为中文

### 8.2 员工信息获取模式
- 通过`permissionCenterClient.getStaffById()`获取员工信息
- 从员工信息中获取公司ID用于语言配置查询
- 包含完整的错误处理和日志记录

### 8.3 参数传递模式
- 使用Map进行参数传递
- JSON序列化/反序列化处理复杂参数
- 通过`getOrDefault()`方法提供默认值

### 8.4 枚举类设计模式
- 统一的枚举结构和命名规范
- 静态Map提供快速查找
- 包含业务逻辑验证方法

## 9. 实际应用示例

### 9.1 AppAgentApproveCommentServiceImpl.sendEmail
```java
// 1. 获取语言配置
Map<Long, String> versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
String versionValue2 = versionConfigMap.get(appAgent.getFkCompanyId());

// 2. 根据语言设置不同的处理逻辑
if (versionValue2.equals("en")) {
    taskTitle = new StringBuilder("【Agent Application Approval Decision Notification】");
    staffName = map1.get(appAgent.getFkStaffId());
    taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_APPROVE_COMMENT_REMINDER_ENGLISH);
} else {
    taskTitle = new StringBuilder("代理申请审批意见通知");
    staffName = permissionCenterClient.getStaffName(appAgent.getFkStaffId()).getData();
    taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_APPROVE_COMMENT_REMINDER);
}

// 3. 设置语言代码
if (versionValue2.equals("en")) {
    remindTaskDto.setLanguageCode("en");
}
```

### 9.2 PartnerUserEmailHelper.assembleEmailData
```java
// 1. 解析参数Map
Map<String, String> parsedMap = new HashMap<>();
if (GeneralTool.isNotEmpty(emailSenderQueue.getEmailParameter())) {
    ObjectMapper mapper = new ObjectMapper();
    parsedMap = mapper.readValue(emailSenderQueue.getEmailParameter(), Map.class);
}

// 2. 设置语言代码
String languageCode = parsedMap.getOrDefault("versionValue", "zh");
emailDto.setLanguageCode(languageCode);

// 3. 设置其他参数
emailDto.setPersonalName(parsedMap.getOrDefault("personalName", ""));
emailDto.setAgentName(parsedMap.getOrDefault("name", ""));
```

这些模式为设计新的语言处理枚举类和业务逻辑提供了清晰的参考和指导。