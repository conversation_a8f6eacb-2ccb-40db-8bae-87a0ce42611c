# 邮件系统优化方案分析报告

## 1. 现有邮件系统架构分析

### 1.1 当前实现方式

现有邮件系统采用**策略模式 + 工厂模式**的设计架构：

#### 核心组件
- **EmailAbstractHelper**: 抽象基类，定义邮件发送接口
- **具体策略类**: 20+个Helper类，每个处理特定邮件类型
- **EmailFactoryConfiguration**: 工厂配置，管理策略映射
- **EmailSenderQueueService**: 队列服务，通过工厂获取策略

#### 调用流程
```java
// 1. 接收邮件发送请求
EmailSenderQueue emailSenderQueue = ...;

// 2. 通过工厂获取对应策略
String emailTypeKey = emailSenderQueue.getFkEmailTypeKey();
EmailAbstractHelper helper = emailHelperMap.get(emailTypeKey);

// 3. 执行邮件发送
helper.sendMail(emailSenderQueue);
```

### 1.2 代码重复度分析

通过对现有Helper类的深入分析，发现**90%以上的代码完全相同**：

#### 相同的代码模式

**sendMail()方法重复逻辑**：
```java
@Override
public void sendMail(EmailSenderQueue emailSenderQueue) {
    StringJoiner failedEmails = new StringJoiner("; "); // ✓ 完全相同
    
    try {
        // ✓ 数据组装 - 唯一不同的部分
        XxxDto dto = assembleEmailData(emailSenderQueue);
        
        // ✓ 获取员工信息 - 完全相同
        List<StaffVo> staff = permissionCenterClient.getStaffByIds(dto.getStaffEmailSet());
        Map<Long, StaffVo> data = staff.stream().collect(Collectors.toMap(...));
        
        // ✓ 模板处理 - 完全相同
        String template = setEmailTemplate(dto);
        
        // ✓ 循环发送 - 完全相同
        for (Long id : dto.getStaffEmailSet()) {
            try {
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                emailSystemMQMessageDto.setTitle(dto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                // ... 记录成功邮箱
            } catch (Exception e) {
                // ... 记录失败邮箱 - 完全相同
            }
        }
        
        // ✓ 状态更新 - 完全相同
        LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());
        if (failedEmails.length() > 0) {
            updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
        }
        emailSenderQueueMapper.update(null, updateWrapper);
        
    } catch (Exception e) {
        // ✓ 异常处理 - 完全相同
        log.error("邮件发送异常", e);
        emailSenderQueue.setErrorMessage(e.getMessage());
        emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
        emailSenderQueue.setOperationStatus(-1);
        emailSenderQueueMapper.updateById(emailSenderQueue);
    }
}
```

**setEmailTemplate()方法重复逻辑**：
```java
private String setEmailTemplate(XxxDto reminderDto) {
    // ✓ 查询模板 - 完全相同
    List<EmailTemplate> remindTemplates = emailTemplateMapper.selectList(
        Wrappers.<EmailTemplate>lambdaQuery()
            .eq(EmailTemplate::getEmailTypeKey, reminderDto.getFkEmailTypeKey()));
    
    // ✓ 验证模板 - 完全相同
    if (GeneralTool.isEmpty(remindTemplates)) {
        throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
    }
    
    // ✓ 语言选择 - 完全相同
    String emailTemplate = null;
    if (!reminderDto.getLanguageCode().equals("en")) {
        emailTemplate = remindTemplates.get(0).getEmailTemplate();
    } else {
        emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
    }
    
    // ✓ 变量替换 - 完全相同
    emailTemplate = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
    emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
    
    // ✓ 父子模板处理 - 完全相同
    if (GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId()) 
        && remindTemplates.get(0).getFkParentEmailTemplateId() != 0) {
        EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
        Map map = new HashMap();
        map.put("subtemplate", emailTemplate);
        
        if (reminderDto.getLanguageCode().equals("en")) {
            parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
        } else {
            parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
        }
    }
    
    return parentEmailTemplate;
}
```

#### 唯一差异：数据组装逻辑

只有`assembleEmailData()`方法在各个Helper中有所不同：

```java
// ProviderContractExpiryEmailHelper
public ProviderContractExpiryDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
    // 获取合同信息
    InstitutionProviderContractReminderVo contractInfo = 
        institutionCenterClient.getContractExpiredByProviderId(emailSenderQueue.getFkTableId());
    
    // 获取收件人（创建人、审批人、上级）
    reminderDto.getStaffEmailSet().add(contractInfo.getGmtCreateUserId());
    reminderDto.getStaffEmailSet().add(contractInfo.getFkStaffId());
    
    // 组装模板参数
    map.put("name", contractInfo.getFullName());
    map.put("startTime", formatDate(contractInfo.getStartTime()));
    map.put("endTime", formatDate(contractInfo.getEndTime()));
    // ...
}

// CommissionNoticeEmailHelper  
public CommissionNoticeEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
    // 获取佣金信息
    CommissionInfo commissionInfo = 
        saleCenterClient.getCommissionInfo(emailSenderQueue.getFkTableId());
    
    // 获取收件人（财务人员、销售人员）
    reminderDto.getStaffEmailSet().addAll(getFinanceStaff());
    reminderDto.getStaffEmailSet().addAll(getSalesStaff());
    
    // 组装模板参数  
    map.put("studentName", commissionInfo.getStudentName());
    map.put("amount", commissionInfo.getAmount());
    map.put("agentName", commissionInfo.getAgentName());
    // ...
}
```

### 1.3 维护成本评估

#### 当前问题
1. **代码冗余严重**: 20+个Helper类，每个200-300行代码，90%重复
2. **维护成本高**: 修改公共逻辑需要同时修改20+个文件
3. **新增模板复杂**: 需要创建完整的Helper类、DTO类、配置映射
4. **测试成本高**: 每个Helper都需要独立的单元测试
5. **不符合DRY原则**: Don't Repeat Yourself原则严重违反

#### 定量分析
- **总代码量**: 约6000+行（20个Helper × 300行）
- **重复代码**: 约5400行（90% × 6000行）
- **有效代码**: 约600行（数据组装逻辑）
- **代码冗余率**: 90%

### 1.4 现有架构优缺点

#### 优点
- **职责清晰**: 每个Helper只处理一种邮件类型
- **扩展性**: 可以独立处理复杂的业务逻辑
- **类型安全**: 编译期类型检查

#### 缺点
- **代码重复**: 90%的代码完全相同
- **维护困难**: 公共逻辑修改影响面大
- **开发效率低**: 新增邮件类型需要大量重复代码
- **测试复杂**: 需要为每个Helper编写相似的测试

---

## 2. 优化方案设计

### 2.1 方案1：统一邮件服务

#### 设计思路
将公共的邮件发送逻辑提取到统一服务中，通过接口回调处理不同的数据组装逻辑。

#### 架构设计

```java
/**
 * 统一邮件发送服务
 */
@Service
public class UnifiedEmailService {
    
    @Resource
    private EmailTemplateMapper emailTemplateMapper;
    
    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    @Resource
    private RemindTaskQueueService remindTaskQueueService;
    
    /**
     * 统一邮件发送接口
     * @param emailSenderQueue 邮件队列
     * @param dataAssembler 数据组装器
     */
    public void sendMail(EmailSenderQueue emailSenderQueue, EmailDataAssembler dataAssembler) {
        log.info("开始发送邮件，队列ID: {}, 邮件类型: {}", 
                emailSenderQueue.getId(), emailSenderQueue.getFkEmailTypeKey());
        
        StringJoiner failedEmails = new StringJoiner("; ");
        
        try {
            // 1. 数据组装（通过回调实现）
            EmailData emailData = dataAssembler.assembleData(emailSenderQueue);
            
            // 2. 获取邮件模板
            String template = getEmailTemplate(emailData);
            
            // 3. 批量获取员工信息
            Map<Long, StaffVo> staffMap = getStaffMap(emailData.getRecipientIds());
            
            // 4. 循环发送邮件
            StringJoiner emailsCombined = new StringJoiner(", ");
            for (Long staffId : emailData.getRecipientIds()) {
                try {
                    sendSingleEmail(emailSenderQueue, emailData, template, staffMap.get(staffId));
                    emailsCombined.add(staffMap.get(staffId).getEmail());
                } catch (Exception e) {
                    handleSendFailure(failedEmails, staffMap.get(staffId), e);
                }
            }
            
            // 5. 更新队列状态
            updateQueueStatus(emailSenderQueue, emailsCombined.toString(), failedEmails);
            
        } catch (Exception e) {
            handleGlobalException(emailSenderQueue, e);
        }
    }
    
    /**
     * 发送单个邮件
     */
    private void sendSingleEmail(EmailSenderQueue emailSenderQueue, EmailData emailData, 
                                String template, StaffVo staff) {
        // 个性化模板参数
        Map<String, String> personalizedParams = new HashMap<>(emailData.getTemplateParams());
        personalizedParams.put("personalName", staff.getFullName());
        
        // 替换个性化参数
        String personalizedTemplate = ReminderTemplateUtils.getReminderTemplate(personalizedParams, template);
        
        // 构建消息
        EmailSystemMQMessageDto messageDto = new EmailSystemMQMessageDto();
        messageDto.setEmailSenderQueueId(emailSenderQueue.getId());
        messageDto.setTitle(emailData.getEmailTitle());
        messageDto.setContent(personalizedTemplate);
        messageDto.setToEmail(staff.getEmail());
        
        // 发送邮件
        remindTaskQueueService.sendSystemMail(messageDto);
    }
    
    /**
     * 获取邮件模板（复用现有逻辑）
     */
    private String getEmailTemplate(EmailData emailData) {
        List<EmailTemplate> templates = emailTemplateMapper.selectList(
            Wrappers.<EmailTemplate>lambdaQuery()
                .eq(EmailTemplate::getEmailTypeKey, emailData.getEmailTypeKey()));
        
        if (GeneralTool.isEmpty(templates)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        
        String template = emailData.getLanguageCode().equals("en") 
            ? templates.get(0).getEmailTemplateEn() 
            : templates.get(0).getEmailTemplate();
        
        template = ReminderTemplateUtils.getReminderTemplate(emailData.getTemplateParams(), template);
        template = template.replace("#{taskTitle}", emailData.getEmailTitle());
        
        // 处理父子模板
        return processParentTemplate(templates.get(0), template, emailData.getLanguageCode());
    }
    
    // ... 其他公共方法
}
```

#### 数据组装接口

```java
/**
 * 邮件数据组装器接口
 */
public interface EmailDataAssembler {
    
    /**
     * 组装邮件数据
     * @param emailSenderQueue 邮件队列
     * @return 邮件数据
     */
    EmailData assembleData(EmailSenderQueue emailSenderQueue);
}

/**
 * 邮件数据传输对象
 */
@Data
public class EmailData {
    private String emailTypeKey;
    private String emailTitle;
    private String languageCode;
    private Set<Long> recipientIds;
    private Map<String, String> templateParams;
}
```

#### 具体实现示例

```java
/**
 * 供应商合同到期邮件数据组装器
 */
@Component
public class ProviderContractExpiryDataAssembler implements EmailDataAssembler {
    
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    @Override
    public EmailData assembleData(EmailSenderQueue emailSenderQueue) {
        EmailData emailData = new EmailData();
        
        // 获取合同信息
        InstitutionProviderContractReminderVo contractInfo = 
            institutionCenterClient.getContractExpiredByProviderId(emailSenderQueue.getFkTableId());
        
        // 获取提醒天数
        String reminderDays = emailSenderQueue.getEmailParameter();
        
        // 设置收件人
        Set<Long> recipients = new HashSet<>();
        recipients.add(contractInfo.getGmtCreateUserId()); // 创建人
        recipients.add(contractInfo.getFkStaffId()); // 审批人
        
        Long supervisorId = permissionCenterClient.getStaffSupervisorIdByStaffId(contractInfo.getFkStaffId()).getData();
        if (supervisorId != null) {
            recipients.add(supervisorId); // 上级
        }
        
        // 添加配置的通知人员
        String configValue = permissionCenterClient.getConfigValueByConfigKey(
            ProjectKeyEnum.REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION.key).getData();
        if (GeneralTool.isNotEmpty(configValue)) {
            JSONObject configJSON = JSONUtil.parseObj(configValue);
            JSONArray staffIdsArray = configJSON.getJSONArray("Notifier");
            if (staffIdsArray != null) {
                for (int i = 0; i < staffIdsArray.size(); i++) {
                    recipients.add(staffIdsArray.getLong(i));
                }
            }
        }
        
        // 组装模板参数
        Map<String, String> templateParams = new HashMap<>();
        templateParams.put("name", contractInfo.getFullName());
        templateParams.put("startTime", formatDate(contractInfo.getStartTime()));
        templateParams.put("endTime", formatDate(contractInfo.getEndTime()));
        templateParams.put("count", reminderDays);
        templateParams.put("contractTitle", contractInfo.getContractName());
        
        // 设置邮件标题
        String title = String.format("【合同到期提醒】%s(%s至%s，提前%s天提醒)",
            contractInfo.getFullName(),
            formatDate(contractInfo.getStartTime()),
            formatDate(contractInfo.getEndTime()),
            reminderDays);
        
        emailData.setEmailTypeKey(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey());
        emailData.setEmailTitle(title);
        emailData.setLanguageCode("zh");
        emailData.setRecipientIds(recipients);
        emailData.setTemplateParams(templateParams);
        
        return emailData;
    }
    
    private String formatDate(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }
}
```

#### 使用方式

```java
/**
 * 邮件发送工厂
 */
@Component
public class EmailSenderFactory {
    
    @Resource
    private UnifiedEmailService unifiedEmailService;
    
    @Resource
    private Map<String, EmailDataAssembler> assemblerMap;
    
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        String emailTypeKey = emailSenderQueue.getFkEmailTypeKey();
        EmailDataAssembler assembler = assemblerMap.get(emailTypeKey + "DataAssembler");
        
        if (assembler == null) {
            throw new GetServiceException("未找到对应的数据组装器: " + emailTypeKey);
        }
        
        unifiedEmailService.sendMail(emailSenderQueue, assembler);
    }
}
```

#### 方案1优缺点

**优点**：
- **大幅减少重复代码**: 公共逻辑统一实现
- **维护成本低**: 修改公共逻辑只需修改一处
- **类型安全**: 编译期检查，IDE支持好
- **渐进式迁移**: 可以逐步替换现有Helper

**缺点**：
- **仍需创建组装器类**: 新增邮件类型仍需编码
- **接口复杂度**: 需要设计合适的回调接口
- **学习成本**: 开发人员需要理解新的抽象

---

### 2.2 方案2：模板方法模式优化

#### 设计思路
重构现有的继承体系，将公共逻辑上移到抽象父类，子类只需实现数据组装逻辑。

#### 架构设计

```java
/**
 * 邮件发送抽象模板类
 */
public abstract class EmailAbstractHelper {
    
    @Resource
    protected EmailTemplateMapper emailTemplateMapper;
    
    @Resource
    protected EmailSenderQueueMapper emailSenderQueueMapper;
    
    @Resource
    protected IPermissionCenterClient permissionCenterClient;
    
    @Resource
    protected RemindTaskQueueService remindTaskQueueService;
    
    /**
     * 模板方法：定义邮件发送流程
     */
    public final void sendMail(EmailSenderQueue emailSenderQueue) {
        log.info("开始发送邮件，队列ID: {}, 邮件类型: {}", 
                emailSenderQueue.getId(), emailSenderQueue.getFkEmailTypeKey());
        
        StringJoiner failedEmails = new StringJoiner("; ");
        
        try {
            // 1. 数据组装（子类实现）
            EmailTemplateData templateData = assembleEmailData(emailSenderQueue);
            
            // 2. 获取邮件模板（模板方法）
            String template = getEmailTemplate(templateData);
            
            // 3. 批量获取员工信息（模板方法）
            Map<Long, StaffVo> staffMap = getStaffMap(templateData.getRecipientIds());
            
            // 4. 循环发送邮件（模板方法）
            StringJoiner emailsCombined = new StringJoiner(", ");
            for (Long staffId : templateData.getRecipientIds()) {
                try {
                    sendSingleEmail(emailSenderQueue, templateData, template, staffMap.get(staffId));
                    emailsCombined.add(staffMap.get(staffId).getEmail());
                } catch (Exception e) {
                    handleSendFailure(failedEmails, staffMap.get(staffId), e);
                }
            }
            
            // 5. 更新队列状态（模板方法）
            updateQueueStatus(emailSenderQueue, emailsCombined.toString(), failedEmails);
            
        } catch (Exception e) {
            handleGlobalException(emailSenderQueue, e);
        }
    }
    
    /**
     * 抽象方法：数据组装（子类必须实现）
     */
    public abstract EmailTemplateData assembleEmailData(EmailSenderQueue emailSenderQueue);
    
    /**
     * 模板方法：获取邮件模板
     */
    protected final String getEmailTemplate(EmailTemplateData templateData) {
        List<EmailTemplate> templates = emailTemplateMapper.selectList(
            Wrappers.<EmailTemplate>lambdaQuery()
                .eq(EmailTemplate::getEmailTypeKey, templateData.getEmailTypeKey()));
        
        if (GeneralTool.isEmpty(templates)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        
        String template = templateData.getLanguageCode().equals("en") 
            ? templates.get(0).getEmailTemplateEn() 
            : templates.get(0).getEmailTemplate();
        
        template = ReminderTemplateUtils.getReminderTemplate(templateData.getTemplateParams(), template);
        template = template.replace("#{taskTitle}", templateData.getEmailTitle());
        
        return processParentTemplate(templates.get(0), template, templateData.getLanguageCode());
    }
    
    /**
     * 模板方法：批量获取员工信息
     */
    protected final Map<Long, StaffVo> getStaffMap(Set<Long> staffIds) {
        List<StaffVo> staffList = permissionCenterClient.getStaffByIds(staffIds);
        return staffList.stream().collect(Collectors.toMap(StaffVo::getId, Function.identity()));
    }
    
    /**
     * 模板方法：发送单个邮件
     */
    protected final void sendSingleEmail(EmailSenderQueue emailSenderQueue, EmailTemplateData templateData, 
                                        String template, StaffVo staff) {
        // 个性化模板参数
        Map<String, String> personalizedParams = new HashMap<>(templateData.getTemplateParams());
        personalizedParams.put("personalName", staff.getFullName());
        
        // 替换个性化参数
        String personalizedTemplate = ReminderTemplateUtils.getReminderTemplate(personalizedParams, template);
        
        // 构建消息
        EmailSystemMQMessageDto messageDto = new EmailSystemMQMessageDto();
        messageDto.setEmailSenderQueueId(emailSenderQueue.getId());
        messageDto.setTitle(templateData.getEmailTitle());
        messageDto.setContent(personalizedTemplate);
        messageDto.setToEmail(staff.getEmail());
        
        // 发送邮件
        remindTaskQueueService.sendSystemMail(messageDto);
    }
    
    /**
     * 模板方法：处理发送失败
     */
    protected final void handleSendFailure(StringJoiner failedEmails, StaffVo staff, Exception e) {
        String failedEmail = staff != null && staff.getEmail() != null 
            ? staff.getEmail() 
            : "staffId:" + (staff != null ? staff.getId() : "unknown");
        failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
        log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}", 
                 staff != null ? staff.getId() : "unknown", failedEmail, e.getMessage());
    }
    
    /**
     * 模板方法：更新队列状态
     */
    protected final void updateQueueStatus(EmailSenderQueue emailSenderQueue, String emailsCombined, 
                                          StringJoiner failedEmails) {
        LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())
                    .set(EmailSenderQueue::getEmailTo, emailsCombined);
        
        if (failedEmails.length() > 0) {
            updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            log.warn("邮件发送部分失败，失败邮箱: {}", failedEmails.toString());
        }
        
        emailSenderQueueMapper.update(null, updateWrapper);
    }
    
    /**
     * 模板方法：处理全局异常
     */
    protected final void handleGlobalException(EmailSenderQueue emailSenderQueue, Exception e) {
        log.error("邮件发送异常，队列ID: {}", emailSenderQueue.getId(), e);
        emailSenderQueue.setErrorMessage(e.getMessage());
        emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
        emailSenderQueue.setOperationStatus(-1);
        emailSenderQueueMapper.updateById(emailSenderQueue);
    }
    
    /**
     * 处理父子模板
     */
    protected final String processParentTemplate(EmailTemplate emailTemplate, String template, String languageCode) {
        if (GeneralTool.isNotEmpty(emailTemplate.getFkParentEmailTemplateId()) 
            && emailTemplate.getFkParentEmailTemplateId() != 0) {
            
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(emailTemplate.getFkParentEmailTemplateId());
            Map<String, Object> map = new HashMap<>();
            map.put("subtemplate", template);
            
            if (languageCode.equals("en")) {
                return ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            } else {
                return ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }
        }
        return template;
    }
}
```

#### 统一数据对象

```java
/**
 * 邮件模板数据对象
 */
@Data
public class EmailTemplateData {
    private String emailTypeKey;
    private String emailTitle;
    private String languageCode;
    private Set<Long> recipientIds;
    private Map<String, String> templateParams;
    
    public EmailTemplateData() {
        this.recipientIds = new HashSet<>();
        this.templateParams = new HashMap<>();
        this.languageCode = "zh"; // 默认中文
    }
}
```

#### 子类实现示例

```java
/**
 * 供应商合同到期邮件Helper
 */
@Component("providerContractExpiryEmailHelper")
public class ProviderContractExpiryEmailHelper extends EmailAbstractHelper {
    
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    
    @Override
    public EmailTemplateData assembleEmailData(EmailSenderQueue emailSenderQueue) {
        EmailTemplateData templateData = new EmailTemplateData();
        
        // 获取合同信息
        InstitutionProviderContractReminderVo contractInfo = 
            institutionCenterClient.getContractExpiredByProviderId(emailSenderQueue.getFkTableId());
        
        // 获取提醒天数
        String reminderDays = emailSenderQueue.getEmailParameter();
        
        // 设置收件人
        templateData.getRecipientIds().add(contractInfo.getGmtCreateUserId()); // 创建人
        templateData.getRecipientIds().add(contractInfo.getFkStaffId()); // 审批人
        
        Long supervisorId = permissionCenterClient.getStaffSupervisorIdByStaffId(contractInfo.getFkStaffId()).getData();
        if (supervisorId != null) {
            templateData.getRecipientIds().add(supervisorId); // 上级
        }
        
        // 添加配置的通知人员
        addConfiguredRecipients(templateData.getRecipientIds());
        
        // 组装模板参数
        templateData.getTemplateParams().put("name", contractInfo.getFullName());
        templateData.getTemplateParams().put("startTime", formatDate(contractInfo.getStartTime()));
        templateData.getTemplateParams().put("endTime", formatDate(contractInfo.getEndTime()));
        templateData.getTemplateParams().put("count", reminderDays);
        templateData.getTemplateParams().put("contractTitle", contractInfo.getContractName());
        
        // 设置邮件信息
        templateData.setEmailTypeKey(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey());
        templateData.setEmailTitle(buildEmailTitle(contractInfo, reminderDays));
        templateData.setLanguageCode("zh");
        
        return templateData;
    }
    
    private void addConfiguredRecipients(Set<Long> recipients) {
        String configValue = permissionCenterClient.getConfigValueByConfigKey(
            ProjectKeyEnum.REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION.key).getData();
        if (GeneralTool.isNotEmpty(configValue)) {
            JSONObject configJSON = JSONUtil.parseObj(configValue);
            JSONArray staffIdsArray = configJSON.getJSONArray("Notifier");
            if (staffIdsArray != null) {
                for (int i = 0; i < staffIdsArray.size(); i++) {
                    recipients.add(staffIdsArray.getLong(i));
                }
            }
        }
    }
    
    private String buildEmailTitle(InstitutionProviderContractReminderVo contractInfo, String reminderDays) {
        return String.format("【合同到期提醒】%s(%s至%s，提前%s天提醒)",
            contractInfo.getFullName(),
            formatDate(contractInfo.getStartTime()),
            formatDate(contractInfo.getEndTime()),
            reminderDays);
    }
    
    private String formatDate(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }
}
```

#### 代理申请通过邮件Helper

```java
/**
 * 代理申请通过邮件Helper（新增）
 */
@Component("agencyApplyApprovedEmailHelper")
public class AgencyApplyApprovedEmailHelper extends EmailAbstractHelper {
    
    @Resource
    private IPartnerCenterClient partnerCenterClient;
    
    @Resource
    private ISaleCenterClient saleCenterClient;
    
    @Override
    public EmailTemplateData assembleEmailData(EmailSenderQueue emailSenderQueue) {
        EmailTemplateData templateData = new EmailTemplateData();
        
        // 解析邮件参数获取注册用户信息
        String emailParameter = emailSenderQueue.getEmailParameter();
        List<RegisterPartnerUserVo> registerUsers = parseRegisterUsers(emailParameter);
        
        // 获取代理信息
        AgentVo agentInfo = saleCenterClient.getAgentById(emailSenderQueue.getFkTableId()).getData();
        
        // 这里收件人不是员工ID，而是直接的邮箱地址
        // 我们需要重写父类的发送逻辑，或者创建临时员工记录
        // 为简化示例，这里假设我们使用邮箱地址直接发送
        
        // 组装模板参数（通用部分）
        templateData.getTemplateParams().put("name", agentInfo.getAgentName());
        templateData.getTemplateParams().put("taskLink", "https://miniprogram.com");
        
        // 设置邮件信息
        templateData.setEmailTypeKey(EmailTemplateEnum.AGENCY_APPLY_APPROVED.getEmailTemplateKey());
        templateData.setEmailTitle("代理申请审核通过通知");
        templateData.setLanguageCode("zh");
        
        // 这里需要特殊处理，因为收件人是邮箱而不是员工ID
        // 可以考虑扩展父类的处理逻辑
        
        return templateData;
    }
    
    private List<RegisterPartnerUserVo> parseRegisterUsers(String emailParameter) {
        // 解析注册用户信息的逻辑
        try {
            return JSON.parseArray(emailParameter, RegisterPartnerUserVo.class);
        } catch (Exception e) {
            log.error("解析注册用户信息失败", e);
            return Collections.emptyList();
        }
    }
}
```

#### 方案2优缺点

**优点**：
- **减少重复代码**: 公共逻辑在父类统一实现
- **保持现有结构**: 继续使用Helper模式，变更影响小
- **类型安全**: 编译期检查，IDE支持好
- **渐进式重构**: 可以逐步迁移现有Helper

**缺点**：
- **仍需创建Helper类**: 新增邮件类型仍需编码
- **继承深度**: 可能导致继承层次复杂
- **灵活性限制**: 特殊场景（如收件人不是员工）处理困难

---

### 2.3 方案3：配置驱动架构

#### 设计思路
将数据组装逻辑从Java代码中完全抽离，通过JSON配置文件定义数据源、收件人规则、模板参数等，使用反射和表达式引擎动态执行，实现零代码添加新邮件模板。

#### 整体架构

```mermaid
graph TD
    A[邮件发送请求] --> B[配置加载器]
    B --> C[JSON配置文件]
    C --> D[数据源执行器]
    D --> E[业务服务调用]
    E --> F[表达式计算器]
    F --> G[收件人计算]
    F --> H[模板参数计算]
    G --> I[统一邮件发送器]
    H --> I
    I --> J[RocketMQ异步发送]
```

#### 配置文件设计

**email-config.json**
```json
{
  "AGENCY_APPLY_APPROVED": {
    "description": "代理申请通过通知",
    "dataSource": {
      "registerUsers": {
        "service": "partnerCenterClient",
        "method": "getRegisterPartnerUsers", 
        "params": ["#{agentId}"],
        "resultType": "List<RegisterPartnerUserVo>"
      },
      "agentInfo": {
        "service": "saleCenterClient",
        "method": "getAgentById",
        "params": ["#{agentId}"],
        "resultType": "AgentVo"
      }
    },
    "recipients": {
      "type": "dynamic",
      "expression": "registerUsers.stream().map(user -> user.getAccount()).collect(Collectors.toList())",
      "recipientType": "email"
    },
    "templateParams": {
      "personalName": "#{extractNameFromEmail(recipient)}",
      "name": "#{agentInfo.agentName != null ? agentInfo.agentName : '代理商'}",
      "account": "#{getCurrentUser(registerUsers, recipient).account}",
      "password": "#{getCurrentUser(registerUsers, recipient).password}",
      "taskLink": "https://miniprogram.com"
    },
    "emailTitle": "代理申请审核通过通知",
    "languageCode": "zh"
  },
  
  "PROVIDER_CONTRACT_EXPIRE": {
    "description": "供应商合同到期提醒",
    "dataSource": {
      "contractInfo": {
        "service": "institutionCenterClient",
        "method": "getContractExpiredByProviderId",
        "params": ["#{tableId}"],
        "resultType": "InstitutionProviderContractReminderVo"
      },
      "configInfo": {
        "service": "permissionCenterClient", 
        "method": "getConfigValueByConfigKey",
        "params": ["REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION"],
        "resultType": "String"
      }
    },
    "recipients": {
      "type": "composite",
      "sources": [
        {
          "type": "single",
          "expression": "contractInfo.gmtCreateUserId"
        },
        {
          "type": "single", 
          "expression": "contractInfo.fkStaffId"
        },
        {
          "type": "dynamic",
          "expression": "getSupervisorId(contractInfo.fkStaffId)"
        },
        {
          "type": "config",
          "expression": "parseConfigStaffIds(configInfo, 'Notifier')"
        }
      ],
      "recipientType": "staffId"
    },
    "templateParams": {
      "name": "#{contractInfo.fullName}",
      "startTime": "#{formatDate(contractInfo.startTime, 'yyyy-MM-dd')}",
      "endTime": "#{formatDate(contractInfo.endTime, 'yyyy-MM-dd')}",
      "count": "#{emailParameter}",
      "contractTitle": "#{contractInfo.contractName}"
    },
    "emailTitle": "【合同到期提醒】#{contractInfo.fullName}(#{formatDate(contractInfo.startTime, 'yyyy-MM-dd')}至#{formatDate(contractInfo.endTime, 'yyyy-MM-dd')}，提前#{emailParameter}天提醒)",
    "languageCode": "zh"
  },
  
  "COMMISSION_NOTICE": {
    "description": "佣金通知",
    "dataSource": {
      "commissionInfo": {
        "service": "saleCenterClient",
        "method": "getCommissionInfo",
        "params": ["#{tableId}"],
        "resultType": "CommissionInfo"
      },
      "financeStaff": {
        "service": "permissionCenterClient",
        "method": "getStaffByRole",
        "params": ["FINANCE"],
        "resultType": "List<StaffVo>"
      }
    },
    "recipients": {
      "type": "composite",
      "sources": [
        {
          "type": "dynamic",
          "expression": "financeStaff.stream().map(staff -> staff.getId()).collect(Collectors.toList())"
        },
        {
          "type": "single",
          "expression": "commissionInfo.salesStaffId"
        }
      ],
      "recipientType": "staffId"
    },
    "templateParams": {
      "studentName": "#{commissionInfo.studentName}",
      "amount": "#{formatCurrency(commissionInfo.amount)}",
      "agentName": "#{commissionInfo.agentName}",
      "courseName": "#{commissionInfo.courseName}"
    },
    "emailTitle": "【佣金通知】#{commissionInfo.studentName} - #{commissionInfo.courseName}",
    "languageCode": "zh"
  }
}
```

#### 核心实现组件

##### 1. 配置加载器

```java
/**
 * 邮件配置加载器
 */
@Component
public class EmailConfigLoader {
    
    private static final String CONFIG_FILE_PATH = "classpath:config/email-config.json";
    
    @Value("${email.config.file:#{null}}")
    private String customConfigPath;
    
    private Map<String, EmailConfig> configCache = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void loadConfigs() {
        try {
            String configPath = StringUtils.isNotEmpty(customConfigPath) ? customConfigPath : CONFIG_FILE_PATH;
            Resource resource = new ClassPathResource(configPath);
            
            String configContent = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
            Type type = new TypeToken<Map<String, EmailConfig>>(){}.getType();
            Map<String, EmailConfig> configs = new Gson().fromJson(configContent, type);
            
            configCache.putAll(configs);
            log.info("成功加载邮件配置，共{}个模板", configs.size());
            
        } catch (Exception e) {
            log.error("加载邮件配置失败", e);
            throw new RuntimeException("邮件配置加载失败", e);
        }
    }
    
    public EmailConfig getConfig(String emailTypeKey) {
        EmailConfig config = configCache.get(emailTypeKey);
        if (config == null) {
            throw new GetServiceException("未找到邮件配置: " + emailTypeKey);
        }
        return config;
    }
    
    public void reloadConfig() {
        configCache.clear();
        loadConfigs();
    }
}

/**
 * 邮件配置对象
 */
@Data
public class EmailConfig {
    private String description;
    private Map<String, DataSourceConfig> dataSource;
    private RecipientsConfig recipients;
    private Map<String, String> templateParams;
    private String emailTitle;
    private String languageCode;
}

@Data
public class DataSourceConfig {
    private String service;
    private String method;
    private List<String> params;
    private String resultType;
}

@Data 
public class RecipientsConfig {
    private String type; // single, dynamic, composite, config
    private String expression;
    private List<RecipientsConfig> sources; // for composite type
    private String recipientType; // staffId, email
}
```

##### 2. 数据源执行器

```java
/**
 * 数据源执行器
 */
@Component
public class DataSourceExecutor {
    
    @Resource
    private ApplicationContext applicationContext;
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    public Map<String, Object> execute(Map<String, DataSourceConfig> dataSources, EmailSenderQueue emailSenderQueue) {
        Map<String, Object> context = buildBaseContext(emailSenderQueue);
        
        // 按依赖顺序执行数据源
        for (Map.Entry<String, DataSourceConfig> entry : dataSources.entrySet()) {
            String key = entry.getKey();
            DataSourceConfig config = entry.getValue();
            
            try {
                Object result = executeDataSource(config, context);
                context.put(key, result);
                log.debug("数据源执行成功: {} -> {}", key, result != null ? result.getClass().getSimpleName() : "null");
            } catch (Exception e) {
                log.error("数据源执行失败: {}", key, e);
                throw new GetServiceException("数据源执行失败: " + key + ", " + e.getMessage());
            }
        }
        
        return context;
    }
    
    private Map<String, Object> buildBaseContext(EmailSenderQueue emailSenderQueue) {
        Map<String, Object> context = new HashMap<>();
        context.put("agentId", emailSenderQueue.getFkTableId());
        context.put("tableId", emailSenderQueue.getFkTableId());
        context.put("emailParameter", emailSenderQueue.getEmailParameter());
        context.put("emailTypeKey", emailSenderQueue.getFkEmailTypeKey());
        return context;
    }
    
    private Object executeDataSource(DataSourceConfig config, Map<String, Object> context) {
        // 获取服务Bean
        Object service = applicationContext.getBean(config.getService());
        
        // 解析参数
        Object[] resolvedParams = resolveParams(config.getParams(), context);
        
        // 通过反射调用方法
        Method method = findMethod(service.getClass(), config.getMethod(), resolvedParams);
        method.setAccessible(true);
        
        Object result = ReflectionUtils.invokeMethod(method, service, resolvedParams);
        
        // 如果是Result包装类，提取数据
        if (result instanceof Result) {
            Result<?> resultWrapper = (Result<?>) result;
            if (!resultWrapper.isSuccess()) {
                throw new GetServiceException("服务调用失败: " + resultWrapper.getMessage());
            }
            return resultWrapper.getData();
        }
        
        return result;
    }
    
    private Object[] resolveParams(List<String> paramTemplates, Map<String, Object> context) {
        if (CollectionUtils.isEmpty(paramTemplates)) {
            return new Object[0];
        }
        
        Object[] params = new Object[paramTemplates.size()];
        for (int i = 0; i < paramTemplates.size(); i++) {
            String template = paramTemplates.get(i);
            params[i] = resolveParam(template, context);
        }
        return params;
    }
    
    private Object resolveParam(String template, Map<String, Object> context) {
        if (template.startsWith("#{") && template.endsWith("}")) {
            String expression = template.substring(2, template.length() - 1);
            return context.get(expression);
        }
        return template;
    }
    
    private Method findMethod(Class<?> clazz, String methodName, Object[] params) {
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName) && method.getParameterCount() == params.length) {
                return method;
            }
        }
        throw new GetServiceException("未找到方法: " + clazz.getSimpleName() + "." + methodName);
    }
}
```

##### 3. 表达式计算器

```java
/**
 * 表达式计算器
 */
@Component
public class ExpressionEvaluator {
    
    private final SpelExpressionParser parser = new SpelExpressionParser();
    private final StandardEvaluationContext evaluationContext;
    
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    
    public ExpressionEvaluator() {
        this.evaluationContext = new StandardEvaluationContext();
        registerFunctions();
    }
    
    public List<String> evaluateRecipients(RecipientsConfig config, Map<String, Object> context) {
        List<Object> recipients = new ArrayList<>();
        
        switch (config.getType()) {
            case "single":
                Object singleResult = evaluateExpression(config.getExpression(), context);
                if (singleResult != null) {
                    recipients.add(singleResult);
                }
                break;
                
            case "dynamic":
                Object dynamicResult = evaluateExpression(config.getExpression(), context);
                if (dynamicResult instanceof Collection) {
                    recipients.addAll((Collection<?>) dynamicResult);
                } else if (dynamicResult != null) {
                    recipients.add(dynamicResult);
                }
                break;
                
            case "composite":
                for (RecipientsConfig source : config.getSources()) {
                    recipients.addAll(evaluateRecipients(source, context));
                }
                break;
                
            case "config":
                Object configResult = evaluateExpression(config.getExpression(), context);
                if (configResult instanceof Collection) {
                    recipients.addAll((Collection<?>) configResult);
                }
                break;
        }
        
        // 转换为邮箱地址
        return convertToEmails(recipients, config.getRecipientType());
    }
    
    public Map<String, String> evaluateTemplateParams(Map<String, String> paramTemplates, 
                                                      Map<String, Object> context, String recipient) {
        Map<String, String> result = new HashMap<>();
        
        // 添加当前收件人到上下文
        context.put("recipient", recipient);
        
        for (Map.Entry<String, String> entry : paramTemplates.entrySet()) {
            String key = entry.getKey();
            String template = entry.getValue();
            
            String value = parseTemplate(template, context);
            result.put(key, value != null ? value : "");
        }
        
        return result;
    }
    
    public String evaluateEmailTitle(String titleTemplate, Map<String, Object> context) {
        return parseTemplate(titleTemplate, context);
    }
    
    private Object evaluateExpression(String expression, Map<String, Object> context) {
        try {
            Expression exp = parser.parseExpression(expression);
            
            // 设置上下文变量
            for (Map.Entry<String, Object> entry : context.entrySet()) {
                evaluationContext.setVariable(entry.getKey(), entry.getValue());
            }
            
            return exp.getValue(evaluationContext);
        } catch (Exception e) {
            log.error("表达式计算失败: {}", expression, e);
            throw new GetServiceException("表达式计算失败: " + expression);
        }
    }
    
    private String parseTemplate(String template, Map<String, Object> context) {
        if (StringUtils.isEmpty(template)) {
            return "";
        }
        
        // 解析 #{expression} 格式的表达式
        Pattern pattern = Pattern.compile("#\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String expression = matcher.group(1);
            Object value = evaluateExpression(expression, context);
            matcher.appendReplacement(sb, value != null ? value.toString() : "");
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    private List<String> convertToEmails(List<Object> recipients, String recipientType) {
        if ("email".equals(recipientType)) {
            return recipients.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        } else if ("staffId".equals(recipientType)) {
            Set<Long> staffIds = recipients.stream()
                .filter(Objects::nonNull)
                .map(obj -> Long.valueOf(obj.toString()))
                .collect(Collectors.toSet());
            
            List<StaffVo> staffList = permissionCenterClient.getStaffByIds(staffIds);
            return staffList.stream()
                .map(StaffVo::getEmail)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        }
        
        return Collections.emptyList();
    }
    
    private void registerFunctions() {
        // 注册自定义函数
        try {
            evaluationContext.registerFunction("formatDate", 
                ExpressionFunctions.class.getDeclaredMethod("formatDate", Date.class, String.class));
            evaluationContext.registerFunction("formatCurrency", 
                ExpressionFunctions.class.getDeclaredMethod("formatCurrency", Number.class));
            evaluationContext.registerFunction("extractNameFromEmail", 
                ExpressionFunctions.class.getDeclaredMethod("extractNameFromEmail", String.class));
            evaluationContext.registerFunction("getCurrentUser", 
                ExpressionFunctions.class.getDeclaredMethod("getCurrentUser", List.class, String.class));
            evaluationContext.registerFunction("getSupervisorId", 
                ExpressionFunctions.class.getDeclaredMethod("getSupervisorId", Long.class));
            evaluationContext.registerFunction("parseConfigStaffIds", 
                ExpressionFunctions.class.getDeclaredMethod("parseConfigStaffIds", String.class, String.class));
        } catch (NoSuchMethodException e) {
            log.error("注册自定义函数失败", e);
        }
    }
}

/**
 * 表达式自定义函数
 */
public class ExpressionFunctions {
    
    public static String formatDate(Date date, String pattern) {
        if (date == null) return "";
        return new SimpleDateFormat(pattern).format(date);
    }
    
    public static String formatCurrency(Number amount) {
        if (amount == null) return "0.00";
        return String.format("%.2f", amount.doubleValue());
    }
    
    public static String extractNameFromEmail(String email) {
        if (StringUtils.isEmpty(email)) return "用户";
        int atIndex = email.indexOf('@');
        return atIndex > 0 ? email.substring(0, atIndex) : "用户";
    }
    
    public static RegisterPartnerUserVo getCurrentUser(List<RegisterPartnerUserVo> users, String email) {
        return users.stream()
            .filter(user -> email.equals(user.getAccount()))
            .findFirst()
            .orElse(null);
    }
    
    public static Long getSupervisorId(Long staffId) {
        // 这里需要注入PermissionCenterClient，简化示例
        return null;
    }
    
    public static List<Long> parseConfigStaffIds(String configJson, String key) {
        try {
            JSONObject config = JSONUtil.parseObj(configJson);
            JSONArray staffArray = config.getJSONArray(key);
            List<Long> staffIds = new ArrayList<>();
            for (int i = 0; i < staffArray.size(); i++) {
                staffIds.add(staffArray.getLong(i));
            }
            return staffIds;
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }
}
```

##### 4. 统一邮件发送服务

```java
/**
 * 配置驱动的统一邮件发送服务
 */
@Service
public class ConfigDrivenEmailService {
    
    @Resource
    private EmailConfigLoader configLoader;
    
    @Resource
    private DataSourceExecutor dataSourceExecutor;
    
    @Resource
    private ExpressionEvaluator expressionEvaluator;
    
    @Resource
    private EmailTemplateMapper emailTemplateMapper;
    
    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;
    
    @Resource
    private RemindTaskQueueService remindTaskQueueService;
    
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        String emailTypeKey = emailSenderQueue.getFkEmailTypeKey();
        log.info("开始配置驱动邮件发送，队列ID: {}, 邮件类型: {}", 
                emailSenderQueue.getId(), emailTypeKey);
        
        StringJoiner failedEmails = new StringJoiner("; ");
        
        try {
            // 1. 加载配置
            EmailConfig config = configLoader.getConfig(emailTypeKey);
            
            // 2. 执行数据源，获取业务数据
            Map<String, Object> dataContext = dataSourceExecutor.execute(config.getDataSource(), emailSenderQueue);
            
            // 3. 计算收件人列表
            List<String> recipients = expressionEvaluator.evaluateRecipients(config.getRecipients(), dataContext);
            
            if (recipients.isEmpty()) {
                log.warn("未找到收件人，跳过邮件发送，队列ID: {}", emailSenderQueue.getId());
                return;
            }
            
            // 4. 获取邮件模板
            String baseTemplate = getEmailTemplate(config, dataContext);
            
            // 5. 计算邮件标题
            String emailTitle = expressionEvaluator.evaluateEmailTitle(config.getEmailTitle(), dataContext);
            
            // 6. 循环发送邮件
            StringJoiner emailsCombined = new StringJoiner(", ");
            for (String recipient : recipients) {
                try {
                    sendSingleEmail(emailSenderQueue, config, dataContext, baseTemplate, emailTitle, recipient);
                    emailsCombined.add(recipient);
                } catch (Exception e) {
                    failedEmails.add(recipient + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - 收件人: {}, 原因: {}", recipient, e.getMessage());
                }
            }
            
            // 7. 更新队列状态
            updateQueueStatus(emailSenderQueue, emailsCombined.toString(), failedEmails);
            
            log.info("配置驱动邮件发送完成，队列ID: {}, 成功: {}, 失败: {}", 
                    emailSenderQueue.getId(), recipients.size() - failedEmails.toString().split(";").length, 
                    failedEmails.toString().split(";").length);
            
        } catch (Exception e) {
            log.error("配置驱动邮件发送异常，队列ID: {}", emailSenderQueue.getId(), e);
            handleGlobalException(emailSenderQueue, e);
        }
    }
    
    private void sendSingleEmail(EmailSenderQueue emailSenderQueue, EmailConfig config, 
                                Map<String, Object> dataContext, String baseTemplate, 
                                String emailTitle, String recipient) {
        // 1. 为当前收件人计算模板参数
        Map<String, String> templateParams = expressionEvaluator.evaluateTemplateParams(
            config.getTemplateParams(), dataContext, recipient);
        
        // 2. 替换模板参数
        String personalizedTemplate = ReminderTemplateUtils.getReminderTemplate(templateParams, baseTemplate);
        
        // 3. 构建消息
        EmailSystemMQMessageDto messageDto = new EmailSystemMQMessageDto();
        messageDto.setEmailSenderQueueId(emailSenderQueue.getId());
        messageDto.setTitle(emailTitle);
        messageDto.setContent(personalizedTemplate);
        messageDto.setToEmail(recipient);
        
        // 4. 发送邮件
        remindTaskQueueService.sendSystemMail(messageDto);
    }
    
    private String getEmailTemplate(EmailConfig config, Map<String, Object> dataContext) {
        // 查询模板
        List<EmailTemplate> templates = emailTemplateMapper.selectList(
            Wrappers.<EmailTemplate>lambdaQuery()
                .eq(EmailTemplate::getEmailTypeKey, dataContext.get("emailTypeKey")));
        
        if (GeneralTool.isEmpty(templates)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        
        // 选择语言模板
        String template = config.getLanguageCode().equals("en") 
            ? templates.get(0).getEmailTemplateEn() 
            : templates.get(0).getEmailTemplate();
        
        // 处理标题占位符
        template = template.replace("#{taskTitle}", 
            expressionEvaluator.evaluateEmailTitle(config.getEmailTitle(), dataContext));
        
        // 处理父子模板
        return processParentTemplate(templates.get(0), template, config.getLanguageCode(), dataContext);
    }
    
    private String processParentTemplate(EmailTemplate emailTemplate, String template, 
                                        String languageCode, Map<String, Object> dataContext) {
        if (GeneralTool.isNotEmpty(emailTemplate.getFkParentEmailTemplateId()) 
            && emailTemplate.getFkParentEmailTemplateId() != 0) {
            
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(emailTemplate.getFkParentEmailTemplateId());
            Map<String, Object> parentParams = new HashMap<>(dataContext);
            parentParams.put("subtemplate", template);
            
            String parentTemplateContent = languageCode.equals("en") 
                ? parentTemplate.getEmailTemplateEn() 
                : parentTemplate.getEmailTemplate();
                
            return ReminderTemplateUtils.getReminderTemplate(parentParams, parentTemplateContent);
        }
        return template;
    }
    
    private void updateQueueStatus(EmailSenderQueue emailSenderQueue, String emailsCombined, StringJoiner failedEmails) {
        LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())
                    .set(EmailSenderQueue::getEmailTo, emailsCombined);
        
        if (failedEmails.length() > 0) {
            updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            log.warn("邮件发送部分失败，失败邮箱: {}", failedEmails.toString());
        }
        
        emailSenderQueueMapper.update(null, updateWrapper);
    }
    
    private void handleGlobalException(EmailSenderQueue emailSenderQueue, Exception e) {
        emailSenderQueue.setErrorMessage(e.getMessage());
        emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
        emailSenderQueue.setOperationStatus(-1);
        emailSenderQueueMapper.updateById(emailSenderQueue);
    }
}
```

##### 5. 工厂集成

```java
/**
 * 更新邮件工厂配置
 */
@Configuration
public class EmailFactoryConfiguration {
    
    @Resource
    private ConfigDrivenEmailService configDrivenEmailService;
    
    // ... 保留现有Helper的注入
    
    @Bean(name = "emailHelperMap")
    public Map<String, EmailAbstractHelper> emailHelperMap() {
        Map<String, EmailAbstractHelper> helperMap = new HashMap<>();
        
        // 现有的Helper映射（保持向后兼容）
        helperMap.put(EmailTemplateEnum.STUDENT_OFFER_ITEM_COMMISSION_NOTICE.getEmailTemplateKey(), 
                      studentOfferItemCommissionNoticeEmailHelper);
        // ... 其他现有映射
        
        // 新增配置驱动的Helper适配器
        helperMap.put(EmailTemplateEnum.AGENCY_APPLY_APPROVED.getEmailTemplateKey(), 
                      new ConfigDrivenEmailAdapter(configDrivenEmailService));
        helperMap.put(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey(),
                      new ConfigDrivenEmailAdapter(configDrivenEmailService));
        
        return helperMap;
    }
}

/**
 * 配置驱动邮件Helper适配器
 */
public class ConfigDrivenEmailAdapter extends EmailAbstractHelper {
    
    private final ConfigDrivenEmailService configDrivenEmailService;
    
    public ConfigDrivenEmailAdapter(ConfigDrivenEmailService configDrivenEmailService) {
        this.configDrivenEmailService = configDrivenEmailService;
    }
    
    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        configDrivenEmailService.sendMail(emailSenderQueue);
    }
    
    @Override
    public Object assembleEmailData(EmailSenderQueue emailSenderQueue) {
        // 配置驱动模式下不需要此方法
        return null;
    }
}
```

#### 使用示例

**添加新邮件模板（零代码）**：

只需在`email-config.json`中添加配置：

```json
{
  "STUDENT_GRADUATION_REMINDER": {
    "description": "学生毕业提醒",
    "dataSource": {
      "studentInfo": {
        "service": "saleCenterClient",
        "method": "getStudentById",
        "params": ["#{tableId}"],
        "resultType": "StudentVo"
      },
      "courseInfo": {
        "service": "institutionCenterClient", 
        "method": "getCourseInfo",
        "params": ["#{studentInfo.courseId}"],
        "resultType": "CourseVo"
      }
    },
    "recipients": {
      "type": "composite",
      "sources": [
        {
          "type": "single",
          "expression": "studentInfo.advisorId"
        },
        {
          "type": "dynamic",
          "expression": "getAcademicStaff(courseInfo.institutionId)"
        }
      ],
      "recipientType": "staffId"
    },
    "templateParams": {
      "studentName": "#{studentInfo.name}",
      "courseName": "#{courseInfo.name}",
      "graduationDate": "#{formatDate(studentInfo.graduationDate, 'yyyy-MM-dd')}",
      "institutionName": "#{courseInfo.institutionName}"
    },
    "emailTitle": "【毕业提醒】#{studentInfo.name} - #{courseInfo.name}",
    "languageCode": "zh"
  }
}
```

然后在工厂配置中添加一行映射：

```java
helperMap.put("STUDENT_GRADUATION_REMINDER", new ConfigDrivenEmailAdapter(configDrivenEmailService));
```

#### 方案3优缺点

**优点**：
- **零代码添加模板**: 新邮件类型只需JSON配置
- **高度灵活**: 支持复杂的数据源组合和表达式计算
- **可视化配置**: 业务逻辑清晰可见
- **易于维护**: 配置文件修改即时生效
- **扩展性强**: 可轻松添加新的数据源和函数

**缺点**：
- **学习成本**: 需要学习配置语法和表达式
- **调试困难**: 配置错误不易定位
- **性能考虑**: 反射和表达式计算有一定开销
- **类型安全**: 编译期无法检查配置正确性

---

## 3. 方案对比评估

### 3.1 评估维度

| 维度 | 权重 | 现有方案 | 方案1：统一服务 | 方案2：模板方法 | 方案3：配置驱动 |
|------|------|----------|----------------|----------------|----------------|
| **开发效率** | 25% | 2分 | 7分 | 6分 | 9分 |
| **维护成本** | 25% | 2分 | 8分 | 7分 | 9分 |
| **代码质量** | 20% | 3分 | 8分 | 7分 | 6分 |
| **扩展性** | 15% | 4分 | 7分 | 6分 | 9分 |
| **学习成本** | 10% | 8分 | 6分 | 7分 | 4分 |
| **性能影响** | 5% | 8分 | 7分 | 8分 | 6分 |
| **总分** | 100% | **3.45分** | **7.35分** | **6.6分** | **7.8分** |

### 3.2 详细对比分析

#### 开发效率对比

**现有方案（2分）**：
- 新增邮件模板需要创建Helper类、DTO类、工厂配置
- 大量重复代码编写
- 平均新增一个模板需要半天时间

**方案1：统一服务（7分）**：
- 只需创建数据组装器
- 减少90%重复代码
- 平均新增一个模板需要2小时

**方案2：模板方法（6分）**：
- 只需创建Helper子类
- 减少80%重复代码
- 平均新增一个模板需要3小时

**方案3：配置驱动（9分）**：
- 零代码，只需JSON配置
- 平均新增一个模板需要30分钟

#### 维护成本对比

**现有方案（2分）**：
- 修改公共逻辑需要同时修改20+个文件
- 回归测试工作量大
- 容易引入不一致性

**方案1：统一服务（8分）**：
- 公共逻辑统一维护
- 影响面小，测试工作量少
- 一处修改，全局生效

**方案2：模板方法（7分）**：
- 父类统一维护公共逻辑
- 需要考虑继承体系的影响
- 特殊场景处理复杂

**方案3：配置驱动（9分）**：
- 配置文件修改即时生效
- 业务逻辑可视化
- 维护工作量最小

#### 代码质量对比

**现有方案（3分）**：
- 大量重复代码，违反DRY原则
- 代码冗余率90%
- 可读性差

**方案1：统一服务（8分）**：
- 遵循DRY原则
- 职责分离清晰
- 类型安全

**方案2：模板方法（7分）**：
- 减少重复代码
- 继承层次清晰
- 部分场景处理复杂

**方案3：配置驱动（6分）**：
- 逻辑配置化，可读性好
- 运行时类型检查
- 调试相对困难

#### 扩展性对比

**现有方案（4分）**：
- 策略模式支持扩展
- 但每次扩展成本高
- 不支持动态配置

**方案1：统一服务（7分）**：
- 通过接口扩展
- 支持多种数据组装方式
- 扩展点明确

**方案2：模板方法（6分）**：
- 通过继承扩展
- 模板方法固定流程
- 特殊需求处理困难

**方案3：配置驱动（9分）**：
- 高度可配置
- 支持复杂业务规则
- 可动态添加数据源和函数

### 3.3 实施难度评估

#### 方案1：统一服务
- **实施难度**: 中等
- **风险等级**: 低
- **迁移策略**: 渐进式替换
- **时间估算**: 2-3周

#### 方案2：模板方法
- **实施难度**: 中等
- **风险等级**: 低
- **迁移策略**: 重构现有类
- **时间估算**: 3-4周

#### 方案3：配置驱动
- **实施难度**: 高
- **风险等级**: 中等
- **迁移策略**: 并行开发，逐步切换
- **时间估算**: 4-6周

### 3.4 推荐方案

基于综合评估，推荐采用**方案3：配置驱动架构**，理由如下：

1. **最佳的开发效率**: 零代码添加新模板
2. **最低的维护成本**: 配置化管理，修改即时生效
3. **最强的扩展性**: 支持复杂业务规则和动态配置
4. **长远收益**: 一次投入，长期受益

**实施建议**：
- 先实现核心框架和2-3个典型模板
- 验证架构可行性后，逐步迁移现有模板
- 建立配置规范和最佳实践文档
- 提供配置校验和调试工具

---

## 4. 实施路线图

### 4.1 第一阶段：框架搭建（2周）

#### 目标
建立配置驱动邮件系统的核心框架

#### 主要任务
1. **设计配置文件格式**
   - 定义JSON Schema
   - 设计数据源、收件人、模板参数的配置语法
   - 建立配置规范文档

2. **实现核心组件**
   - EmailConfigLoader: 配置加载器
   - DataSourceExecutor: 数据源执行器
   - ExpressionEvaluator: 表达式计算器
   - ConfigDrivenEmailService: 统一邮件服务

3. **建立基础设施**
   - 自定义函数库
   - 异常处理机制
   - 日志记录规范
   - 单元测试框架

#### 交付物
- 核心框架代码
- 配置文件Schema
- 基础单元测试
- 开发文档

### 4.2 第二阶段：试点验证（1周）

#### 目标
选择2-3个典型邮件模板进行试点实现

#### 试点模板选择
1. **AGENCY_APPLY_APPROVED**: 代理申请通过（收件人为邮箱地址）
2. **PROVIDER_CONTRACT_EXPIRE**: 供应商合同到期（收件人为员工ID）
3. **COMMISSION_NOTICE**: 佣金通知（复合收件人逻辑）

#### 主要任务
1. **配置实现**
   - 编写3个模板的JSON配置
   - 实现对应的数据源服务调用
   - 配置表达式和模板参数

2. **功能验证**
   - 端到端测试
   - 性能基准测试
   - 与现有系统对比验证

3. **问题优化**
   - 修复发现的问题
   - 优化性能和用户体验
   - 完善错误处理

#### 交付物
- 3个试点模板配置
- 功能验证报告
- 性能测试报告
- 问题修复记录

### 4.3 第三阶段：逐步迁移（6周）

#### 目标
将现有的20+个邮件模板逐步迁移到配置驱动架构

#### 迁移策略
1. **分批迁移**
   - 第1批：5个简单模板（1周）
   - 第2批：8个中等复杂模板（2周）
   - 第3批：7个复杂模板（2周）
   - 第4批：剩余模板和优化（1周）

2. **并行运行**
   - 新配置与原Helper并存
   - 通过开关控制使用哪种实现
   - 对比验证确保一致性

3. **风险控制**
   - 每批迁移后进行充分测试
   - 发现问题及时回滚
   - 保留原有实现作为备份

#### 迁移优先级
1. **高优先级**：常用且逻辑简单的模板
2. **中优先级**：使用频率中等的模板
3. **低优先级**：复杂或很少使用的模板

#### 主要任务
1. **配置编写**
   - 逐个模板编写JSON配置
   - 测试验证配置正确性
   - 优化配置结构

2. **功能验证**
   - 对比原有实现确保一致性
   - 进行回归测试
   - 性能基准对比

3. **清理优化**
   - 删除废弃的Helper类
   - 清理无用的DTO类
   - 优化工厂配置

#### 交付物
- 完整的邮件配置文件
- 迁移验证报告
- 性能对比报告
- 清理方案文档

### 4.4 第四阶段：优化完善（2周）

#### 目标
完善配置驱动邮件系统，提升开发体验

#### 主要任务
1. **开发工具**
   - 配置校验工具
   - 配置生成器
   - 调试工具

2. **监控告警**
   - 配置错误监控
   - 性能指标监控
   - 邮件发送状态监控

3. **文档完善**
   - 使用指南
   - 最佳实践
   - 故障排查手册

4. **培训推广**
   - 开发团队培训
   - 配置语法培训
   - 最佳实践分享

#### 交付物
- 开发工具集
- 监控告警系统
- 完整文档体系
- 培训材料

### 4.5 风险评估与应对

#### 主要风险

1. **技术风险**
   - **配置复杂度**: 表达式语法学习成本
   - **应对措施**: 提供详细文档和示例，建立最佳实践

2. **性能风险**
   - **反射开销**: 反射调用可能影响性能
   - **应对措施**: 缓存机制、预编译、性能基准测试

3. **兼容性风险**
   - **现有系统影响**: 迁移过程可能影响现有功能
   - **应对措施**: 并行运行、灰度切换、快速回滚

4. **维护风险**
   - **配置错误**: JSON配置错误不易发现
   - **应对措施**: Schema校验、单元测试、监控告警

#### 应对策略

1. **分阶段实施**: 降低单次变更风险
2. **并行运行**: 确保可快速回滚
3. **充分测试**: 每个阶段都进行完整测试
4. **监控告警**: 实时监控系统运行状态
5. **文档培训**: 确保团队掌握新系统

### 4.6 成功标准

#### 功能标准
- [ ] 所有现有邮件模板成功迁移
- [ ] 新增邮件模板时间缩短到30分钟以内
- [ ] 配置错误能够及时发现和修复
- [ ] 系统稳定性不低于现有方案

#### 性能标准
- [ ] 邮件发送性能不低于现有方案
- [ ] 系统响应时间在可接受范围内
- [ ] 内存使用量不显著增加

#### 维护标准
- [ ] 代码量减少80%以上
- [ ] 公共逻辑修改影响面明确可控
- [ ] 配置文件可读性和可维护性良好

#### 团队标准
- [ ] 开发团队掌握新系统使用方法
- [ ] 建立完善的配置规范和最佳实践
- [ ] 故障排查和问题解决流程清晰

---

## 5. 总结

### 5.1 核心问题

现有邮件系统存在严重的代码重复问题，90%的代码完全相同，只有数据组装逻辑有所不同。这导致了：
- 维护成本高：修改公共逻辑需要改动20+个文件
- 开发效率低：新增邮件模板需要大量重复编码
- 代码质量差：违反DRY原则，可读性和可维护性差

### 5.2 解决方案

提出了三种优化方案：
1. **统一邮件服务**：通过接口回调处理数据组装差异
2. **模板方法模式**：重构继承体系，提取公共逻辑到父类
3. **配置驱动架构**：将数据组装逻辑配置化，实现零代码添加模板

### 5.3 推荐方案

推荐采用**配置驱动架构**，具有以下优势：
- **开发效率最高**：新增模板只需JSON配置，时间从半天缩短到30分钟
- **维护成本最低**：配置文件修改即时生效，业务逻辑可视化
- **扩展性最强**：支持复杂业务规则和动态配置
- **长远收益**：一次投入，长期受益

### 5.4 预期收益

实施配置驱动架构后，预期获得以下收益：
- **代码量减少80%**：从6000+行减少到1200行左右
- **开发效率提升10倍**：新增模板时间从半天缩短到30分钟
- **维护成本降低90%**：公共逻辑统一维护，配置化管理
- **系统稳定性提升**：统一的错误处理和监控机制

### 5.5 实施建议

1. **分阶段实施**：框架搭建 → 试点验证 → 逐步迁移 → 优化完善
2. **风险控制**：并行运行、灰度切换、快速回滚
3. **团队准备**：技术培训、文档完善、最佳实践建立
4. **持续优化**：监控告警、性能优化、工具建设

通过实施配置驱动的邮件系统架构，可以从根本上解决当前系统的重复代码问题，大幅提升开发效率和系统可维护性，为业务快速发展提供强有力的技术支撑。

---

*本报告详细分析了现有邮件系统的问题，提出了三种可行的优化方案，并推荐了最佳的配置驱动架构。希望能为技术团队的架构决策提供有价值的参考。*