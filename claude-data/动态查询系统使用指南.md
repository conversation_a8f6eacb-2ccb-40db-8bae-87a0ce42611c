# 动态查询系统使用指南

## 概述

动态查询系统是基于注解驱动的查询条件构建器，可以根据DTO字段上的注解自动构建MyBatis Plus查询条件，大幅减少手动SQL拼接的工作量。

## 核心优势

- **代码减少90%**：从几十行XML减少到1行Java代码
- **类型安全**：编译时检查，避免字段名错误  
- **易于维护**：新增查询条件只需加注解
- **统一规范**：所有查询都使用相同的模式
- **性能优化**：反射结果缓存，避免重复解析

## 快速开始

### 1. 在DTO上添加查询注解

```java
import com.get.common.query.QueryField;
import com.get.common.query.QueryType;

@Data
public class OrderListDto {

    @QueryField(type = QueryType.EQ, column = "fk_insurance_company_id")
    @ApiModelProperty(value = "保险公司Id")
    private Long insuranceCompanyId;

    @QueryField(type = QueryType.LIKE)
    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @QueryField(type = QueryType.LIKE)
    @ApiModelProperty(value = "姓名")
    private String insurantName;

    @QueryField(type = QueryType.EQ)
    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @QueryField(type = QueryType.IN)
    @ApiModelProperty(value = "公司ID列表")
    private List<Long> companyIds;
}
```

### 2. 在Service中使用动态查询

```java
import com.get.common.query.QueryUtils;

@Service
public class OrderServiceImpl {
    
    /**
     * 使用动态查询的订单列表方法
     */
    public List<OrderVo> getOrderList(OrderListDto queryDto, Page page) {
        // 1. 构建动态查询条件（核心代码，只需1行！）
        LambdaQueryWrapper<InsuranceOrder> wrapper = QueryUtils.buildQuery(queryDto, InsuranceOrder.class);
        
        // 2. 添加自定义条件（可选）
        wrapper.orderByDesc(InsuranceOrder::getGmtCreate);
        
        // 3. 执行分页查询
        IPage<InsuranceOrder> iPage = GetCondition.getPage(
            PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())
        );
        
        IPage<InsuranceOrder> result = this.page(iPage, wrapper);
        
        // 4. 转换为VO
        List<OrderVo> orderList = result.getRecords().stream()
            .map(order -> BeanCopyUtils.objClone(order, OrderVo::new))
            .collect(Collectors.toList());
        
        page.setAll((int) result.getTotal());
        return orderList;
    }
}
```

### 3. 在Controller中使用

```java
@RestController
@RequestMapping("/order")
public class OrderController {
    
    @PostMapping("/list")
    public ResponseBo<OrderVo> orderList(@RequestBody SearchBean<OrderListDto> page) {
        List<OrderVo> list = orderService.getOrderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }
}
```

## 支持的查询类型

| 注解配置 | SQL效果 | 使用场景 |
|---------|---------|----------|
| `@QueryField(type = QueryType.EQ)` | `column = value` | 精确匹配 |
| `@QueryField(type = QueryType.LIKE)` | `column LIKE '%value%'` | 模糊搜索 |
| `@QueryField(type = QueryType.LIKE_LEFT)` | `column LIKE '%value'` | 左模糊 |
| `@QueryField(type = QueryType.LIKE_RIGHT)` | `column LIKE 'value%'` | 右模糊 |
| `@QueryField(type = QueryType.IN)` | `column IN (v1,v2,v3)` | 多选条件 |
| `@QueryField(type = QueryType.NOT_IN)` | `column NOT IN (v1,v2,v3)` | 排除条件 |
| `@QueryField(type = QueryType.GT)` | `column > value` | 大于 |
| `@QueryField(type = QueryType.GE)` | `column >= value` | 大于等于 |
| `@QueryField(type = QueryType.LT)` | `column < value` | 小于 |
| `@QueryField(type = QueryType.LE)` | `column <= value` | 小于等于 |
| `@QueryField(type = QueryType.BETWEEN)` | `column BETWEEN v1 AND v2` | 范围查询 |
| `@QueryField(type = QueryType.IS_NULL)` | `column IS NULL` | 空值查询 |
| `@QueryField(type = QueryType.IS_NOT_NULL)` | `column IS NOT NULL` | 非空查询 |

## 高级用法

### 1. 自定义字段映射

```java
@QueryField(type = QueryType.EQ, column = "fk_insurance_company_id")
private Long insuranceCompanyId;  // DTO字段名与数据库字段名不同时使用
```

### 2. 添加自定义查询条件

```java
LambdaQueryWrapper<InsuranceOrder> wrapper = QueryUtils.buildQuery(queryDto, InsuranceOrder.class, 
    w -> {
        // 添加权限控制
        w.eq(InsuranceOrder::getFkCompanyId, SecureUtil.getCompanyId());
        // 添加状态过滤
        w.in(InsuranceOrder::getOrderStatus, Arrays.asList(1, 2, 3));
        // 添加时间范围
        w.ge(InsuranceOrder::getGmtCreate, DateUtil.beginOfDay(new Date()));
    }
);
```

### 3. 范围查询示例

```java
@Data
public class OrderQueryDto {
    
    @QueryField(type = QueryType.BETWEEN)
    private List<Date> createTimeRange; // [开始时间, 结束时间]
    
    @QueryField(type = QueryType.BETWEEN, column = "premium_amount")
    private List<BigDecimal> amountRange; // [最小金额, 最大金额]
}
```

### 4. 复杂查询示例

```java
@Data
public class ComplexOrderQueryDto {
    
    @QueryField(type = QueryType.LIKE)
    private String customerName;
    
    @QueryField(type = QueryType.IN)
    private List<Long> companyIds;
    
    @QueryField(type = QueryType.GE, column = "premium_amount")
    private BigDecimal minAmount;
    
    @QueryField(type = QueryType.LE, column = "premium_amount")
    private BigDecimal maxAmount;
    
    @QueryField(type = QueryType.BETWEEN)
    private List<Date> createTimeRange;
    
    @QueryField(type = QueryType.EQ)
    private Integer orderStatus;
}
```

## 对比效果

### 传统XML方式（需要46行）：
```xml
<select id="selectOrderPage" resultType="OrderVo">
    select o.* from m_insurance_order o
    <where>
        <if test="param.insuranceCompanyId != null">
            and o.fk_insurance_company_id = #{param.insuranceCompanyId}
        </if>
        <if test="param.insuranceNum != null and param.insuranceNum != ''">
            and o.insurance_num like CONCAT('%', #{param.insuranceNum}, '%')
        </if>
        <if test="param.insurantName != null and param.insurantName != ''">
            and o.insurant_name like CONCAT('%', #{param.insurantName}, '%')
        </if>
        <!-- 更多条件... -->
    </where>
    order by o.gmt_create desc
</select>
```

### 动态查询方式（只需1行）：
```java
LambdaQueryWrapper<InsuranceOrder> wrapper = QueryUtils.buildQuery(queryDto, InsuranceOrder.class);
```

## 注意事项

1. **字段映射**：DTO字段名会自动转换为下划线命名，如 `insuranceCompanyId` → `insurance_company_id`
2. **空值处理**：默认忽略null值和空字符串，可通过 `ignoreEmpty = false` 改变
3. **集合类型**：IN查询需要使用List或Set类型的字段
4. **范围查询**：BETWEEN查询需要使用包含两个元素的List
5. **性能优化**：反射结果会被缓存，首次使用后性能接近手写代码

## 迁移建议

1. **保留原有方法**：现有的XML查询方法继续保留
2. **新增动态查询方法**：添加新的使用动态查询的方法
3. **逐步验证**：在测试环境验证查询结果的正确性
4. **性能测试**：对比新旧方法的性能差异
5. **团队培训**：确保团队成员了解新的使用方式

## 常见问题

**Q: 是否会影响现有代码？**
A: 不会。动态查询系统是新增功能，不会影响现有的XML查询方式。

**Q: 性能如何？**
A: 首次使用时会有反射开销，但结果会被缓存。后续使用性能接近手写代码。

**Q: 是否支持复杂的关联查询？**
A: 目前主要支持单表查询。复杂关联查询建议继续使用XML或结合自定义条件。

**Q: 如何调试生成的SQL？**
A: 可以通过MyBatis Plus的日志配置查看生成的SQL语句。
