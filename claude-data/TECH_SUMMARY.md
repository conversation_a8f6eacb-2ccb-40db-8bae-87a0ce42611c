# HTI Java AIS v1 项目核心技术总结文档

## 📋 项目概述

HTI Java AIS v1 是一个基于Spring Boot + Spring Cloud的企业级微服务架构教育行业SaaS系统，包含30个独立微服务，涵盖销售、财务、机构管理、学生服务等完整业务链条。

## 🔧 技术架构特点

- **微服务架构**：30个独立微服务，业务模块化
- **消息队列**：RocketMQ实现异步处理和系统解耦
- **工作流引擎**：Activiti 7处理业务审批流程
- **分布式任务**：XXL-JOB处理定时任务调度
- **文档处理**：完整的PDF/Word生成和处理能力
- **邮件系统**：企业级异步邮件发送解决方案

---

## 1. 📄 PDF处理功能

### 技术栈
- **核心库**：iText 5.5.13.2
- **中文支持**：itext-asian 5.2.0  
- **HTML转换**：xmlworker 5.5.13.2
- **图片处理**：batik-transcoder 1.14/1.16

### 依赖配置
```xml
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itextpdf</artifactId>
    <version>5.5.13.2</version>
</dependency>
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext-asian</artifactId>
    <version>5.2.0</version>
</dependency>
<dependency>
    <groupId>com.itextpdf.tool</groupId>
    <artifactId>xmlworker</artifactId>
    <version>5.5.13.2</version>
</dependency>
```

### 核心功能
1. **PDF动态生成**：基于iText API动态创建PDF内容
2. **PDF模板填充**：使用AcroFields填充PDF表单字段
3. **HTML转PDF**：使用XMLWorkerHelper将HTML转为PDF
4. **签名图片处理**：支持SVG Base64格式签名转PNG并插入PDF

### 主要使用场景
- **保险结算单PDF**：生成代理商保险佣金结算单（最完整实现）
- **代理合同PDF**：基于PDF模板填充代理合同信息，支持电子签名
- **学生服务费发票PDF**：生成学生留学服务费发票
- **HTML内容转PDF**：将富文本HTML内容转换为PDF格式

### 核心代码结构
```java
// PDF生成核心流程
Document document = new Document();
PdfWriter.getInstance(document, outputStream);
document.open();
buildPdfContent(document, businessData);
document.close();

// PDF模板填充
PdfReader reader = new PdfReader(templatePath);
PdfStamper stamper = new PdfStamper(reader, outputStream);
AcroFields form = stamper.getAcroFields();
// 设置中文字体
BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
form.addSubstitutionFont(bf);
form.setField(fieldName, fieldValue);
stamper.setFormFlattening(true);
```

### 核心工具类
- **位置**：`biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/PdfUtils.java`
- **位置**：`biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/DocUtils.java`

---

## 2. 📝 Word处理功能

### 技术栈
- **模板引擎**：FreeMarker 2.3.12
- **文档格式**：WordML (Word XML) + .ftl扩展名
- **多语言支持**：中文/英文/越南语三语言模板

### 依赖配置
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-freemarker</artifactId>
    <version>2.3.12.RELEASE</version>
</dependency>
```

### 核心功能
1. **Word文档生成**：基于FreeMarker模板引擎生成Word文档
2. **多语言模板**：支持三种语言版本的合同模板
3. **数字签名处理**：支持SVG签名转换并嵌入Word文档
4. **复杂格式支持**：包括表格、字体、段落格式等完整Word功能

### 主要使用场景
- **代理合同生成**：多语言版本合同自动生成（核心业务场景）
- **考试题目导出**：导出考试题目到Word文档
- **业务报告生成**：基于模板的各类业务报告

### 核心代码结构
```java
// Word生成核心流程
public static void createDoc(Map<String, Object> dataMap, HttpServletResponse response, 
                           String ftlName, String fileName) throws Exception {
    Configuration configuration = new Configuration();
    configuration.setDefaultEncoding("utf-8");
    configuration.setClassForTemplateLoading(DocUtils.class, "/template");
    Template template = configuration.getTemplate(ftlName);
    
    response.setCharacterEncoding("utf-8");
    response.setContentType("application/msword");
    response.setHeader("Content-Disposition", "attachment;filename=" + 
                      URLEncoder.encode(fileName + ".docx", "UTF-8"));
    
    StringWriter writer = new StringWriter();
    template.process(dataMap, writer);
    ByteArrayInputStream inputStream = new ByteArrayInputStream(
        writer.toString().getBytes(StandardCharsets.UTF_8));
    IOUtils.copy(inputStream, response.getOutputStream());
}

// 模板选择逻辑
if (contractVsion == 0) {
    DocUtils.createDoc(dataMap, response, "/contract.ftl", "contract");      // 中文
} else if (contractVsion == 2) {
    DocUtils.createDoc(dataMap, response, "/contractVi.ftl", "contract");    // 越南语  
} else {
    DocUtils.createDoc(dataMap, response, "/contractEng.ftl", "contract");   // 英文
}
```

### 模板文件结构
```
/src/main/resources/template/
├── contract.ftl        # 中文合同模板
├── contractEng.ftl     # 英文合同模板  
├── contractVi.ftl      # 越南语合同模板
└── examQuestion.ftl    # 考试题目模板
```

### 核心工具类
- **位置**：`biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/DocUtils.java`

---

## 3. 🚀 RocketMQ消息队列

### 技术栈
- **核心库**：rocketmq-spring-boot-starter 2.2.1
- **NameServer**：192.168.2.28:9876
- **专门服务**：ais-rocketmq-center微服务

### 配置文件
```yaml
rocketmq:
  name-server: 192.168.2.28:9876
  producer:
    group: mail_system_queue_topic_producer_group_dev
    send-message-timeout: 3000
    retry-times-when-send-failed: 3
  consumer:
    group: mail_system_queue_topic_consumer_group_dev
    max-reconsume-times: 3
    enable-msg-trace: true
```

### 核心功能
1. **异步邮件处理**：邮件发送任务的异步队列处理
2. **业务事件通知**：用户下线、佣金确认等业务事件分发
3. **定时任务触发**：与XXL-JOB结合的异步任务处理
4. **消息重试机制**：支持最大3次重试和死信队列

### 主要Topic和使用场景
```java
@Getter
public enum TopicEnum {
    MAIL_CUSTOM_QUEUE_TOPIC("mail_custom_queue_topic", "发送自定义邮件"),
    MAIL_SYSTEM_QUEUE_TOPIC("mail_system_queue_topic", "发送系统邮件"),
    MAIL_TASK_QUEUE_TOPIC("mail_task_queue_topic", "发送邮件任务");
    
    private String topic;
    private String consumerGroup;
}
```

### 核心代码结构
```java
// 消息生产者
@Component
public class EmailProducer {
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    
    public void sendSystemMail(EmailSystemMQMessageDto message) {
        rocketMQTemplate.asyncSend(TopicEnum.MAIL_SYSTEM_QUEUE_TOPIC.getTopic(), 
                                   message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("系统邮件消息发送成功，消息ID={}", sendResult.getMsgId());
                // 更新状态逻辑
            }
            
            @Override
            public void onException(Throwable throwable) {
                log.error("系统邮件消息发送失败，异常信息={}", throwable.getMessage());
            }
        });
    }
}

// 消息消费者
@RocketMQMessageListener(
    consumeThreadMax = 10,
    topic = "mail_system_queue_topic",
    consumerGroup = "mail_system_queue_topic_consumer_group",
    maxReconsumeTimes = 3,
    consumeMode = ConsumeMode.ORDERLY
)
public class SystemMailQueueListener implements RocketMQListener<EmailSystemMQMessageDto> {
    @Override
    public void onMessage(EmailSystemMQMessageDto message) {
        try {
            Result result = reminderCenterClient.sendSystemMail(message);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getMessage());
            }
        } catch (Exception e) {
            log.error("邮件发送失败，message={}", JSONObject.toJSONString(message));
            throw new RuntimeException(e); // 抛出异常触发重试
        }
    }
}
```

### 消息体结构
```java
// 系统邮件消息体
@Data
public class EmailSystemMQMessageDto {
    private Long emailSenderQueueId;
    private String title;
    private String content;
    private String toEmail;
    private String[] ccEmail;
}

// 自定义邮件消息体
@Data
public class EmailCustomMQMessageDto {
    private String host;            // SMTP服务器地址
    private Integer port;           // SMTP服务器端口
    private String userName;        // 认证用户名
    private String password;        // 认证密码
    private String title;           // 邮件标题
    private String[] toEmail;       // 主邮件接收人
    private String[] ccEmail;       // 抄送人邮件地址
    private String content;         // 邮件正文
    private Boolean isHtml;         // true:启用html  false:纯文本
}
```

---

## 4. 📧 邮件发送功能

### 技术栈
- **邮件库**：javax.mail 1.6.2
- **SMTP服务**：腾讯企业邮箱
- **模板引擎**：17种业务邮件模板
- **队列处理**：与RocketMQ深度集成

### 配置文件
```yaml
spring:
  mail:
    port: 465
    host: smtp.exmail.qq.com
    username: <EMAIL>
    password: FFpnYPwmAbPo5Xoa
    default-encoding: utf-8
    protocol: smtp
    properties:
      mail:
        username: <EMAIL>
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: true
            socketFactory:
              port: 465
              class: javax.net.ssl.SSLSocketFactory

emailSwitch: ${emailSwitch}  # 邮件开关控制
```

### 核心功能
1. **异步邮件发送**：基于RocketMQ的异步邮件发送架构
2. **多模板支持**：17种不同业务场景的邮件模板
3. **国际化支持**：完整的中英文双语邮件模板
4. **状态管理**：完整的发送状态跟踪和重试机制

### 主要邮件模板类型
```java
public enum EmailTemplateEnum {
    STUDENT_OFFER_ITEM_COMMISSION_NOTICE("STUDENT_OFFER_ITEM_COMMISSION_NOTICE", "学习计划佣金结算通知"),
    OFFER_ACCEPT_DUE_REMIND("OFFER_ACCEPT_DUE_REMIND", "接受Offer截止提醒"),
    PAY_DEPOSIT_DUE_REMIND("PAY_DEPOSIT_DUE_REMIND", "支付押金截止提醒"),
    COURSE_OPENING_REMINDER("COURSE_OPENING_REMINDER", "课程预计开课提醒"),
    WORK_LEAVE_WORKFLOW_REMINDER("WORK_LEAVE_WORKFLOW_REMINDER", "工休单工作流提醒"),
    EVENT_FEE_PLAN_CHANGE_REMINDER("EVENT_FEE_PLAN_CHANGE_REMINDER", "活动费用汇总收款计划变更提醒"),
    PROVIDER_CONTRACT_EXPIRE("PROVIDER_CONTRACT_EXPIRE", "供应商合同到期提醒"),
    INSURANCE_COMMISSION_NOTICE("INSURANCE_COMMISSION_NOTICE","留学保险佣金结算通知"),
    // ... 更多模板类型（共17种）
}
```

### 核心架构流程
```java
// 完整邮件发送流程
业务触发 → 创建EmailSenderQueue → 发送到RocketMQ → 
消费者处理 → 选择Helper → 模板处理 → SMTP发送 → 状态更新
```

### 邮件Helper系统
```java
// 邮件Helper工厂配置
@Configuration
public class EmailFactoryConfiguration {
    @Bean(name = "emailHelperMap")
    public Map<String, EmailAbstractHelper> payHelperMap() {
        Map<String, EmailAbstractHelper> payHelperMap = new HashMap<>();
        payHelperMap.put(EmailTemplateEnum.STUDENT_OFFER_ITEM_COMMISSION_NOTICE.getEmailTemplateKey(), 
                         studentOfferItemCommissionNoticeEmailHelper);
        // ... 配置其他Helper
        return payHelperMap;
    }
}

// 具体Helper实现
@Component("studentOfferItemCommissionNoticeEmailHelper")
public class StudentOfferItemCommissionNoticeEmailHelper extends EmailAbstractHelper {
    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            // 1. 组装邮件数据
            OfferItemCommissionNoticeDto reminderDto = assembleEmailData(emailSenderQueue);
            
            // 2. 设置邮件模板
            String template = setEmailTemplate(reminderDto);
            
            // 3. 批量发送邮件
            for (String email : reminderDto.getStaffEmailSet()) {
                EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                emailSystemMQMessageDto.setTitle(reminderDto.getEmailTitle());
                emailSystemMQMessageDto.setContent(template);
                emailSystemMQMessageDto.setToEmail(email);
                remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
            }
        } catch (Exception e) {
            log.error("邮件发送失败：", e);
            // 错误处理逻辑
        }
    }
}
```

### 模板处理系统
```java
// 模板变量替换工具
@Component
public class ReminderTemplateUtils {
    public static String getReminderTemplate(Map<String, String> map, String htmlText) {
        if (GeneralTool.isNotEmpty(map)) {
            for (String s : map.keySet()) {
                if (GeneralTool.isNotEmpty(map.get(s))) {
                    htmlText = htmlText.replace("${" + s + "}", map.get(s));
                } else {
                    htmlText = htmlText.replace("${" + s + "}", "");
                }
            }
        }
        return htmlText;
    }
}
```

---

## 5. ⏰ XXL-JOB定时任务

### 技术栈
- **核心库**：xxl-job-core 2.3.0
- **架构**：xxljob-admin调度中心 + xxljob执行器
- **任务数量**：29个不同类型的定时任务

### 配置文件
```yaml
xxl:
  job:
    accessToken: 'get-xxl-job-2022-02-09'
    admin:
      addresses: http://************:30021/xxl-job-admin
    executor:
      appname: get-job
      ip: 127.0.0.1
      port: 7018
      logpath: ../data/applogs/xxl-job/jobhandler
      logretentiondays: -1
```

### 核心配置类
```java
@Configuration
public class XxlJobConfig {
    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;
    
    @Value("${xxl.job.executor.appname}")
    private String appName;
    
    @Value("${xxl.job.executor.port}")
    private int port;
    
    @Value("${xxl.job.accessToken}")
    private String accessToken;
    
    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        return xxlJobSpringExecutor;
    }
}
```

### 核心功能
1. **任务调度管理**：统一的任务调度和监控
2. **分布式执行**：支持多实例分布式任务执行
3. **失败重试**：完善的任务失败重试机制
4. **日志追踪**：使用XxlJobHelper.log()统一日志记录

### 主要任务类型
- **AgentXxlJob**：代理过期检查任务
- **ContractXxlJob**：合同提醒任务
- **MessageXxlJob**：消息队列处理任务
- **FinanceExcelXxlJob**：财务Excel定时处理任务
- **FetchMailXxlJob**：邮件抓取任务
- **InstitutionCourseXxlJob**：RPA课程同步任务
- **StaffContractExpireXxlJob**：员工劳动合同过期提醒
- **SettlementInstallmentXxlJob**：结算分期任务
- **GetDayOfStaffBirthdayXxlJob**：员工生日提醒任务

### 标准任务格式
```java
@Component
public class AgentXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(AgentXxlJob.class);
    
    @Resource
    private ISaleCenterClient saleCenterClient;
    
    @XxlJob("agentTaskJobHandler")
    public void agentTaskJobHandler() throws Exception {
        XxlJobHelper.log("XXL-JOB, AgentTaskJobHandler start...");
        Result<Boolean> result = saleCenterClient.agentIsKeyExpired();
        XxlJobHelper.log(result.toString());
    }
}

// 带业务逻辑的任务模式
@Component
public class ContractXxlJob {
    @Resource
    private IContractXxlJobService contractXxlJobService;
    
    @XxlJob("contractTaskJobHandler")
    public void contractTaskJobHandler() throws Exception {
        XxlJobHelper.log("XXL-JOB, ContractTaskJobHandler start...");
        Boolean successful = contractXxlJobService.addContractRemind();
        if (successful) {
            XxlJobHelper.log("XXL-JOB, ContractTaskJobHandler success...");
        } else {
            XxlJobHelper.log("XXL-JOB, ContractTaskJobHandler fail...");
        }
    }
}
```

---

## 🔗 功能间集成关系

### 1. RocketMQ + 邮件系统集成
- 邮件发送完全基于RocketMQ异步处理
- 支持邮件发送失败重试和状态跟踪
- 17种邮件模板通过队列异步处理

### 2. XXL-JOB + RocketMQ集成
```java
// 定时任务通过RocketMQ发送业务消息
@XxlJob("fetchMailJob")
public void fetchMailJob() throws Exception {
    List<MailAccount> mailAccountList = mailAccountMapper.selectAccount();
    for (MailAccount mailAccount : mailAccountList) {
        rocketMQTemplate.asyncSend("fetch_mail_topic", mailAccount, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                logger.info("用户获取邮件消息发送成功，消息ID={}", sendResult.getMsgId());
            }
            @Override
            public void onException(Throwable throwable) {
                logger.error("用户获取邮件消息发送失败，异常信息={}", throwable.getMessage());
            }
        });
    }
}
```

### 3. PDF/Word + 邮件系统集成
- 生成的PDF/Word文档可作为邮件附件
- 文档生成完成后触发邮件通知
- 支持文档上传到文件服务器后发送下载链接

### 4. 统一的文件管理
```java
// 文件转换和上传
public static MultipartFile toMultipartFile(byte[] content, String paramName, 
                                           String originalName, String contentType) {
    return new ByteArrayMultipartFile(content, paramName, originalName, contentType);
}

// 上传到文件服务器
MultipartFile multipartFile = FileConvertUtils.toMultipartFile(
    pdfBytes, "file", fileName, "application/pdf");
mediaAndAttachedService.uploadAttached(multipartFile, businessId);
```

---

## 🚀 技术优势

### 1. 企业级特性
- **高可靠性**：完善的异常处理和重试机制
- **高可扩展性**：微服务架构便于功能扩展
- **高性能**：异步处理和消息队列优化
- **监控完善**：详细的日志记录和状态跟踪

### 2. 业务适应性
- **多语言支持**：国际化的模板和内容处理
- **多格式支持**：PDF、Word、HTML等多种格式
- **业务解耦**：消息队列实现业务模块间解耦
- **灵活配置**：多环境配置和开关控制

### 3. 开发友好性
- **代码复用**：统一的工具类和设计模式
- **易于维护**：清晰的代码结构和注释规范
- **便于测试**：完善的异常处理和日志记录
- **文档完整**：详细的使用案例和实现说明

---

## 📝 最佳实践建议

### 1. 开发规范
- 遵循项目既有的代码风格和架构决策
- 优先使用现有功能，避免重复造轮子
- 统一使用项目的异常处理和国际化机制
- 所有重要操作必须记录日志

### 2. 异常处理规范
```java
// 统一异常类：只使用 GetServiceException 作为业务异常类
throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));

// 带参数异常
throw new GetServiceException(LocaleMessageUtils.getFormatMessage("agent_file_download_license_failed", agentName));
```

### 3. 性能优化
- 大文档生成使用异步处理
- 合理使用缓存避免重复计算
- 批量操作避免在循环中进行数据库或网络调用
- 及时释放资源防止内存泄漏

### 4. 错误处理
- 使用统一的GetServiceException异常类
- 异常信息通过国际化资源文件配置
- 重要流程节点必须记录error日志
- 提供合理的重试机制和降级方案

### 5. 日志处理规范
```java
// 强制日志规则
log.error("PDF生成异常", e);  // 所有异常处理语句之前必须使用 log.error()
log.info("任务开始执行");      // 关键业务节点或流程开始前必须使用 log.info()
```

---

## 📍 重要文件位置

### PDF处理相关
- **PdfUtils**: `biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/PdfUtils.java`
- **DocUtils**: `biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/DocUtils.java`

### Word处理相关
- **DocUtils**: `biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/DocUtils.java`
- **模板文件**: `src/main/resources/template/`

### RocketMQ相关
- **EmailProducer**: `sys-service/ais-rocketmq-center/src/main/java/com/get/rocketmqcenter/producer/EmailProducer.java`
- **TopicEnum**: `sys-service-ap/ais-rocketmq-center-ap/src/main/java/com/get/rocketmqcenter/enums/TopicEnum.java`

### 邮件系统相关
- **EmailTemplateEnum**: `sys-service-ap/ais-reminder-center-ap/src/main/java/com/get/remindercenter/enums/EmailTemplateEnum.java`
- **EmailHelper**: `sys-service/ais-reminder-center/src/main/java/com/get/remindercenter/service/email/`

### XXL-JOB相关
- **XxlJobConfig**: `xxljob/src/main/java/com/get/xxljob/config/XxlJobConfig.java`
- **任务类**: `xxljob/src/main/java/com/get/xxljob/service/`

## 6. 📁 文件上传功能

### 技术栈
- **核心服务**：file-center专门的文件管理微服务
- **存储方案**：腾讯云COS (Cloud Object Storage)
- **邮件库**：javax.mail 1.6.2
- **文件处理**：MultipartFile + UUID文件命名

### 依赖配置
```yaml
spring:
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-file-size: 200MB      # 单个文件最大200MB
      max-request-size: 300MB   # 请求总大小最大300MB

# 腾讯云COS配置
spring:
  tencentcloudimage:
    bucketname: hti-ais-images-dev-1301376564  # 公开桶
  tencentcloudfile:
    bucketname: hti-ais-files-dev-1301376564   # 私密桶
  tencentCloudShareFile:
    bucketname: hti-ais-files-dev-1301376564   # 共享私密桶
```

### 核心功能
1. **多桶存储策略**：公开桶、私密桶、共享桶、HTI专用桶
2. **业务隔离存储**：不同业务模块使用独立的文件表和存储路径
3. **统一文件管理**：通过file-center服务统一处理文件上传
4. **完善安全控制**：文件类型验证、大小限制、权限验证

### 主要使用场景
- **图片上传**：用户头像、机构LOGO、产品图片等（公开桶）
- **文档上传**：合同文档、学生资料、财务凭证等（私密桶）
- **附件管理**：邮件附件、报告附件、各类业务附件
- **临时文件**：PDF生成、Word导出等临时文件处理

### 核心代码结构
```java
// 文件上传核心接口
@PostMapping(value = "upload")
public ResponseBo upload(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
    return new ListResponseBo<>(this.fileService.upload(files, type));
}

@PostMapping(value = "uploadAppendix")
public ResponseBo uploadAppendix(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type) {
    return new ListResponseBo<>(this.fileService.uploadAppendix(files, type));
}

// 文件路径生成策略
public static String getFilePath(MultipartFile file) {
    String fileFileName = file.getOriginalFilename();
    String dateString = getDateString(); // /yyyy/MM/dd/
    String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
    String fileurl = FILE_ROOT_PATH + dateString + fileName; // /files/2024/01/15/uuid.jpg
    return fileurl;
}

// 文件验证机制
private void validatefile(MultipartFile[] files) {
    for (MultipartFile file : files) {
        String filename = file.getOriginalFilename();
        String substring = filename.substring(filename.lastIndexOf(".")).toLowerCase();
        
        // 允许的文件类型
        String regexfile = "^(.doc|.pdf|.txt|.docx|.xlsx|.xls|.ppt|.jpg|.png|.gif|...)$";
        if (!substring.matches(regexfile)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_format_error"));
        }
    }
}
```

### 完整上传流程
```java
// 1. 前端发起请求：POST /sale/media/uploadAttached
// 2. 业务服务处理：MediaAndAttachedController.uploadAttached()
// 3. Feign调用：IFileCenterClient.uploadAppendix()
// 4. 文件中心处理：FileServiceImpl.uploadAppendix()
// 5. 保存元数据：根据type保存到对应的业务文件表
// 6. 上传云端：TencentCloudServiceImpl.uploadObject()

@Service
public class FileServiceImpl implements IFileService {
    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] files, String type) {
        validatefile(files);
        List<FileDto> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file);
            String bucketName = determineBucketName(type); // 根据业务类型选择存储桶
            FileDto fileDto = saveFileInfo(bucketName, file, fileurl, type, getAppendixPerfix(), false);
            datas.add(fileDto);
        }
        return datas;
    }
}
```

### 业务文件表设计
系统为不同业务模块设计了独立的文件表：
- **file_sale** - 销售中心文件
- **file_finance** - 财务中心文件  
- **file_institution** - 机构中心文件
- **file_permission** - 权限中心文件
- **file_exam** - 考试中心文件

### 核心工具类
- **位置**：`sys-service/file-center/src/main/java/com/get/filecenter/service/impl/FileServiceImpl.java`
- **Feign客户端**：`sys-service-ap/file-center-ap/src/main/java/com/get/filecenter/feign/IFileCenterClient.java`

---

## 7. 📱 二维码处理功能

### 技术栈
- **核心库**：Google ZXing 3.0.0
- **微信支付**：weixin-java-pay 4.1.5.B
- **微信小程序**：weixin-java-miniapp 4.1.0
- **图片处理**：MatrixToImageWriter

### 依赖配置
```xml
<!-- 二维码生成核心库 -->
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>core</artifactId>
    <version>3.0.0</version>
</dependency>

<!-- 微信支付SDK -->
<dependency>
    <groupId>com.github.binarywang</groupId>
    <artifactId>weixin-java-pay</artifactId>
    <version>4.1.5.B</version>
</dependency>

<!-- 微信小程序SDK -->
<dependency>
    <groupId>com.github.binarywang</groupId>
    <artifactId>weixin-java-miniapp</artifactId>
    <version>4.1.0</version>
</dependency>
```

### 核心功能
1. **支付二维码生成**：基于ZXing的微信支付二维码生成
2. **小程序码生成**：基于微信SDK的小程序二维码生成
3. **样式定制**：支持尺寸、颜色、容错级别等参数配置
4. **安全加密**：小程序配置信息AES加密存储

### 主要使用场景
- **微信支付二维码**：会议酒店预订支付、学费支付等
- **微信小程序码**：考试答题入口、活动参与入口等
- **分享二维码**：课程分享、机构推广等
- **登录二维码**：移动端扫码登录等

### 核心代码结构
```java
// ZXing二维码生成工具
public static BitMatrix createCode(String content) {
    int width = 200;
    int height = 200;
    
    Map<EncodeHintType, Object> hints = new HashMap<>();
    hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
    hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H); // 高级容错
    hints.put(EncodeHintType.MARGIN, 1); // 白边宽度
    
    BitMatrix bitMatrix = new MultiFormatWriter().encode(content,
            BarcodeFormat.QR_CODE, width, height, hints);
    return bitMatrix;
}

// 微信支付二维码生成完整流程
@Override
public void generateOrders(ConventionHotelPayDto payDto, HttpServletResponse response) throws IOException {
    // 1. 生成订单编号
    String orderCode = getOrderCode();
    
    // 2. 计算支付金额
    BigDecimal amount = calculateAmount(payDto);
    
    // 3. 创建微信支付订单
    WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request()
            .setAppid(wxPayService.getConfig().getAppId())
            .setMchid(wxPayService.getConfig().getMchId())
            .setDescription("酒店房费-" + price + "/晚")
            .setOutTradeNo(orderCode)
            .setNotifyUrl("https://convention.ht-international.net/sale/annualReservationForm/notifyWeiXinPay")
            .setAmount(new WxPayUnifiedOrderV3Request.Amount().setTotal(amount.intValue()));
    
    // 4. 调用微信统一下单API
    String payUrl = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, request);
    
    // 5. 生成二维码并输出到响应流
    BitMatrix bitMatrix = MyStringUtils.createCode(payUrl);
    MatrixToImageWriter.writeToStream(bitMatrix, "jpg", response.getOutputStream());
}

// 微信小程序码生成
@Override
public String createQrCode(Long examinationPaperId) {
    // 1. 获取考试配置
    ExaminationPaper examinationPaper = examinationPaperMapper.selectById(examinationPaperId);
    
    // 2. 获取微信小程序配置（加密存储）
    ConfigVo configVo = permissionCenterClient.getConfigValueByConfigKey("EXAM_QRCODE_WECHAT_CONFIG", fkExaminationId);
    
    // 3. 解密配置信息
    String appId = AESUtils.Decrypt(configVo.getValue3(), AESConstant.AESKEY);
    String appSecret = AESUtils.Decrypt(configVo.getValue4(), AESConstant.AESKEY);
    
    // 4. 配置微信小程序服务
    WxMaService wxMaService = new WxMaServiceImpl();
    WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
    config.setAppid(appId);
    config.setSecret(appSecret);
    wxMaService.setWxMaConfig(config);
    
    // 5. 构建小程序页面路径
    String pathUrl = configVo.getValue2() + examinationPaperId;
    
    // 6. 设置二维码样式（黑色线条）
    WxMaCodeLineColor lineColor = new WxMaCodeLineColor("0", "0", "0");
    
    // 7. 生成小程序码
    byte[] qrCodeBytes = wxMaService.getQrcodeService().createWxaCodeBytes(pathUrl, 430, false, lineColor, false);
    
    // 8. 转换为Base64返回
    return Base64.encodeBase64String(qrCodeBytes);
}
```

### 二维码参数配置

**ZXing二维码参数：**
- **尺寸**: 200x200像素
- **字符集**: UTF-8
- **容错级别**: ErrorCorrectionLevel.H (高级容错)
- **白边宽度**: 1像素
- **格式**: QR_CODE

**微信小程序码参数：**
- **尺寸**: 430像素
- **线条颜色**: 黑色 (0,0,0)
- **透明底色**: false
- **auto_color**: false（手动指定线条颜色）

### 核心工具类
- **位置**：`biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/MyStringUtils.java`
- **支付处理**：`biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AnnualReservationFormServiceImpl.java`
- **小程序码**：`biz-service/ais-exam-center/src/main/java/com/get/examcenter/service/impl/ExaminationPaperImpl.java`

---

## 🔗 功能间集成关系

### 1. RocketMQ + 邮件系统集成
- 邮件发送完全基于RocketMQ异步处理
- 支持邮件发送失败重试和状态跟踪
- 17种邮件模板通过队列异步处理

### 2. XXL-JOB + RocketMQ集成
```java
// 定时任务通过RocketMQ发送业务消息
@XxlJob("fetchMailJob")
public void fetchMailJob() throws Exception {
    List<MailAccount> mailAccountList = mailAccountMapper.selectAccount();
    for (MailAccount mailAccount : mailAccountList) {
        rocketMQTemplate.asyncSend("fetch_mail_topic", mailAccount, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                logger.info("用户获取邮件消息发送成功，消息ID={}", sendResult.getMsgId());
            }
            @Override
            public void onException(Throwable throwable) {
                logger.error("用户获取邮件消息发送失败，异常信息={}", throwable.getMessage());
            }
        });
    }
}
```

### 3. PDF/Word + 邮件系统集成
- 生成的PDF/Word文档可作为邮件附件
- 文档生成完成后触发邮件通知
- 支持文档上传到文件服务器后发送下载链接

### 4. 文件上传 + 文档生成集成
```java
// 文档生成后自动上传到文件服务器
ByteArrayOutputStream baos = new ByteArrayOutputStream();
// ... PDF生成逻辑
MultipartFile multipartFile = FileConvertUtils.toMultipartFile(
    baos.toByteArray(), "file", fileName, "application/pdf");
mediaAndAttachedService.uploadAttached(multipartFile, businessId);
```

### 5. 二维码 + 支付系统集成
- 二维码生成与微信支付深度集成
- 支付成功后通过回调更新订单状态
- 支持二维码过期时间和支付状态监控

---

## 🚀 技术优势

### 1. 企业级特性
- **高可靠性**：完善的异常处理和重试机制
- **高可扩展性**：微服务架构便于功能扩展
- **高性能**：异步处理和消息队列优化
- **监控完善**：详细的日志记录和状态跟踪

### 2. 业务适应性
- **多语言支持**：国际化的模板和内容处理
- **多格式支持**：PDF、Word、HTML等多种格式
- **业务解耦**：消息队列实现业务模块间解耦
- **灵活配置**：多环境配置和开关控制

### 3. 开发友好性
- **代码复用**：统一的工具类和设计模式
- **易于维护**：清晰的代码结构和注释规范
- **便于测试**：完善的异常处理和日志记录
- **文档完整**：详细的使用案例和实现说明

---

## 📝 最佳实践建议

### 1. 开发规范
- 遵循项目既有的代码风格和架构决策
- 优先使用现有功能，避免重复造轮子
- 统一使用项目的异常处理和国际化机制
- 所有重要操作必须记录日志

### 2. 异常处理规范
```java
// 统一异常类：只使用 GetServiceException 作为业务异常类
throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));

// 带参数异常
throw new GetServiceException(LocaleMessageUtils.getFormatMessage("agent_file_download_license_failed", agentName));
```

### 3. 性能优化
- 大文档生成使用异步处理
- 合理使用缓存避免重复计算
- 批量操作避免在循环中进行数据库或网络调用
- 及时释放资源防止内存泄漏

### 4. 错误处理
- 使用统一的GetServiceException异常类
- 异常信息通过国际化资源文件配置
- 重要流程节点必须记录error日志
- 提供合理的重试机制和降级方案

### 5. 日志处理规范
```java
// 强制日志规则
log.error("PDF生成异常", e);  // 所有异常处理语句之前必须使用 log.error()
log.info("任务开始执行");      // 关键业务节点或流程开始前必须使用 log.info()
```

---

## 📍 重要文件位置

### PDF处理相关
- **PdfUtils**: `biz-service/ais-insurance-center/src/main/java/com/get/insurancecenter/utils/PdfUtils.java`
- **DocUtils**: `biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/DocUtils.java`

### Word处理相关
- **DocUtils**: `biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/DocUtils.java`
- **模板文件**: `src/main/resources/template/`

### RocketMQ相关
- **EmailProducer**: `sys-service/ais-rocketmq-center/src/main/java/com/get/rocketmqcenter/producer/EmailProducer.java`
- **TopicEnum**: `sys-service-ap/ais-rocketmq-center-ap/src/main/java/com/get/rocketmqcenter/enums/TopicEnum.java`

### 邮件系统相关
- **EmailTemplateEnum**: `sys-service-ap/ais-reminder-center-ap/src/main/java/com/get/remindercenter/enums/EmailTemplateEnum.java`
- **EmailHelper**: `sys-service/ais-reminder-center/src/main/java/com/get/remindercenter/service/email/`

### XXL-JOB相关
- **XxlJobConfig**: `xxljob/src/main/java/com/get/xxljob/config/XxlJobConfig.java`
- **任务类**: `xxljob/src/main/java/com/get/xxljob/service/`

### 文件上传相关
- **FileServiceImpl**: `sys-service/file-center/src/main/java/com/get/filecenter/service/impl/FileServiceImpl.java`
- **IFileCenterClient**: `sys-service-ap/file-center-ap/src/main/java/com/get/filecenter/feign/IFileCenterClient.java`

### 二维码处理相关
- **MyStringUtils**: `biz-service/ais-sale-center/src/main/java/com/get/salecenter/utils/MyStringUtils.java`
- **支付二维码**: `biz-service/ais-sale-center/src/main/java/com/get/salecenter/service/impl/AnnualReservationFormServiceImpl.java`
- **小程序码**: `biz-service/ais-exam-center/src/main/java/com/get/examcenter/service/impl/ExaminationPaperImpl.java`

---

这套技术方案为教育行业SaaS平台提供了完整的文档处理、消息队列、邮件发送、任务调度、文件上传和二维码处理解决方案，具有很强的实用价值和参考意义。在开发新功能时，建议优先参考这些成熟的实现方案，确保系统的一致性和可维护性。