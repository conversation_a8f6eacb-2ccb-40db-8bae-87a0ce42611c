# 邮件发送流程使用指南

## 概述

本文档详细描述了基于 `EmailSenderUtils` 工具类的邮件发送完整流程，包括前提条件、配置要求、使用步骤和实际案例。

## 前提条件和要求

### 1. 邮件模板数据库配置

**数据库表**：`u_email_template`

**必需字段**：
- `email_type_key`：邮件模板类型标识（如：`AGENCY_APPLY_APPROVED`）
- `email_template`：中文邮件模板内容（HTML格式）
- `email_template_en`：英文邮件模板内容（HTML格式）
- `fk_parent_email_template_id`：父模板ID（可选，用于模板继承）

**模板内容示例**：
```html
<div class="content">
    <h2 class="title">代理申请审核通过通知</h2>
    <div class="desc">
        <div>
            ${personalName}，您好:<br/>
            提交的申请已审核通过，请您登录小程序完成合同签署<br/>
            以下为华通伙伴小程序的初始账号和密码:<br/>
            账号：${account}<br/>
            密码：${password}<br/>
            小程序链接：<a href="${taskLink}">点击进入</a><br/>
        </div>
    </div>
</div>
申请代理：${name}
```

### 2. 枚举类型配置

**EmailTemplateEnum** 中需要定义对应的枚举值：
```java
AGENCY_APPLY_APPROVED("AGENCY_APPLY_APPROVED", "代理申请通过通知")
```

### 3. 邮件处理器配置

**EmailFactoryConfiguration** 中需要配置处理器映射：
```java
payHelperMap.put(EmailTemplateEnum.AGENCY_APPLY_APPROVED.getEmailTemplateKey(), partnerUserEmailHelper);
```

## 邮件发送工具使用指南

### 1. 核心工具类

#### EmailSenderUtils
- **作用**：统一的邮件发送工具类
- **位置**：`com.get.salecenter.utils.EmailSenderUtils`
- **主要方法**：
  - `sendSingleEmail(EmailSendContext context)`：发送单个邮件
  - `sendBatchEmails(List<EmailSendContext> contexts, Long businessId)`：批量发送邮件

#### EmailSendContext
- **作用**：邮件发送上下文数据传输对象
- **位置**：`com.get.salecenter.dto.EmailSendContext`
- **主要字段**：
  - `projectKey`：项目标识
  - `tableId`：关联业务表主键
  - `tableName`：关联业务表名
  - `recipient`：收件人邮箱
  - `title`：邮件标题
  - `emailTemplate`：邮件模板枚举
  - `parameters`：邮件参数Map
  - `operationTime`：执行时间（可选）

### 2. 参数传递规范

#### 必需参数
- **personalName**：收件人姓名
- **account**：账号信息
- **password**：密码信息
- **taskLink**：任务链接
- **name**：业务名称（如代理名称）

#### 可选参数
- **versionValue**：语言版本（zh/en，默认zh）
- **id**：业务ID（对应模板中的${$id}）

## 完整流程步骤

### 步骤1：准备邮件参数
```java
// 构建邮件参数Map
Map<String, String> emailParams = new HashMap<>();
emailParams.put("personalName", "张三");           // 收件人姓名
emailParams.put("name", "ABC教育机构");            // 代理名称
emailParams.put("account", "<EMAIL>");  // 账号
emailParams.put("password", "temp123456");        // 密码
emailParams.put("taskLink", "https://miniprogram-link.com"); // 小程序链接
emailParams.put("versionValue", "zh");           // 语言版本
```

### 步骤2：构建邮件上下文
```java
EmailSendContext context = EmailSendContext.builder()
    .projectKey(ProjectKeyEnum.SALE_CENTER)           // 项目标识
    .tableId(12345L)                                  // 关联业务ID
    .tableName(TableEnum.SALE_AGENT)                  // 关联业务表
    .recipient("<EMAIL>")                   // 收件人邮箱
    .title("代理申请审核通过通知")                      // 邮件标题
    .emailTemplate(EmailTemplateEnum.AGENCY_APPLY_APPROVED) // 邮件模板
    .parameters(emailParams)                          // 邮件参数
    .operationTime(new Date())                        // 执行时间（可选）
    .build();
```

### 步骤3：发送邮件
```java
// 单个邮件发送
emailSenderUtils.sendSingleEmail(context);

// 或者批量发送
List<EmailSendContext> contexts = Arrays.asList(context);
emailSenderUtils.sendBatchEmails(contexts, 12345L);
```

### 步骤4：系统自动处理流程

1. **EmailSenderUtils 处理**：
   - 构建 `EmailSenderQueue` 对象
   - 设置业务关联信息
   - JSON序列化邮件参数
   - 调用 `reminderCenterClient.batchAddEmailQueue()`

2. **数据库存储**：
   - 邮件任务保存到 `m_email_sender_queue` 表
   - 状态设置为 `0`（待处理）

3. **XXL-JOB 定时扫描**：
   - `sendEmailScheduleTask` 定时任务扫描待处理邮件
   - 筛选条件：`operation_status = 0` 且 `operation_time <= 当前时间`

4. **RocketMQ 消息队列**：
   - 扫描到的邮件任务发送到 `mail_system_queue_topic`
   - 使用 `mail_system_queue_topic_consumer_group` 消费组

5. **邮件处理器处理**：
   - `PartnerUserEmailHelper` 消费MQ消息
   - 从数据库获取邮件模板内容
   - 使用 `ReminderTemplateUtils.getReminderTemplate()` 进行参数替换
   - 发送邮件到SMTP服务器

6. **状态更新**：
   - 成功：状态更新为 `2`
   - 失败：状态更新为 `-1`，重试次数+1

## 实际使用案例

### 案例：代理申请审核通过邮件

```java
/**
 * 发送代理申请审核通过邮件
 */
public void sendAgentApprovalEmail(Long agentId, String agentName, String userEmail, String userName, String account, String password) {
    try {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", userName);
        emailParams.put("name", agentName);
        emailParams.put("account", account);
        emailParams.put("password", password);
        emailParams.put("taskLink", buildTaskLink(agentId));
        emailParams.put("versionValue", "zh");
        
        // 构建邮件上下文
        EmailSendContext context = EmailSendContext.builder()
            .projectKey(ProjectKeyEnum.SALE_CENTER)
            .tableId(agentId)
            .tableName(TableEnum.SALE_AGENT)
            .recipient(userEmail)
            .title("代理申请审核通过通知")
            .emailTemplate(EmailTemplateEnum.AGENCY_APPLY_APPROVED)
            .parameters(emailParams)
            .build();
        
        // 发送邮件
        emailSenderUtils.sendSingleEmail(context);
        
        log.info("代理申请审核通过邮件发送成功，代理ID: {}, 收件人: {}", agentId, userEmail);
        
    } catch (Exception e) {
        log.error("代理申请审核通过邮件发送失败，代理ID: {}, 收件人: {}", agentId, userEmail, e);
        // 不抛异常，避免影响主业务流程
    }
}

/**
 * 构建任务链接
 */
private String buildTaskLink(Long agentId) {
    return "http://************:9005/apply-agent-online-form/renewal?id=" + agentId;
}
```

### 案例：批量发送邮件

```java
/**
 * 批量发送合作伙伴用户审批邮件
 */
public void sendBatchPartnerUserApprovalEmails(List<RegisterPartnerUserVo> registerResults, Long agentId, String agentName) {
    if (GeneralTool.isEmpty(registerResults)) {
        return;
    }
    
    try {
        List<EmailSendContext> emailContexts = new ArrayList<>();
        
        for (RegisterPartnerUserVo registerResult : registerResults) {
            // 构建邮件参数
            Map<String, String> emailParams = new HashMap<>();
            emailParams.put("personalName", registerResult.getName());
            emailParams.put("name", agentName);
            emailParams.put("account", registerResult.getAccount());
            emailParams.put("password", registerResult.getPassword());
            emailParams.put("taskLink", buildTaskLink(agentId));
            emailParams.put("versionValue", "zh");
            
            // 构建邮件上下文
            EmailSendContext context = EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableId(agentId)
                .tableName(TableEnum.SALE_AGENT)
                .recipient(registerResult.getAccount())
                .title("代理申请审核通过通知")
                .emailTemplate(EmailTemplateEnum.AGENCY_APPLY_APPROVED)
                .parameters(emailParams)
                .build();
            
            emailContexts.add(context);
        }
        
        // 批量发送邮件
        emailSenderUtils.sendBatchEmails(emailContexts, agentId);
        
    } catch (Exception e) {
        log.error("批量发送合作伙伴用户审批邮件失败，代理ID: {}", agentId, e);
    }
}
```

## 常见问题和解决方案

### 1. 邮件模板参数缺失
**问题**：邮件内容中出现 `${参数名}` 未被替换
**解决**：检查参数Map中是否包含所有模板需要的参数

### 2. 邮件发送失败
**问题**：邮件任务状态为 `-1`
**解决**：
- 检查 `m_email_sender_queue` 表中的 `error_message` 字段
- 查看 `ais-rocketmq-center` 服务日志
- 确认SMTP服务器配置正确

### 3. 邮件队列积压
**问题**：邮件任务状态长时间保持为 `0`
**解决**：
- 检查 `sendEmailScheduleTask` 定时任务是否正常运行
- 检查RocketMQ NameServer连接状态
- 确认 `ais-rocketmq-center` 消费者服务正常

### 4. 参数包含特殊字符
**问题**：邮件内容显示异常
**解决**：在构建参数时进行HTML转义处理

## 注意事项

1. **参数完整性**：确保所有模板参数都有对应的值
2. **邮件地址验证**：发送前验证收件人邮箱格式
3. **异常处理**：邮件发送失败不应影响主业务流程
4. **日志记录**：记录详细的邮件发送日志便于问题排查
5. **模板版本**：支持中英文模板，根据 `versionValue` 参数选择
6. **执行时间**：可以设置延迟发送，通过 `operationTime` 参数控制

## 总结

通过以上流程，可以实现：
- 统一的邮件发送接口
- 灵活的参数配置
- 完善的错误处理
- 详细的日志记录
- 异步的邮件处理

这套邮件发送机制提供了完整的从参数配置到最终邮件发送的解决方案，具有良好的可扩展性和维护性。