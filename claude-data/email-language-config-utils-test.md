# EmailLanguageConfigUtils 测试验证

## 功能测试验证

### 1. 基本功能测试

#### 1.1 语言代码确定测试
```java
// 测试用例1：通过versionValue确定语言
Map<String, String> params1 = new HashMap<>();
params1.put("versionValue", "en");
String result1 = EmailLanguageConfigUtils.determineLanguageCode(params1, permissionCenterClient);
// 期望结果：en

// 测试用例2：通过staffId确定语言
Map<String, String> params2 = new HashMap<>(); 
params2.put("staffId", "12345");
String result2 = EmailLanguageConfigUtils.determineLanguageCode(params2, permissionCenterClient);
// 期望结果：根据员工所在公司配置确定

// 测试用例3：默认语言
Map<String, String> params3 = new HashMap<>();
String result3 = EmailLanguageConfigUtils.determineLanguageCode(params3, permissionCenterClient);
// 期望结果：zh
```

#### 1.2 模板选择测试
```java
// 测试用例1：英文模板选择
String result1 = EmailLanguageConfigUtils.selectEmailTemplate(
    "en", 
    "中文模板内容", 
    "English template content"
);
// 期望结果：English template content

// 测试用例2：中文模板选择
String result2 = EmailLanguageConfigUtils.selectEmailTemplate(
    "zh", 
    "中文模板内容", 
    "English template content"
);
// 期望结果：中文模板内容
```

#### 1.3 公司配置获取测试
```java
// 测试用例1：通过公司ID获取语言配置
String result1 = EmailLanguageConfigUtils.getLanguageCodeByCompanyId(1L, permissionCenterClient);
// 期望结果：根据公司配置返回相应语言代码

// 测试用例2：通过员工ID获取语言配置
String result2 = EmailLanguageConfigUtils.getLanguageCodeByStaffId(12345L, permissionCenterClient);
// 期望结果：根据员工所在公司配置返回相应语言代码
```

### 2. 边界情况测试

#### 2.1 空值处理
```java
// 测试空参数
String result1 = EmailLanguageConfigUtils.determineLanguageCode(null, permissionCenterClient);
// 期望结果：zh

// 测试空员工ID
String result2 = EmailLanguageConfigUtils.getLanguageCodeByStaffId(null, permissionCenterClient);
// 期望结果：zh

// 测试空公司ID
String result3 = EmailLanguageConfigUtils.getLanguageCodeByCompanyId(null, permissionCenterClient);
// 期望结果：zh
```

#### 2.2 异常情况
```java
// 测试格式错误的staffId
Map<String, String> params = new HashMap<>();
params.put("staffId", "abc");
String result = EmailLanguageConfigUtils.determineLanguageCode(params, permissionCenterClient);
// 期望结果：zh (默认值)
```

### 3. 集成测试

#### 3.1 PartnerUserEmailHelper 集成测试
```java
// 构建测试邮件队列
EmailSenderQueue testQueue = new EmailSenderQueue();
testQueue.setEmailParameter("{\"staffId\":\"12345\",\"personalName\":\"测试用户\"}");
testQueue.setEmailTitle("测试邮件标题");
testQueue.setFkEmailTypeKey("AGENCY_APPLY_APPROVED");

// 执行邮件数据组装
PartnerUserEmailDto result = partnerUserEmailHelper.assembleEmailData(testQueue);

// 验证结果
assert result.getLanguageCode() != null;
assert result.getPersonalName().equals("测试用户");
```

### 4. 性能测试

#### 4.1 工具类方法性能
```java
// 测试语言配置获取性能
long startTime = System.currentTimeMillis();
for (int i = 0; i < 1000; i++) {
    EmailLanguageConfigUtils.getLanguageCodeByStaffId(12345L, permissionCenterClient);
}
long endTime = System.currentTimeMillis();
System.out.println("1000次调用耗时: " + (endTime - startTime) + "ms");
```

### 5. 日志验证

#### 5.1 关键日志输出
- 员工信息获取失败时的警告日志
- 公司配置获取失败时的警告日志
- staffId格式错误时的警告日志
- 异常情况的错误日志

#### 5.2 日志级别验证
- WARN级别：参数异常、数据不存在
- ERROR级别：网络异常、系统异常

### 6. 兼容性测试

#### 6.1 向后兼容性
```java
// 测试原有versionValue参数仍然有效
Map<String, String> params = new HashMap<>();
params.put("versionValue", "en");
String result = EmailLanguageConfigUtils.determineLanguageCode(params, permissionCenterClient);
// 期望结果：en
```

#### 6.2 优先级测试
```java
// 测试versionValue优先级高于staffId
Map<String, String> params = new HashMap<>();
params.put("versionValue", "en");
params.put("staffId", "12345"); // 假设该员工公司配置为中文
String result = EmailLanguageConfigUtils.determineLanguageCode(params, permissionCenterClient);
// 期望结果：en (versionValue优先)
```

### 7. 验证重构效果

#### 7.1 代码简化验证
- PartnerUserEmailHelper 中的重复代码已移除
- 语言相关逻辑集中在工具类中
- 方法调用更加简洁

#### 7.2 功能一致性验证
- 重构前后的邮件语言选择逻辑保持一致
- 模板选择结果与原有逻辑相同
- 异常处理行为保持不变

### 8. 实际使用场景测试

#### 8.1 代理申请审批邮件
```java
// 模拟代理申请审批场景
Map<String, String> params = new HashMap<>();
params.put("personalName", "张三");
params.put("name", "测试代理");
params.put("staffId", "12345");

String languageCode = EmailLanguageConfigUtils.determineLanguageCode(params, permissionCenterClient);
// 根据员工公司配置确定语言
```

#### 8.2 多语言模板选择
```java
// 模拟模板选择场景
String chineseTemplate = "尊敬的${personalName}，您的代理申请已通过审核。";
String englishTemplate = "Dear ${personalName}, your agent application has been approved.";

String selectedTemplate = EmailLanguageConfigUtils.selectEmailTemplate(
    languageCode, 
    chineseTemplate, 
    englishTemplate
);
```

## 测试结果预期

### 成功标准
1. 所有基本功能测试通过
2. 边界情况正确处理
3. 异常情况有适当的默认值
4. 日志输出正确
5. 性能满足要求
6. 向后兼容性良好

### 风险控制
1. 网络异常情况下的降级策略
2. 配置缺失时的默认行为
3. 参数格式错误的容错处理
4. 内存泄漏风险评估

通过以上测试验证，确保 EmailLanguageConfigUtils 工具类功能正确、稳定、高效。