# Claude Data 目录

这个目录专门用于存放与Claude Code交互时需要保存的数据。

## 用途

- 存放代码片段、设计文档、测试数据等
- 保存临时配置文件和实验性代码
- 存储需要在会话间保持的数据
- 本地开发和调试相关的文件

## 特性

- **本地专用**: 此目录已添加到.gitignore，不会被提交到git仓库
- **私有存储**: 仅在本地环境中可用，不会影响代码库
- **灵活使用**: 可以自由创建子目录和文件

## 目录结构建议

```
claude-data/
├── snippets/          # 代码片段
├── docs/              # 文档和设计
├── configs/           # 配置文件
├── temp/              # 临时文件
└── experiments/       # 实验性代码
```

## 注意事项

- 请勿在此目录存放敏感信息（如密码、API密钥等）
- 定期清理不需要的临时文件
- 子目录结构可根据实际需要调整