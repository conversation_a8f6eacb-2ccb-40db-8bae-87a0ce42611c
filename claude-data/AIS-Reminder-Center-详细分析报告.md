# AIS Reminder Center 详细分析报告

## 📋 概述

**AIS Reminder Center** 是 HTI Java AIS v1 系统中的邮件通知中心微服务，负责整个系统的统一邮件发送、提醒通知和消息处理功能。该模块采用现代化的微服务架构，提供高可靠性、高性能的邮件通知服务。

## 🏗️ 架构设计

### 模块结构

```
ais-reminder-center/                    # 主服务模块
├── src/main/java/com/get/remindercenter/
│   ├── ReminderCenterApplication.java  # 启动类
│   ├── component/                      # 邮件处理组件
│   │   ├── EmailAbstractHelper.java    # 邮件处理抽象基类
│   │   ├── AcceptOfferDeadlineReminderEmailHelper.java
│   │   ├── StudentOfferItemCommissionNoticeEmailHelper.java
│   │   ├── WorkLeaveReminderEmailHelper.java
│   │   ├── EventFeeCollectionChangeReminderEmailHelper.java
│   │   ├── RewardPromotionActivityReminderEmailHelper.java
│   │   ├── MultiUserTaskReminderEmailHelper.java
│   │   ├── SummitRegistrationReminderEmailHelper.java
│   │   ├── ProviderContractExpiryEmailHelper.java
│   │   └── [其他11种邮件处理组件]
│   ├── config/                         # 配置类
│   │   ├── DataSourceConfig.java       # 多数据源配置
│   │   ├── EmailConfig.java            # 邮件配置
│   │   └── RocketMQConfig.java         # 消息队列配置
│   ├── controller/                     # 控制器层
│   │   ├── ReminderController.java     # 提醒控制器
│   │   └── EmailController.java        # 邮件控制器
│   ├── dao/                           # 数据访问层
│   │   ├── ReminderTaskDao.java
│   │   ├── EmailSenderQueueDao.java
│   │   └── EmailTemplateDao.java
│   ├── feign/                         # 内部服务调用
│   │   ├── SaleCenterClient.java
│   │   ├── WorkflowCenterClient.java
│   │   └── OfficeCenterClient.java
│   ├── service/                       # 业务服务层
│   │   ├── ReminderService.java
│   │   ├── EmailService.java
│   │   └── impl/
│   └── utils/                         # 工具类
│       ├── EmailUtils.java
│       └── DateUtils.java

ais-reminder-center-ap/                # API 定义模块
├── src/main/java/com/get/remindercenter/
│   ├── dto/                           # 数据传输对象
│   │   ├── ReminderTaskDto.java
│   │   ├── EmailSenderDto.java
│   │   └── EmailTemplateDto.java
│   ├── entity/                        # 实体类
│   │   ├── ReminderTask.java
│   │   ├── EmailSenderQueue.java
│   │   ├── EmailTemplate.java
│   │   └── SystemEmailAccount.java
│   ├── enums/                         # 枚举类
│   │   ├── EmailTemplateEnum.java     # 邮件模板枚举
│   │   ├── ReminderTypeEnum.java      # 提醒类型枚举
│   │   └── EmailStatusEnum.java       # 邮件状态枚举
│   ├── feign/                         # Feign 客户端接口
│   │   ├── IReminderCenterClient.java # 主要服务接口
│   │   └── IReminderCenterClientFallBack.java
│   └── vo/                           # 视图对象
│       ├── ReminderTaskVo.java
│       └── EmailSenderVo.java
```

## 🛠️ 技术栈

### 核心技术栈
- **Spring Boot 2.3.12.RELEASE**: 基础框架
- **Spring Cloud Hoxton.SR11**: 微服务架构
- **MyBatis Plus**: 数据持久化框架
- **Jakarta Mail**: 邮件发送核心组件
- **阿里云邮件推送**: 云端邮件服务
- **RocketMQ**: 异步消息队列
- **Dynamic DataSource**: 多数据源支持

### 关键依赖
```xml
<dependencies>
    <!-- 邮件服务 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
    </dependency>
    
    <!-- 阿里云邮件推送 -->
    <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>aliyun-java-sdk-dm</artifactId>
    </dependency>
    
    <!-- RocketMQ -->
    <dependency>
        <groupId>com.get</groupId>
        <artifactId>ais-rocketmq-center-ap</artifactId>
    </dependency>
    
    <!-- 多数据源 -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
    </dependency>
</dependencies>
```

## 📧 邮件模板系统

### 17种邮件模板类型

根据 `EmailTemplateEnum` 定义，系统支持以下邮件模板：

#### 学生服务类提醒
1. **STUDENT_OFFER_ITEM_COMMISSION_NOTICE**: 学习计划佣金结算通知
2. **OFFER_ACCEPT_DUE_REMIND**: 接受Offer截止提醒
3. **PAY_DEPOSIT_DUE_REMIND**: 支付押金截止提醒
4. **COURSE_OPENING_REMINDER**: 课程预计开课提醒

#### 代理管理类提醒
5. **AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE**: 代理学生接受Offer截止提醒
6. **AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE**: 代理学生支付押金截止提醒

#### 工作流程类提醒
7. **WORK_LEAVE_WORKFLOW_REMINDER**: 工休单工作流提醒
8. **NON_WORK_LEAVE_WORKFLOW_REMINDER**: 非工休单工作流提醒
9. **APPLN_TERM_INVAL_WORKFLOW_REMINDER**: 申请方案终止作废工作流提醒

#### 业务管理类提醒
10. **EVENT_FEE_PLAN_CHANGE_REMINDER**: 活动费用汇总收款计划变更提醒
11. **REWARD_PROMOTION_ACTIVITY_REMINDER**: 奖励活动推广活动提醒
12. **MULTI_USER_TASK_REMINDER**: 多人任务提醒
13. **SUMMIT_REGISTRATION_REMINDER**: 峰会提交报名册提醒

#### 佣金结算类通知
14. **INSURANCE_COMMISSION_NOTICE**: 留学保险佣金结算通知
15. **ACCOMMODATION_COMMISSION_NOTICE**: 留学住宿佣金结算通知
16. **SERVICE_FEE_COMMISSION_NOTICE**: 服务费佣金结算通知
17. **PROVIDER_CONTRACT_EXPIRY_NOTICE**: 供应商合同到期通知

### 邮件模板特性

#### 多语言支持
- **中文模板**: 默认语言，支持简体中文
- **英文模板**: 支持英语国际化
- **HTML富文本**: 支持复杂的HTML邮件格式
- **动态参数**: 支持模板变量替换

#### 模板管理
- **版本控制**: 模板版本管理和历史记录
- **A/B测试**: 支持多版本模板测试
- **实时预览**: 模板编辑实时预览功能
- **参数验证**: 模板参数有效性验证

## 🏛️ 策略模式邮件处理架构

### 设计模式
系统采用**策略模式**（Strategy Pattern）设计，每种邮件类型对应一个专门的处理组件，实现了高度的可扩展性和可维护性。

### 抽象基类
```java
/**
 * 邮件处理抽象基类
 */
public abstract class EmailAbstractHelper {
    
    /**
     * 获取邮件模板类型
     */
    public abstract EmailTemplateEnum getEmailTemplateType();
    
    /**
     * 处理邮件发送
     */
    public abstract void processEmail(EmailSenderDto emailSenderDto);
    
    /**
     * 验证邮件参数
     */
    public abstract boolean validateEmailParams(Map<String, Object> params);
    
    /**
     * 构建邮件内容
     */
    public abstract String buildEmailContent(Map<String, Object> params);
}
```

### 具体实现组件
```java
/**
 * Offer截止提醒邮件处理器
 */
@Component
public class AcceptOfferDeadlineReminderEmailHelper extends EmailAbstractHelper {
    
    @Override
    public EmailTemplateEnum getEmailTemplateType() {
        return EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND;
    }
    
    @Override
    public void processEmail(EmailSenderDto emailSenderDto) {
        // 具体的邮件处理逻辑
        // 1. 获取学生信息
        // 2. 构建邮件内容
        // 3. 发送邮件
        // 4. 记录发送日志
    }
    
    @Override
    public boolean validateEmailParams(Map<String, Object> params) {
        // 参数验证逻辑
        return params.containsKey("studentId") && 
               params.containsKey("offerDeadline");
    }
    
    @Override
    public String buildEmailContent(Map<String, Object> params) {
        // 构建邮件内容
        return freeMarkerService.processTemplate(
            "offer_deadline_reminder.ftl", params);
    }
}
```

## 📊 数据库设计

### 主要数据表

#### 核心业务表
1. **m_email_sender_queue**: 邮件发送队列
   - `id`: 主键
   - `email_template_type`: 邮件模板类型
   - `to_email`: 收件人邮箱
   - `subject`: 邮件主题
   - `content`: 邮件内容
   - `status`: 发送状态
   - `retry_count`: 重试次数
   - `send_time`: 发送时间
   - `create_time`: 创建时间

2. **m_email_template**: 邮件模板配置
   - `id`: 主键
   - `template_key`: 模板唯一标识
   - `template_name`: 模板名称
   - `template_content`: 模板内容
   - `template_type`: 模板类型
   - `language`: 语言类型
   - `version`: 版本号
   - `status`: 状态

3. **m_remind_task**: 提醒任务管理
   - `id`: 主键
   - `task_type`: 任务类型
   - `task_name`: 任务名称
   - `cron_expression`: 定时表达式
   - `status`: 任务状态
   - `next_fire_time`: 下次执行时间

4. **m_remind_task_queue**: 提醒任务队列
   - `id`: 主键
   - `task_id`: 任务ID
   - `business_id`: 业务ID
   - `remind_time`: 提醒时间
   - `status`: 状态
   - `retry_count`: 重试次数

#### 支持功能表
5. **m_system_email_account**: 系统邮箱账户
   - `id`: 主键
   - `email_address`: 邮箱地址
   - `smtp_host`: SMTP主机
   - `smtp_port`: SMTP端口
   - `username`: 用户名
   - `password`: 密码
   - `ssl_enable`: SSL启用状态

6. **m_batch_email_promotion_queue**: 批量推广邮件队列
   - `id`: 主键
   - `campaign_id`: 活动ID
   - `email_list`: 邮件列表
   - `template_id`: 模板ID
   - `send_time`: 发送时间
   - `status`: 状态

7. **m_unsubscribe_email**: 邮件退订管理
   - `id`: 主键
   - `email_address`: 邮箱地址
   - `unsubscribe_type`: 退订类型
   - `unsubscribe_time`: 退订时间
   - `reason`: 退订原因

8. **m_invalid_email**: 无效邮箱记录
   - `id`: 主键
   - `email_address`: 邮箱地址
   - `invalid_reason`: 无效原因
   - `invalid_time`: 无效时间

9. **m_log_send_email**: 邮件发送日志
   - `id`: 主键
   - `email_id`: 邮件ID
   - `send_status`: 发送状态
   - `error_message`: 错误信息
   - `send_time`: 发送时间
   - `response_info`: 响应信息

### 数据库配置
```yaml
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: jdbc:mysql://************:3316/ais_reminder_center
          username: ${database.username}
          password: ${database.password}
          driver-class-name: com.mysql.cj.jdbc.Driver
        doris:
          url: **************************************************
          username: ${doris.username}
          password: ${doris.password}
          driver-class-name: com.mysql.cj.jdbc.Driver
```

## 🔄 消息队列集成

### RocketMQ集成架构
系统与RocketMQ深度集成，实现异步邮件处理：

#### 消息队列配置
```yaml
rocketmq:
  name-server: ************:9876
  producer:
    group: reminder-center-producer
    send-message-timeout: 10000
    retry-times-when-send-failed: 3
  consumer:
    group: reminder-center-consumer
    consume-message-batch-max-size: 1
    consume-timeout: 15
```

#### 主要Topic和Queue
1. **mail_system_queue_topic**: 系统邮件队列
   - 处理系统自动触发的邮件
   - 支持延时发送
   - 失败重试机制

2. **mail_custom_queue_topic**: 自定义邮件队列
   - 处理用户自定义的邮件
   - 支持批量发送
   - 优先级处理

3. **mail_task_queue_topic**: 任务邮件队列
   - 处理定时任务触发的邮件
   - 支持定时发送
   - 任务状态跟踪

#### 消息处理流程
```java
/**
 * 邮件消息生产者
 */
@Component
public class EmailMessageProducer {
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    /**
     * 发送系统邮件消息
     */
    public void sendSystemEmail(EmailSenderDto emailDto) {
        Message<EmailSenderDto> message = MessageBuilder
            .withPayload(emailDto)
            .setHeader("messageType", "SYSTEM_EMAIL")
            .build();
            
        rocketMQTemplate.send("mail_system_queue_topic", message);
    }
    
    /**
     * 发送自定义邮件消息
     */
    public void sendCustomEmail(EmailSenderDto emailDto) {
        rocketMQTemplate.send("mail_custom_queue_topic", emailDto);
    }
    
    /**
     * 发送延时邮件消息
     */
    public void sendDelayEmail(EmailSenderDto emailDto, int delayLevel) {
        rocketMQTemplate.syncSend("mail_task_queue_topic", emailDto, 
            3000, delayLevel);
    }
}
```

```java
/**
 * 邮件消息消费者
 */
@Component
@RocketMQMessageListener(
    topic = "mail_system_queue_topic",
    consumerGroup = "reminder-center-consumer"
)
public class EmailMessageConsumer implements RocketMQListener<EmailSenderDto> {
    
    @Autowired
    private EmailService emailService;
    
    @Override
    public void onMessage(EmailSenderDto emailDto) {
        try {
            // 处理邮件发送
            emailService.processEmail(emailDto);
            
            // 记录发送日志
            emailService.logEmailSending(emailDto);
            
        } catch (Exception e) {
            log.error("邮件发送失败", e);
            
            // 失败重试逻辑
            emailService.retryEmailSending(emailDto);
        }
    }
}
```

## 🌐 多渠道邮件发送

### 发送渠道
1. **SMTP直接发送**: 腾讯企业邮箱
2. **阿里云邮件推送**: 大批量邮件发送
3. **MQ异步发送**: 通过消息队列异步处理
4. **批量邮件处理**: 支持大量邮件的批量发送

### 邮件发送策略
```java
/**
 * 邮件发送策略
 */
@Component
public class EmailSendingStrategy {
    
    /**
     * 选择发送渠道
     */
    public EmailChannelEnum selectChannel(EmailSenderDto emailDto) {
        // 根据邮件类型和数量选择合适的发送渠道
        if (emailDto.getEmailType().equals("PROMOTION")) {
            return EmailChannelEnum.ALIYUN_PUSH;
        } else if (emailDto.isBatchSend()) {
            return EmailChannelEnum.BATCH_SEND;
        } else {
            return EmailChannelEnum.SMTP_DIRECT;
        }
    }
    
    /**
     * 执行邮件发送
     */
    public void sendEmail(EmailSenderDto emailDto) {
        EmailChannelEnum channel = selectChannel(emailDto);
        
        switch (channel) {
            case SMTP_DIRECT:
                smtpEmailService.sendEmail(emailDto);
                break;
            case ALIYUN_PUSH:
                aliyunEmailService.sendEmail(emailDto);
                break;
            case BATCH_SEND:
                batchEmailService.sendEmail(emailDto);
                break;
            case MQ_ASYNC:
                mqEmailService.sendEmail(emailDto);
                break;
        }
    }
}
```

### 失败重试机制
```java
/**
 * 邮件发送重试机制
 */
@Component
public class EmailRetryService {
    
    private static final int MAX_RETRY_COUNT = 3;
    private static final long RETRY_DELAY = 5000; // 5秒
    
    /**
     * 重试邮件发送
     */
    public void retryEmailSending(EmailSenderDto emailDto) {
        if (emailDto.getRetryCount() >= MAX_RETRY_COUNT) {
            // 标记为失败
            emailDto.setStatus(EmailStatusEnum.FAILED);
            emailService.updateEmailStatus(emailDto);
            return;
        }
        
        // 增加重试次数
        emailDto.setRetryCount(emailDto.getRetryCount() + 1);
        
        // 延时重试
        try {
            Thread.sleep(RETRY_DELAY * emailDto.getRetryCount());
            emailService.sendEmail(emailDto);
        } catch (Exception e) {
            log.error("邮件重试发送失败", e);
            retryEmailSending(emailDto);
        }
    }
}
```

## 🔗 API接口设计

### Feign客户端接口
```java
/**
 * 提醒中心服务接口
 */
@FeignClient(name = "ais-reminder-center", 
             fallback = IReminderCenterClientFallBack.class)
public interface IReminderCenterClient {
    
    // ========== 任务管理接口 ==========
    
    /**
     * 批量新增任务
     */
    @PostMapping("/reminder/task/batchAdd")
    R<Boolean> batchAdd(@RequestBody List<ReminderTaskDto> tasks);
    
    /**
     * 批量修改任务
     */
    @PostMapping("/reminder/task/batchUpdate")
    R<Boolean> batchUpdate(@RequestBody List<ReminderTaskDto> tasks);
    
    /**
     * 批量删除任务
     */
    @PostMapping("/reminder/task/batchDelete")
    R<Boolean> batchDelete(@RequestBody List<Long> taskIds);
    
    /**
     * 执行提醒任务
     */
    @PostMapping("/reminder/task/performTasks")
    R<Boolean> performTasks(@RequestBody List<Long> taskIds);
    
    // ========== 邮件发送接口 ==========
    
    /**
     * 基础邮件发送
     */
    @PostMapping("/email/sendMail")
    R<Boolean> sendMail(@RequestBody EmailSenderDto emailDto);
    
    /**
     * 批量邮件发送
     */
    @PostMapping("/email/batchSendEmail")
    R<Boolean> batchSendEmail(@RequestBody List<EmailSenderDto> emailList);
    
    /**
     * 自定义邮件发送
     */
    @PostMapping("/email/customSendMail")
    R<Boolean> customSendMail(@RequestBody EmailSenderDto emailDto);
    
    /**
     * 系统邮件发送（通过MQ）
     */
    @PostMapping("/email/sendSystemMail")
    R<Boolean> sendSystemMail(@RequestBody EmailSenderDto emailDto);
    
    /**
     * 自定义邮件发送（通过MQ）
     */
    @PostMapping("/email/sendCustomMail")
    R<Boolean> sendCustomMail(@RequestBody EmailSenderDto emailDto);
    
    // ========== 模板管理接口 ==========
    
    /**
     * 获取提醒模板
     */
    @GetMapping("/template/getRemindTemplateByTypeKey")
    R<EmailTemplateVo> getRemindTemplateByTypeKey(@RequestParam String typeKey);
    
    /**
     * 获取邮件模板
     */
    @GetMapping("/template/getEmailTemplateByTypeKey")
    R<EmailTemplateVo> getEmailTemplateByTypeKey(@RequestParam String typeKey);
    
    // ========== 阿里云邮件服务 ==========
    
    /**
     * 阿里云邮件推送
     */
    @PostMapping("/aliyun/sendMailNew")
    R<Boolean> aliyunSendMailNew(@RequestBody AliyunEmailDto aliyunEmailDto);
    
    /**
     * 阿里云邮件标签管理
     */
    @PostMapping("/aliyun/addTag")
    R<Boolean> aliyunAddTag(@RequestBody AliyunTagDto aliyunTagDto);
    
    // ========== 邮件退订管理 ==========
    
    /**
     * 邮件退订
     */
    @PostMapping("/unsubscribe/email")
    R<Boolean> unsubscribeEmail(@RequestParam String email, 
                               @RequestParam String type);
    
    /**
     * 检查邮箱是否已退订
     */
    @GetMapping("/unsubscribe/check")
    R<Boolean> checkUnsubscribe(@RequestParam String email);
    
    // ========== 统计查询接口 ==========
    
    /**
     * 获取邮件发送统计
     */
    @GetMapping("/statistics/emailSending")
    R<EmailStatisticsVo> getEmailSendingStatistics(@RequestParam String dateRange);
    
    /**
     * 获取任务执行统计
     */
    @GetMapping("/statistics/taskExecution")
    R<TaskStatisticsVo> getTaskExecutionStatistics(@RequestParam String dateRange);
}
```

### 数据传输对象（DTO）
```java
/**
 * 邮件发送DTO
 */
@Data
public class EmailSenderDto {
    
    /**
     * 邮件模板类型
     */
    private EmailTemplateEnum templateType;
    
    /**
     * 收件人邮箱
     */
    private String toEmail;
    
    /**
     * 收件人姓名
     */
    private String toName;
    
    /**
     * 邮件主题
     */
    private String subject;
    
    /**
     * 邮件内容
     */
    private String content;
    
    /**
     * 邮件参数
     */
    private Map<String, Object> params;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 0;
    
    /**
     * 是否批量发送
     */
    private Boolean batchSend = false;
    
    /**
     * 发送状态
     */
    private EmailStatusEnum status;
}
```

## 🔧 配置管理

### 多环境配置
系统支持7个环境的独立配置：

#### 本地开发环境（local）
```yaml
# application-local.yml
server:
  port: 8093

spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***********************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver

  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: ${email.username}
    password: ${email.password}
    properties:
      mail:
        smtp:
          ssl:
            enable: true

rocketmq:
  name-server: ************:9876
  producer:
    group: reminder-center-producer-local
```

#### 开发环境（dev）
```yaml
# application-dev.yml
server:
  port: 8093

spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: jdbc:mysql://************:3316/ais_reminder_center
          username: ${database.username}
          password: ${database.password}
        doris:
          url: **************************************************
          username: ${doris.username}
          password: ${doris.password}

aliyun:
  email:
    access-key-id: ${aliyun.access.key.id}
    access-key-secret: ${aliyun.access.key.secret}
    region: cn-hangzhou
    account-name: ${aliyun.email.account.name}
```

#### 生产环境（prod）
```yaml
# application-prod.yml
server:
  port: 8093

spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *********************************************
          username: ${database.username}
          password: ${database.password}
          
logging:
  level:
    com.get.remindercenter: INFO
    org.springframework.mail: WARN
```

### 邮件配置
```yaml
# 邮件服务配置
email:
  config:
    # SMTP配置
    smtp:
      host: smtp.exmail.qq.com
      port: 465
      username: ${email.username}
      password: ${email.password}
      ssl-enable: true
    
    # 阿里云邮件推送配置
    aliyun:
      region: cn-hangzhou
      access-key-id: ${aliyun.access.key.id}
      access-key-secret: ${aliyun.access.key.secret}
      account-name: ${aliyun.email.account.name}
      
    # 发送限制
    limits:
      max-recipients: 100
      max-daily-send: 10000
      retry-count: 3
      retry-delay: 5000
```

## 🚀 部署和运维

### Docker部署
```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine

VOLUME /tmp

COPY target/ais-reminder-center-1.0.RELEASE.jar app.jar

EXPOSE 8093

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app.jar"]
```

### 构建命令
```bash
# 清理构建
mvn clean -U

# 构建服务
mvn -T 4 clean package -pl biz-service/ais-reminder-center -am -e -U -Dmaven.test.skip=true

# Docker构建
docker build -f src/main/docker/Dockerfile . -t hti-ais-reminder-center:1.0

# 运行容器
docker run -d -p 8093:8093 --name ais-reminder-center hti-ais-reminder-center:1.0
```

### 健康检查
```yaml
# 健康检查配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  health:
    mail:
      enabled: true
    db:
      enabled: true
```

### 监控配置
```yaml
# 监控配置
logging:
  level:
    com.get.remindercenter: INFO
    org.springframework.mail: DEBUG
    org.apache.rocketmq: WARN
    
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    
  file:
    name: logs/reminder-center.log
    max-size: 100MB
    max-history: 30
```

## 🔄 系统集成

### 与其他微服务的集成关系

#### 1. 销售中心（ais-sale-center）
- **学生申请相关提醒**: Offer截止、押金支付等
- **代理管理提醒**: 代理学生状态变更通知
- **合同提醒**: 合同签署、到期等提醒

#### 2. 财务中心（ais-finance-center）
- **佣金结算通知**: 各类佣金结算完成通知
- **付款提醒**: 付款计划、到期提醒
- **发票提醒**: 发票开具、收取提醒

#### 3. 机构中心（ais-institution-center）
- **合同到期提醒**: 供应商合同到期通知
- **课程提醒**: 课程开课、变更通知
- **机构信息更新**: 机构信息变更通知

#### 4. 办公中心（ais-office-center）
- **工作流任务提醒**: 审批待办、超时提醒
- **会议提醒**: 会议通知、变更提醒
- **任务提醒**: 多人任务分配、完成提醒

#### 5. 工作流中心（workflow-center）
- **审批流程通知**: 审批节点、结果通知
- **流程超时提醒**: 审批超时预警
- **流程状态变更**: 流程状态同步通知

#### 6. 定时任务中心（xxljob）
- **定时任务触发**: 定时任务执行邮件通知
- **任务执行结果**: 任务成功/失败通知
- **任务监控告警**: 任务异常告警

### 数据流示例
```
销售中心 → 学生Offer状态变更 → 提醒中心 → 邮件队列 → 邮件发送
工作流中心 → 审批待办 → 提醒中心 → 邮件模板 → 邮件发送
财务中心 → 佣金结算完成 → 提醒中心 → 批量邮件 → 邮件发送
定时任务 → 定时触发 → 提醒中心 → 延时邮件 → 邮件发送
```

## 🎯 业务特色功能

### 1. 智能邮件模板系统
- **动态参数替换**: 支持复杂的参数替换和条件判断
- **多语言支持**: 中英文邮件模板自动切换
- **模板版本管理**: 支持模板版本控制和回滚
- **实时预览**: 邮件模板实时预览和编辑

### 2. 高可靠性邮件队列
- **失败重试机制**: 最多3次重试，指数退避策略
- **死信队列**: 失败邮件进入死信队列，人工干预
- **状态跟踪**: 完整的邮件生命周期状态跟踪
- **监控告警**: 邮件发送异常实时告警

### 3. 邮件退订管理
- **一键退订**: 推广邮件一键退订功能
- **退订分类**: 按邮件类型分类管理退订
- **加密处理**: 邮箱地址AES加密存储
- **退订统计**: 退订率统计和分析

### 4. 多渠道发送策略
- **智能路由**: 根据邮件类型和数量智能选择发送渠道
- **负载均衡**: 多个SMTP服务器负载均衡
- **故障转移**: 主渠道故障时自动切换备用渠道
- **发送限流**: 避免触发邮件服务商发送限制

### 5. 邮件统计分析
- **发送统计**: 发送成功率、失败率统计
- **用户行为**: 邮件打开率、点击率分析
- **模板效果**: 不同模板的转化率对比
- **时间分析**: 最佳发送时间分析

## 📊 性能优化

### 1. 数据库优化
- **索引优化**: 关键字段创建合适索引
- **分表分库**: 大数据量表进行分区
- **连接池**: 数据库连接池优化配置
- **读写分离**: 读写分离提高性能

### 2. 缓存策略
- **模板缓存**: 邮件模板Redis缓存
- **用户信息缓存**: 用户基本信息缓存
- **配置缓存**: 系统配置信息缓存
- **统计缓存**: 统计数据定时缓存

### 3. 异步处理
- **消息队列**: RocketMQ异步处理邮件发送
- **线程池**: 自定义线程池处理并发任务
- **批量处理**: 批量发送邮件优化
- **延时发送**: 延时邮件队列处理

### 4. 监控告警
- **业务监控**: 邮件发送量、成功率监控
- **技术监控**: 系统资源、接口响应时间监控
- **异常告警**: 异常情况实时告警
- **性能分析**: 性能瓶颈分析和优化

## 🔐 安全设计

### 1. 数据安全
- **敏感信息加密**: 邮箱地址、密码等敏感信息加密存储
- **访问控制**: 基于角色的访问控制
- **数据脱敏**: 日志中敏感信息脱敏
- **传输加密**: HTTPS/TLS加密传输

### 2. 防护机制
- **防垃圾邮件**: 发送频率限制、内容过滤
- **防暴力破解**: 登录失败次数限制
- **IP白名单**: 关键操作IP白名单限制
- **审计日志**: 完整的操作审计日志

### 3. 隐私保护
- **邮件退订**: 尊重用户退订选择
- **数据清理**: 定期清理过期数据
- **隐私政策**: 符合数据保护法规
- **同意机制**: 用户同意机制

## 📝 总结

**AIS Reminder Center** 是一个功能完善、技术先进的企业级邮件通知服务，具有以下核心特点：

### 🌟 核心优势
1. **高可扩展性**: 策略模式设计，新增邮件类型易于扩展
2. **高可靠性**: 完善的重试机制和异常处理
3. **高性能**: 异步队列处理，支持大批量邮件发送
4. **多渠道支持**: 支持SMTP、阿里云等多种发送渠道
5. **智能化**: 智能路由、模板管理、统计分析

### 🎯 业务价值
1. **统一通知中心**: 整个AIS系统的邮件通知统一入口
2. **提升用户体验**: 及时、准确的邮件通知服务
3. **运营支持**: 营销邮件、数据统计分析支持
4. **合规性**: 符合邮件发送规范和隐私保护要求

### 🔧 技术特色
1. **微服务架构**: 独立部署、水平扩展
2. **Spring Cloud生态**: 完整的微服务解决方案
3. **消息队列**: RocketMQ异步处理提升性能
4. **多数据源**: 支持MySQL、Doris等多种数据源

这个模块为HTI Java AIS v1系统提供了稳定、高效、可扩展的邮件通知服务，是整个系统消息通知的核心组件。

---

**文档版本**: v1.0  
**创建时间**: 2025-01-08  
**最后更新**: 2025-01-08  
**作者**: Claude Code Analysis