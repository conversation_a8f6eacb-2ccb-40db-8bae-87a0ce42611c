#spring配置
spring:
  redis:
    ##redis 单机环境配置
    # host: ************
    host: ************
    port: 6379
    password: 
    database: 5
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      validation-query: select 1

#项目模块集中配置
get:
  #分布式锁
  lock:
    enabled: true
    # address: redis://************:6379
    address: redis://************:6379
    port: 6379
    password: 
  #团队协作配置
  ribbon:
    rule:
      enabled: true
      #负载均衡优先调用的ip段
      prior-ip-pattern:
        - ************
  # #开发环境地址
  datasource:
    dev:
      competition:
        url: ******************************************************************************************************************************
        username: root
        password: fzhmysql
      appcompetition:
        url: **********************************************************************************************************************************
        username: root
        password: fzhmysql
      
#峰会报名发送邮件
conference:
  email: <EMAIL>
  emailPassword: lily2023

translation:
  ip: *************
  port: 7860