get:
  datasource:
    test:
      partnerdb:
        url: **************************************************************************************************************************
        username: root
        password: fzhmysql
      saledb:
        url: ***********************************************************************************************************************
        username: root
        password: fzhmysql
      permissiondb:
        url: *****************************************************************************************************************************
        username: root
        password: fzhmysql
      institutiondb:
        url: ******************************************************************************************************************************
        username: root
        password: fzhmysql
      systemdb:
        url: *************************************************************************************************************************
        username: root
        password: fzhmysql
      appfiledb:
        username: root
        password: fzhmysql
        url: ***********************************************************************************************************************************************************************************************************************************************************************************************
      pmp2db:
        username: root
        password: fzhmysql
        url: ***********************************************************************************************************************************************************************************************************************************************************************************************
      platformdb:
        username: root
        password: fzhmysql
        url: ***************************************************************************************************************************************************************************************************************************************************************************************************

encrypt:
  defaultPassword: 123456

file:
  bucketName: s3demo 
  local:
    enable: true
    base-path: /Users/<USER>/Downloads/img
  tencentcloudimage:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-images-dev-1301376564

  tencentcloudfile:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-files-dev-1301376564

rocketmq:
    # 配置 NameServer 地址
    name-server: ************:9876  
    # 生产者分组
    producer:
      group: user_offline_topic
      # 发送超时时间（毫秒） 
      send-message-timeout: 3000
      # 生产者发送失败的最大重试次数
      retry-times-when-send-failed: 3
    consumer:
     # 消费者分组
      group: user_offline_consumer_group
      # 消息最大重试次数（超出后进入死信队列）
      max-reconsume-times: 3
      # 开启消息轨迹    
      enable-msg-trace: true 
