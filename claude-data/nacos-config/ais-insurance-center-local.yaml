spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      primary: insurance  
      datasource:
        insurance:
          username: root
          password: fzhmysql
          url: ****************************************************************************************************************************************************************************************************************************************************************************************************
        institution:
          username: root
          password: fzhmysql
          url: ******************************************************************************************************************************************************************************************************************************************************************************************************
        partner:
          username: root
          password: fzhmysql
          url: **************************************************************************************************************************************************************************************************************************************************************************************************

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

exchangeRate:
  secretId: AKID2xfUvZm4f3pHw0GZxY1iFzsdFix3bCr8clgY
  secretKey: gNf9H71Kb93z7X5Q0G9K2jRusUR93T4O9aPB6WFw

file:
  tencentcloudfile:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-files-dev-1301376564