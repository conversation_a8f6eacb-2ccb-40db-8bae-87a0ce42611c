get:
  datasource:
    test:
      saledb:
        url: ***************************************************************************************************************************************************************************************************************************************************************      
        username: root
        password: fzhmysql
      conventiondb:
        url: ************************************************************************************************************************************
        username: root
        password: fzhmysql
      oldissuedb:
        url: *************************************************************************************************************
        username: root
        password: fzhmysql
      newissuedb:
        url: ****************************************************************************************************************************
        username: root
        password: fzhmysql
      saledb-doris:
        url: *************************************************************************************************************************************************     
        username: root
        password: fzhmysql
      institutiondb-doris:
        # url: jdbc:mysql://************:9030/get_institution_center?useUnicode=true&allowMultiQueries=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=GMT%2b8  
        url: jdbc:mysql://************:9030/ais_institution_center?useUnicode=true&allowMultiQueries=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=GMT%2b8 
        username: root
        password: fzhmysql  
      occdb:
        driver-class-name: oracle.jdbc.driver.OracleDriver
        url: *******************************************
        username: road
        password: GETCPP_ROAD_2F82KF7V
      saledb-doris-export:
        url: *************************************************************************************************************************************************     
        username: root
        password: fzhmysql
      appInsurance:
        url: ********************************************************************************************************************************************************************************************************************************************************************      
        username: root
        password: fzhmysql

# 微信支付配置
  wx:
    pay:
      appId: wx9f92d2ad9aafae4c
      # 微信支付商户号
      mchId: 1650295835
      # path除了classpath也可以用url
      keyPath: classpath:wxPayCertIae/apiclient_cert.p12
      privateKeyPath: classpath:wxPayCertIae/apiclient_key.pem
      privateCertPath: classpath:wxPayCertIae/apiclient_cert.pem
      apiV3Key: 8hsfoi784kos9fu23ksd89u23nr89fkm
      certSerialNo: 79A90D459237AD9098A0B0768E966865B9F970D5
      payNotifyUrl: https://convention.ht-international.net/sale/annualReservationForm/notifyWeiXinPay

# 汇率第三方接口id key
exchangeRate:
  secretId: AKID2xfUvZm4f3pHw0GZxY1iFzsdFix3bCr8clgY
  secretKey: gNf9H71Kb93z7X5Q0G9K2jRusUR93T4O9aPB6WFw

#邮箱匹配接口
emailMate-url: http://************:15016/get_similar_email

analyzeOfferAddress: http://*************:15051/offer_parser/

#解析身份证接口
# idCard-url: http://*************:5024/idcard/?idcard_side=

#顺子提取图片文字内容地址
bl:
  url: http://*************:5024/business_license/
issue:
  url: https://www.issueschool.com
newissue:
  url: https://issue.ht-international.net
gear:
  url: https://issue.ht-international.net
idCard:
  url: http://**********:5024/idcard/?idcard_side=
  

#峰会报名发送邮件
conference:
  email: <EMAIL>
  emailPassword: lily2023
