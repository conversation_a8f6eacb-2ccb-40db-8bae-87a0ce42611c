spring:
  datasource:
    url: *********************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: fzhmysql
  
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: GZEtLhkf6WzpcPd9
    protocol: smtp
    default-encoding: utf-8
    properties:
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.starttls.required: true
      mail.smtp.ssl.enable: true
      mail.smtp.ssl.socketFactory.port: 465
      mail.smtp.ssl.socketFactory.class: javax.net.ssl.SSLSocketFactory
# 配置文件加密根密码
jasypt:
  encryptor:
    password: fzh111
    algorithm: PBEWithMD5AndDES1111
    iv-generator-classname: org.jasypt.iv.NoIvGenerator1111
