#undertow服务器配置
server:
  compression:
    enabled: false
    mime-types: application/json
    min-response-size: 512
  undertow:
    threads:
      # 设置IO线程数,执行非阻塞的任务,默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 类似servlet请求阻塞操作, undertow从这个线程池中取得线程,设置取决于系统的负载
      worker: 400
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: false
bl:
  url: http://192.168.1.96:5024/business_license/
#邮箱开关
emailSwitch: false
#登陆失败次数配置
login:
  fail-count: 100
#spring配置
spring:
  cloud:
    sentinel:
      eager: true
  devtools:
    restart:
      log-condition-evaluation-delta: false
    livereload:
      port: 23333
  autoconfigure:
    exclude:
      - org.activiti.spring.boot.SecurityAutoConfiguration.class
#feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

#hystrix配置：服务熔断和服务降级 熔断机制是应对雪崩效应的一种微服务链路保户机制，当扇出链路的某个微服务不可用或者响应时间太长时，会进行服务的降级，进而熔断该节点微服务的调用，快速返回错误的相应信息。
hystrix:
  threadpool:
    default:
      coreSize: 300
      maxQueueSize: 1000
      queueSizeRejectionThreshold: 800
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000

#ribbon配置：Ribbon是Spring Cloud核心组件之一，它提供的最重要的功能就是负载均衡
ribbon:
  #当前实例重试次数
  MaxAutoRetries: 1
  #切换实例重试次数
  MaxAutoRetriesNextServer: 2
  #请求处理超时时间
  ReadTimeout: 60000
  #请求连接超时时间
  ConnectTimeout: 60000
  #对所有操作请求都进行重试
  OkToRetryOnAllOperations: true

#对外暴露端口
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: get
    password: get
  #增强配置
  setting:
    enableDynamicParameter: true
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://************
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2021 GET

#swagger公共信息
swagger:
  title: GET 接口文档系统
  description: GET 接口文档系统
  version: 1.0.RELEASE
  license: Powered By GET
  license-url: http://localhost
  terms-of-service-url: http://localhost
  contact:
    name: get
    email: <EMAIL>
    url: https://localhost

#get配置
get:
  #token配置
  token:
    #是否有状态
    state: true
  #redis序列化方式
  redis:
    serializer-type: protostuff
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: false
      #使用AesUtil.genAesKey()生成
      aes-key: O2BEeIv122qHQNhD6aGW8R8DEj4bqHXm
      #使用DesUtil.genDesKey()生成
      des-key: jMVCBsFGDQr9USHo
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: false
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: false
  #xss配置,安全防控，对于提交的内容含有css等需要配置白名单
  xss:
    enabled: true
    skip-url:
      - /feign/send-mail
      - /sale/agent/update
      - /sale/agent/addAgent
      - /sale/conventionRegistration/batchAdd
      - /sale/conventionRegistration/update
      - /sale/conventionPerson/update
      - /sale/conventionPerson/add
      - /institution/institution/add
      - /institution/institution/update
      - /institution/institutionCourse/update
      - /institution/institutionCourse/add
      - /institution/institutionProvider/add
      - /institution/institutionProvider/update
      - /institution/news/update
      - /weixin
      - /workflow/service/**
      - /log/saveErrorLog
      - /institution/institutionInfo/update
      - /institution/news/add
      - /help/help/update
      - /sale/studentOfferItem/editComment
      - /sale/client/editComment
      - /customTask/addTask
      - /customTask/updateTask
      - /customTask/updateTaskComment
      - /sale/studentInsurance/editComment
      - /institution/contract/editComment
      - /finance/receiptForm/editComment
      - /finance/paymentForm/editComment
      - /permission/staff/editComment
      - /permission/contract/editComment
      - /sale/event/editComment
      - /sale/agentContract/editComment
      - /sale/student/editComment
      - /sale/studentAccommodation/editComment
      - /institution/translationMapping/updateTranslations
      - /help/translationmapping/updateTranslations
      - /institution/translationMapping/updateTranslations
      - /platform/translationMapping/updateTranslations
      - /institution/areaCountryInfo/add
      - /institution/areaCityInfo/add
      - /sale/studentOffer/add
      - /sale/convention/add
      - /sale/conventionProcedure/update
      - /sale/conventionProcedure/add
      - /platform/agent/add
      - /platform/agent/update
      - /voting/voting/add
      - /exam/ExaminationPaper/update
      - /help/help/add
      - /help/help/update
      - /sale/annualConferenceRegistration/add
      - /feign/custom-send-send
      - /schoolGate/institution/news/update
      - /schoolGate/institution/news/add
      - /wx/wxCpTp/**
      - /sale/conventionPerson/update
      - /feign/batch-add
      - /feign/batch-send-email
      - /competition/competitionItem/add
      - /competition/competitionItem/update
      - /competition/competitionItem/movingOrder
      - /system/translation/getTranslation
      - /schoolGate/template/*
      - /sale/conventionAward/update
      - /sale/conventionAward/batchAdd
      - /sale/agent/update
      - /feign/batch-send-email
      - /platform/mso/sitemap/add
      - /platform/mso/sitemap/update
      - /feign/custom-send-mail-by-body
      - /institution/areaCity/update
      - /institution/areaState/update
      - /institution/institutionDeadlineInfo/update
      - /institution/institutionDeadlineInfo/add
      - /institution/institutionScholarship/update
      - /institution/institutionScholarship/add
      - /institution/institutionAppFee/update
      - /institution/institutionAppFee/add
      - /sale/eventPlan/add
      - /sale/eventPlan/update
      - /sale/translationMapping/updateTranslations
      - /feign/batch-send-email-custom
      - /sale/staffBdCode/getBdSelectAgentOnlineForm/{companyId}
      - /feign/get-system-send-email
      - /feign/send-system-mail
      - /feign/send-custom-mail
      - /feign/batch-update
      - /feign/batch-update-new
      - /feign/specified-person-send-email
      - /feign/batch-add-task
  #安全框架配置
  secure:
    #接口放行，暂时只是针对网关鉴权有效，各微服务有另外鉴权拦截，需要在微服务拦截处理待开发处理。
    skip-url:
      - /permission/password/resetPassword
      - /permission/company/getCompanyLogo
      - /permission/staff/verifyUser
      - /permission/staff/getVerifyCode
      - /permission1/**
      - /seata-demo/user/**
      - /websocket-center/**
      - /workflow-center/workflow/**
      - /workflow-center/editor/**
      - /institution-center/institution/media/uploadAttached/**
      - /sale-center/sale/agentRoleStaff/getAgentRoleStaffToIssue
      - /reminder/remindTask/batchAddToApp
      - /sale-center/sale/goproRegistration/**
      - /institution-center/institution/areaState/getByFkAreaCountryId/{id}
      - /institution-center/institution/areaCity/getByFkAreaStateId/{id}
      - /institution/institutionScholarship/getWcInstitutionScholarshipDatas
      - /institution/institutionDeadlineInfo/getWcInstitutionDeadlineInfoDtoDatas
      - /institution/institutionAppFee/getWcInstitutionAppFeeDatas
      - /institution/institutionScholarship/getWcInstitutionScholarshipList
      - /institution/institutionAppFee/getWcInstitutionAppFeeList
      - /institution/institutionDeadlineInfo/getWcInstitutionDeadlineInfoList
      - /institution/institutionScholarship/getIsOtherModule
      - /institution/institutionRanking/getWcComprehensiveRankingHome
      - /institution/institutionRanking/getWcComprehensiveRanking
      - /institution/institutionRanking/getWcCountryByKey
      - /institution/institutionRanking/getWcCourseTypeKey
      - /institution/institutionRanking/getWcMajorRanking
      - /institution/areaCountry/getNewsAreaCountryList
      - /sale-center/sale/goproNucleicAcid/**
      - /institution/institutionAppFee/getLevelType
      - /help-center/help/help/dataThreeNoPermission
      - /help-center/help/help/noPermission/{id}
      - /sale/annualReservationForm/**
      - /sale/annualConferenceRegistration/**
      - /wx/wxCpTp/**
      - /sale/AnnualHotelReservation/**
      - /institution/institutionCourse/getInstitutionCourseByNameMatch
      - /sale/appAgent/add
      - /sale/conventionAward/listAwardsAndTickets/{id}
      - /sale/conventionAwardWinner/luckDraw
      - /sale/conventionAwardWinner/luckDrawAgain
      - /sale/conventionAwardCode/getListTicketsNoUsedByAwardId/{id}
      - /sale/annualReservationForm/canadaWinterRetreat/{conventionId}
      - /sale/annualReservationForm/getCanadaWinterRetreatSelect/{conventionId}
      - /sale/appAgent/getAppAgentFormConfig
      - /sale/appAgent/getCommonCountrySelect
      - /sale/appAgent/getAppAgentFormDetail
      - /sale/appAgent/download
      - /school-gate-center/schoolGate/user/sendEmailCode/**
      - /school-gate-center/schoolGate/user/registra/**
      - /school-gate-center/schoolGate/user/retrievePassword/**
      - /sale/appAgent/validatedUser
      - /sale/annualReservationForm/**
      - /institution/areaCountry/getAreaCode
      - /sale/eventPlan/**
      - /sale/eventPlanRegistration/**
      - /institution/areaCountry/getCountryList
      - /sale/AnnualHotelReservation/getConventionHotel
      - /sale/staffBdCode/getBdSelectAgentOnlineForm/{companyId}
      - /feign/send-system-mail
      - /feign/send-custom-mail
      - /feign/get-student-offer-item-by-id
      - /feign/get-student-by-id
      - /feign/get-agent-by-id
      - /feign/get-country-by-id
      - /feign/get-institution-by-id
      - /feign/get-course-by-id
      - /feign/get-company-config-map
      - /feign/get-role-and-staff-by-table-id-and-roles
      - /feign/get-system-send-email
      - /feign/get-staff-super-id-by-staff-id
      - /feign/specified-person-send-email
      - /feign/update-email-queue
      - /feign//send-email-tasks
      - /feign/batch-add-task
      - /feign/get-task
      - /feign/get-task-item
      - /doc.html
      - /webjars/**
      - /feign/create-insurance-plan