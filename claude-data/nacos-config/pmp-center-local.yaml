spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      primary: pmp  
      datasource:
        pmp:
          username: root
          password: fzhmysql
          url: ***********************************************************************************************************************************************************************************************************************************************************************************************
        institution:
          username: root
          password: fzhmysql
          url: ******************************************************************************************************************************************************************************************************************************************************************************************************
        permission:
          username: root
          password: fzhmysql
          url: *****************************************************************************************************************************************************************************************************************************************************************************************************
  mail:
    host: smtp.exmail.qq.com
    port: 465
    username: <EMAIL>
    password: GZEtLhkf6WzpcPd9
    protocol: smtp
    default-encoding: utf-8
    properties:
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.starttls.required: true
      mail.smtp.ssl.enable: true
      mail.smtp.ssl.socketFactory.port: 465
      mail.smtp.ssl.socketFactory.class: javax.net.ssl.SSLSocketFactory
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

async:
  executor:
    corePoolSize: 4
    maxPoolSize: 10
    queueCapacity: 100
    threadNamePrefix: "pmp-async-task-"