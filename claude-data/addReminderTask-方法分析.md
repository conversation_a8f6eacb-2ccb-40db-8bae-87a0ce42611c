# AppAgentServiceImpl.addReminderTask 方法详细分析报告

## 📋 方法概述

`addReminderTask` 是 `AppAgentServiceImpl` 类中的一个私有方法，负责在代理申请提交后创建系统内部提醒任务和发送外部邮件通知。该方法体现了企业级应用中双重通知机制的设计理念。

### 方法签名
```java
private void addReminderTask(AddAppAgentContext addAppAgentContext)
```

### 调用场景
- 新增代理申请时（行号258）
- 更新代理申请时（行号326）

## 🔄 详细流程分析

### 1. 语言配置获取阶段
```java
// 获取中英文配置
Map<Long, String> versionConfigMap = permissionCenterClient
        .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
String versionValue2 = versionConfigMap.get(appAgentAddDto.getFkCompanyId());

// 获取员工英文名
Set<Long> staffIds = new HashSet<>();
staffIds.add(appAgentAddDto.getFkStaffId());
Map<Long, String> map1 = permissionCenterClient.getStaffEnNameByIds(staffIds);
String nameEn = map1.get(appAgentAddDto.getFkStaffId());
```

**关键特点：**
- 根据公司ID获取语言偏好配置（`"en"` 或 `"zh"`）
- 预获取员工的英文姓名，为英文模板做准备
- 使用权限中心服务获取配置，体现了微服务架构的特点

### 2. 内部员工提醒任务构建阶段

#### 2.1 英文模板构建
```java
if (versionValue2.equals("en")) {
    taskTitle = new StringBuilder("Proxy online application");
    map.put("staffName", nameEn);
    map.put("other", "，The online application form has been submitted, please refer to it.");
    map.put("agentName", appAgentAddDto.getName());
    taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER_ENGLISH);
}
```

#### 2.2 中文模板构建
```java
else {
    taskTitle = new StringBuilder("代理在线申请");
    String staffName = permissionCenterClient.getStaffName(appAgentAddDto.getFkStaffId()).getData();
    map.put("staffName", staffName);
    map.put("other", "，已经提交在线申请表单，请查阅。");
    map.put("agentName", appAgentAddDto.getName());
    taskRemark = MyStringUtils.getReminderTemplate(map, SaleCenterConstant.APP_AGENT_REMINDER);
}
```

#### 2.3 提醒任务对象构建
```java
RemindTaskDto remindTaskVo = new RemindTaskDto();
remindTaskVo.setTaskTitle(taskTitle.toString());
remindTaskVo.setTaskRemark(taskRemark);
remindTaskVo.setRemindMethod("1");                    // 邮件方式发送
remindTaskVo.setStatus(1);                           // 默认设置执行中
remindTaskVo.setTaskBgColor("#3788d8");              // 默认背景颜色
remindTaskVo.setFkStaffId(appAgentAddDto.getFkStaffId());
remindTaskVo.setStartTime(new Date());
remindTaskVo.setFkTableName(TableEnum.APP_AGENT.key);
remindTaskVo.setFkTableId(fkAppAgentId);
remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.APP_AGENT_ADD_NOTICE.key);
```

### 3. 外部代理联系人邮件发送阶段

#### 3.1 邮件参数准备
```java
Map<String, String> params = Maps.newHashMap();
params.put("email", appAgentContactPersonAddDtos.get(0).getEmail());
params.put("taskRemark", getTaskRemarkToContactPerson(fkAppAgentId, appAgentAddDto.getFkCompanyId(), versionValue2));
```

#### 3.2 多语言邮件发送
```java
if (versionValue2.equals("en")) {
    params.put("title", "Online form successfully submitted");
    params.put("taskTitle", "Dear Partner, Hello！");
    reminderCenterClient.batchSendEnEmail(Lists.newArrayList(params),
            ProjectKeyEnum.APP_AGENT_CONTACT_PERSON_NOTICE.key, versionValue2);
} else {
    params.put("title", "在线表单成功提交");
    params.put("taskTitle", "尊敬的合作方，您好！");
    reminderCenterClient.batchSendEmail(Lists.newArrayList(params),
            ProjectKeyEnum.APP_AGENT_CONTACT_PERSON_NOTICE.key);
}
```

## 📧 邮件通知机制深度解析

### 发件人/收件人设置机制

**🔍 关键发现：不是发件人收件人自己设置且自己去数据库拼接邮件模板发送邮件**

#### 实际机制：
1. **发件人**：由系统统一管理，通过 `reminderCenterClient` 微服务处理
2. **收件人确定逻辑**：
   - **内部提醒**：发送给指定的业务员（`appAgentAddDto.getFkStaffId()`）
   - **外部邮件**：发送给代理的第一联系人（`appAgentContactPersonAddDtos.get(0).getEmail()`）
3. **邮件路由**：通过不同的 Feign Client 接口实现分类发送

### 邮件发送服务架构
```
AppAgentServiceImpl 
    ↓
reminderCenterClient (Feign接口)
    ↓  
/batch-send-email (中文邮件)
/batch-send-en-email (英文邮件)
    ↓
ReminderCenter微服务 → 邮件队列 → 实际发送
```

## 🎨 邮件模板处理系统

### 1. 预设模板常量

#### 中文模板
```java
public static final String APP_AGENT_REMINDER = "<div class=\"desc\">\n" +
        "    <div>${staffName}您好：</div>\n" +
        "    <div>代理：${agentName}${other}</div>\n" +
        "</div>";
```

#### 英文模板
```java
public static final String APP_AGENT_REMINDER_ENGLISH = "<div class=\"desc\">\n" +
        "    <div>${staffName}Hello：</div>\n" +
        "    <div>Agent Name：${agentName}${other}</div>\n" +
        "</div>";
```

### 2. 模板参数替换机制
```java
public static String getReminderTemplate(Map<String, String> map, String htmlText) {
    if (GeneralTool.isNotEmpty(map)) {
        for (String s : map.keySet()) {
            if (GeneralTool.isNotEmpty(map.get(s))) {
                htmlText = htmlText.replace("${" + s + "}", map.get(s));
            } else {
                htmlText = htmlText.replace("${" + s + "}", "");
            }
        }
    }
    return htmlText;
}
```

**特点：**
- 简单的字符串替换机制
- 支持参数为空的情况处理
- 使用 `${}` 占位符语法

### 3. 外部邮件内容生成 (`getTaskRemarkToContactPerson`)

#### 加密链接生成
```java
String encrypt = AESUtils.Encrypt(String.valueOf(fkAppAgentId), AESConstant.AESKEY);
ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
String configValue3 = configVo.getValue3();
String link = configValue3 + "/apply-agent-online-form/?sign=" + encrypt;
```

#### 中文邮件内容
```java
if (versionValue2.equals("zh")) {
    taskRemark = "<div class=\"desc\">\n" +
            "    <div>您已经使用在线表单成功提交了合作申请。在人工审核前，若内容有所变动或需要补充，您可以点击或复制以下链接在浏览器重新打开申请资料进行修改：</div>\n" +
            "    <a style='display:inline-block;margin-top:0' href = \"" + link + "\">" + link + "</a>\n" +
            "    <div>谢谢您的申请，我们会尽快审核，期待与您的合作。</div>\n" +
            "</div>";
}
```

#### 英文邮件内容（Base64编码）
```java
else {
    taskRemark = Base64.getEncoder().encodeToString(("<div class=\"desc\">\n" +
            "    <div>You have successfully submitted a partnership application using the online form...</div>\n" +
            "    <a style='display:inline-block;margin-top:0' href = \"" + link + "\">" + link + "</a>\n" +
            "    <div>Thank you for your application, we will review it as soon as possible...</div>\n" +
            "</div>").getBytes(StandardCharsets.UTF_8));
}
```

## 🌐 多语言支持实现

### 配置驱动的语言切换
1. **配置获取**：通过 `ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key` 获取公司级语言配置
2. **模板选择**：根据 `versionValue2` 的值（`"en"` 或 `"zh"`）选择对应模板
3. **员工姓名**：英文环境使用英文姓名，中文环境调用中文姓名接口
4. **邮件接口**：使用不同的 Feign 接口发送不同语言的邮件

### 语言处理差异
- **英文邮件**：使用 `batchSendEnEmail` 接口，传递 `version` 参数
- **中文邮件**：使用 `batchSendEmail` 接口，不传递 `version` 参数
- **英文内容**：外部邮件采用 Base64 编码存储

## 🔐 安全机制分析

### AES加密链接
```java
String encrypt = AESUtils.Encrypt(String.valueOf(fkAppAgentId), AESConstant.AESKEY);
String link = configValue3 + "/apply-agent-online-form/?sign=" + encrypt;
```

**安全特点：**
- 使用AES加密代理申请ID
- 防止直接暴露业务数据
- 生成安全的访问链接供外部用户访问

## 🏗️ 系统架构设计总结

### 1. 微服务架构特点
- **服务分离**：邮件功能独立为 reminder-center 微服务
- **配置中心化**：通过 permission-center 统一管理配置
- **Feign通信**：使用声明式的HTTP客户端进行服务间调用

### 2. 双重通知机制
- **内部通知**：创建系统内提醒任务，用于员工工作台显示
- **外部通知**：发送邮件给代理联系人，提供访问链接

### 3. 模板化设计
- **预设模板**：使用常量定义HTML邮件模板
- **参数化**：通过Map传递动态参数
- **多语言**：同一业务逻辑支持中英文切换

### 4. 数据流向
```
用户提交代理申请
    ↓
AppAgentServiceImpl.addReminderTask
    ↓
┌─────────────────┬─────────────────┐
│   内部提醒任务    │   外部邮件通知    │
│      ↓          │       ↓         │
│ reminderCenter  │ reminderCenter  │
│   .batchAdd     │ .batchSendEmail │
└─────────────────┴─────────────────┘
    ↓
系统提醒 + 邮件发送
```

## 🎯 关键技术点总结

1. **不是用户自定义邮件**：系统使用预设的发件人和模板，不是用户自己设置
2. **模板预设化**：邮件内容通过常量定义，不是动态拼接
3. **配置驱动**：语言版本通过配置中心管理，支持公司级配置
4. **安全访问**：外部链接使用AES加密，保护业务数据安全
5. **微服务通信**：通过Feign Client实现服务间解耦
6. **双语支持**：完整的中英文模板和接口支持

这个设计体现了企业级应用在通知系统方面的成熟架构：统一的邮件服务、配置化的多语言支持、安全的外部访问机制，以及清晰的业务流程分离。