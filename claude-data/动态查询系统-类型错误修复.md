# 动态查询系统 - 类型错误修复

## 问题描述

之前遇到的错误：
```
wrapper.isNull(columnName);
必需类型: SFunction<T,?>
提供: String
```

## 问题原因

MyBatis Plus的 `LambdaQueryWrapper` 需要使用Lambda函数（如 `Entity::getField`），而不能直接使用字符串字段名。

## 解决方案

### ✅ 修改方案：使用 QueryWrapper 替代 LambdaQueryWrapper

将所有的 `LambdaQueryWrapper<T>` 改为 `QueryWrapper<T>`，因为：

1. **QueryWrapper支持字符串字段名**：
```java
QueryWrapper<Entity> wrapper = new QueryWrapper<>();
wrapper.eq("column_name", value);  // ✅ 支持字符串
```

2. **LambdaQueryWrapper需要Lambda函数**：
```java
LambdaQueryWrapper<Entity> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(Entity::getField, value);  // ✅ 需要Lambda函数
wrapper.eq("column_name", value);     // ❌ 不支持字符串
```

### 修改内容

#### 1. DynamicQueryBuilder.java
```java
// 修改前
public <T> LambdaQueryWrapper<T> buildQueryWrapper(Object queryDto, Class<T> entityClass)

// 修改后  
public <T> QueryWrapper<T> buildQueryWrapper(Object queryDto, Class<T> entityClass)
```

#### 2. QueryUtils.java
```java
// 修改前
public static <T> LambdaQueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass)

// 修改后
public static <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass)
```

#### 3. 所有相关方法签名都已更新

## 使用方式

### ✅ 现在可以正常使用：

```java
// 1. 在DTO上添加注解
@Data
public class OrderListDto {
    @QueryField(type = QueryType.EQ, column = "fk_insurance_company_id")
    private Long insuranceCompanyId;
    
    @QueryField(type = QueryType.LIKE)
    private String insuranceNum;
}

// 2. 构建查询条件
QueryWrapper<InsuranceOrder> wrapper = QueryUtils.buildQuery(dto, InsuranceOrder.class);

// 3. 在Service中使用
public List<OrderVo> getOrderList(OrderListDto queryDto, Page page) {
    // 构建动态查询条件
    QueryWrapper<InsuranceOrder> wrapper = QueryUtils.buildQuery(queryDto, InsuranceOrder.class);
    
    // 添加自定义条件
    wrapper.orderByDesc("gmt_create");
    
    // 执行分页查询
    IPage<InsuranceOrder> iPage = GetCondition.getPage(
        PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())
    );
    
    IPage<InsuranceOrder> result = this.page(iPage, wrapper);
    
    // 转换为VO
    List<OrderVo> orderList = result.getRecords().stream()
        .map(order -> BeanCopyUtils.objClone(order, OrderVo::new))
        .collect(Collectors.toList());
    
    page.setAll((int) result.getTotal());
    return orderList;
}
```

### ✅ 支持自定义条件：

```java
QueryWrapper<InsuranceOrder> wrapper = QueryUtils.buildQuery(queryDto, InsuranceOrder.class, 
    w -> {
        // 添加权限控制
        w.eq("fk_company_id", SecureUtil.getCompanyId());
        // 添加状态过滤
        w.in("order_status", Arrays.asList(1, 2, 3));
        // 添加排序
        w.orderByDesc("gmt_create");
    }
);
```

## 优势

1. **✅ 类型兼容**：QueryWrapper完全支持字符串字段名
2. **✅ 功能完整**：支持所有查询类型（EQ、LIKE、IN、BETWEEN等）
3. **✅ 性能良好**：QueryWrapper性能与LambdaQueryWrapper相当
4. **✅ 易于使用**：不需要处理复杂的Lambda函数映射

## 验证

现在编译应该不会再有类型错误：

```bash
mvn clean compile -pl common
```

如果还有其他错误，请告诉我具体的错误信息。

## 总结

通过将 `LambdaQueryWrapper` 改为 `QueryWrapper`，完美解决了类型不匹配的问题，同时保持了所有功能的完整性。现在动态查询系统可以正常工作了！🎉
