# 代理合同到期提醒系统 - 后端详细设计文档

## 文档信息
- **项目名称**: HTI Java AIS v1 - 代理合同到期提醒系统
- **文档版本**: v1.0
- **创建时间**: 2025-01-08
- **文档类型**: 后端技术设计文档

## 1. 需求概述

基于新需求图片分析，需要实现**代理合同到期提醒和续签管理系统**，主要功能包括：
- 合同状态自动管理
- 多时间点到期提醒（30天、15天、7天、当天）
- 合同续签申请和审批流程
- 提醒记录和统计管理

## 2. 数据库设计

### 2.1 扩展现有表结构

#### 2.1.1 代理合同表 (m_agent_contract) 字段扩展
```sql
-- 添加合同状态管理相关字段
ALTER TABLE m_agent_contract ADD COLUMN contract_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '合同状态：ACTIVE=生效中,EXPIRING_SOON=即将到期,EXPIRED=已到期,RENEWED=已续签,TERMINATED=已终止';
ALTER TABLE m_agent_contract ADD COLUMN reminder_settings JSON COMMENT '提醒设置JSON：{days:[30,15,7,1], enabled:true}';
ALTER TABLE m_agent_contract ADD COLUMN auto_renewal BOOLEAN DEFAULT FALSE COMMENT '是否自动续签';
ALTER TABLE m_agent_contract ADD COLUMN renewal_notice_days INT DEFAULT 30 COMMENT '续签提醒提前天数';
ALTER TABLE m_agent_contract ADD COLUMN last_reminder_date DATETIME COMMENT '最后提醒时间';
ALTER TABLE m_agent_contract ADD COLUMN renewal_application_id BIGINT COMMENT '关联续签申请ID';
```

### 2.2 新增表结构

#### 2.2.1 合同提醒记录表 (m_agent_contract_reminder)
```sql
CREATE TABLE m_agent_contract_reminder (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    fk_agent_contract_id BIGINT NOT NULL COMMENT '代理合同ID',
    reminder_type VARCHAR(20) NOT NULL COMMENT '提醒类型：EXPIRE_30D,EXPIRE_15D,EXPIRE_7D,EXPIRE_1D',
    reminder_date DATETIME NOT NULL COMMENT '提醒时间',
    send_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '发送状态：PENDING=待发送,SENT=已发送,FAILED=发送失败',
    email_queue_id BIGINT COMMENT '邮件队列ID',
    error_message TEXT COMMENT '失败原因',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_user VARCHAR(32),
    update_user VARCHAR(32),
    is_deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_contract_id (fk_agent_contract_id),
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_send_status (send_status)
) COMMENT='代理合同提醒记录表';
```

#### 2.2.2 合同续签申请表 (m_agent_contract_renewal)
```sql
CREATE TABLE m_agent_contract_renewal (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    fk_original_contract_id BIGINT NOT NULL COMMENT '原合同ID',
    fk_agent_id BIGINT NOT NULL COMMENT '代理ID',
    application_code VARCHAR(50) UNIQUE NOT NULL COMMENT '申请编号',
    renewal_start_date DATE NOT NULL COMMENT '续签开始日期',
    renewal_end_date DATE NOT NULL COMMENT '续签结束日期',
    renewal_reason TEXT COMMENT '续签原因',
    commission_adjustment DECIMAL(10,4) COMMENT '佣金调整比例',
    additional_terms TEXT COMMENT '附加条款',
    application_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '申请状态：PENDING=待审批,APPROVED=已批准,REJECTED=已拒绝,CANCELLED=已取消',
    workflow_instance_id VARCHAR(64) COMMENT 'Activiti工作流实例ID',
    fk_new_contract_id BIGINT COMMENT '新合同ID（审批通过后生成）',
    applicant_user_id BIGINT COMMENT '申请人ID',
    application_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    approval_date DATETIME COMMENT '审批完成时间',
    rejection_reason TEXT COMMENT '拒绝原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_user VARCHAR(32),
    update_user VARCHAR(32),
    is_deleted BOOLEAN DEFAULT FALSE,
    INDEX idx_original_contract (fk_original_contract_id),
    INDEX idx_agent_id (fk_agent_id),
    INDEX idx_application_status (application_status),
    INDEX idx_workflow_instance (workflow_instance_id)
) COMMENT='代理合同续签申请表';
```

#### 2.2.3 合同状态变更历史表 (m_agent_contract_status_history)
```sql
CREATE TABLE m_agent_contract_status_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    fk_agent_contract_id BIGINT NOT NULL COMMENT '代理合同ID',
    old_status VARCHAR(20) COMMENT '原状态',
    new_status VARCHAR(20) NOT NULL COMMENT '新状态',
    change_reason VARCHAR(100) COMMENT '状态变更原因',
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型：AUTO=自动,MANUAL=手动',
    operator_user_id BIGINT COMMENT '操作人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_user VARCHAR(32),
    INDEX idx_contract_id (fk_agent_contract_id),
    INDEX idx_change_time (create_time)
) COMMENT='代理合同状态变更历史表';
```

## 3. 后端模块设计

### 3.1 枚举类设计

#### 3.1.1 合同状态枚举
```java
/**
 * 代理合同状态枚举
 */
@Getter
@AllArgsConstructor
public enum AgentContractStatusEnum {
    
    ACTIVE("ACTIVE", "生效中", false),
    EXPIRING_SOON("EXPIRING_SOON", "即将到期", true),
    EXPIRED("EXPIRED", "已到期", true),
    RENEWED("RENEWED", "已续签", false),
    TERMINATED("TERMINATED", "已终止", false);
    
    private final String code;
    private final String description;
    private final Boolean needsAction;
    
    private static final Map<String, AgentContractStatusEnum> STATUS_MAP = new HashMap<>();
    
    static {
        for (AgentContractStatusEnum status : AgentContractStatusEnum.values()) {
            STATUS_MAP.put(status.getCode(), status);
        }
    }
    
    public static AgentContractStatusEnum getByCode(String code) {
        return STATUS_MAP.get(code);
    }
}
```

#### 3.1.2 提醒类型枚举
```java
/**
 * 合同到期提醒类型枚举
 */
@Getter
@AllArgsConstructor
public enum ContractReminderTypeEnum {
    
    EXPIRE_30D("EXPIRE_30D", "30天到期提醒", 30),
    EXPIRE_15D("EXPIRE_15D", "15天到期提醒", 15),
    EXPIRE_7D("EXPIRE_7D", "7天到期提醒", 7),
    EXPIRE_1D("EXPIRE_1D", "当天到期提醒", 1);
    
    private final String code;
    private final String description;
    private final Integer daysBeforeExpiry;
    
    private static final Map<String, ContractReminderTypeEnum> TYPE_MAP = new HashMap<>();
    
    static {
        for (ContractReminderTypeEnum type : ContractReminderTypeEnum.values()) {
            TYPE_MAP.put(type.getCode(), type);
        }
    }
    
    public static ContractReminderTypeEnum getByCode(String code) {
        return TYPE_MAP.get(code);
    }
    
    public static List<ContractReminderTypeEnum> getDefaultReminderTypes() {
        return Arrays.asList(EXPIRE_30D, EXPIRE_15D, EXPIRE_7D, EXPIRE_1D);
    }
}
```

#### 3.1.3 续签申请状态枚举
```java
/**
 * 合同续签申请状态枚举
 */
@Getter
@AllArgsConstructor
public enum ContractRenewalStatusEnum {
    
    PENDING("PENDING", "待审批"),
    APPROVED("APPROVED", "已批准"),
    REJECTED("REJECTED", "已拒绝"),
    CANCELLED("CANCELLED", "已取消");
    
    private final String code;
    private final String description;
    
    private static final Map<String, ContractRenewalStatusEnum> STATUS_MAP = new HashMap<>();
    
    static {
        for (ContractRenewalStatusEnum status : ContractRenewalStatusEnum.values()) {
            STATUS_MAP.put(status.getCode(), status);
        }
    }
    
    public static ContractRenewalStatusEnum getByCode(String code) {
        return STATUS_MAP.get(code);
    }
}
```

### 3.2 实体类设计

#### 3.2.1 合同提醒记录实体
```java
/**
 * 代理合同提醒记录实体
 */
@Data
@TableName("m_agent_contract_reminder")
public class AgentContractReminder extends BaseEntity implements Serializable {
    
    @ApiModelProperty("代理合同ID")
    private Long fkAgentContractId;
    
    @ApiModelProperty("提醒类型")
    private String reminderType;
    
    @ApiModelProperty("提醒时间")
    private Date reminderDate;
    
    @ApiModelProperty("发送状态")
    private String sendStatus;
    
    @ApiModelProperty("邮件队列ID")
    private Long emailQueueId;
    
    @ApiModelProperty("失败原因")
    private String errorMessage;
    
    @ApiModelProperty("重试次数")
    private Integer retryCount;
}
```

#### 3.2.2 合同续签申请实体
```java
/**
 * 代理合同续签申请实体
 */
@Data
@TableName("m_agent_contract_renewal")
public class AgentContractRenewal extends BaseEntity implements Serializable {
    
    @ApiModelProperty("原合同ID")
    private Long fkOriginalContractId;
    
    @ApiModelProperty("代理ID")
    private Long fkAgentId;
    
    @ApiModelProperty("申请编号")
    private String applicationCode;
    
    @ApiModelProperty("续签开始日期")
    private Date renewalStartDate;
    
    @ApiModelProperty("续签结束日期")
    private Date renewalEndDate;
    
    @ApiModelProperty("续签原因")
    private String renewalReason;
    
    @ApiModelProperty("佣金调整比例")
    private BigDecimal commissionAdjustment;
    
    @ApiModelProperty("附加条款")
    private String additionalTerms;
    
    @ApiModelProperty("申请状态")
    private String applicationStatus;
    
    @ApiModelProperty("工作流实例ID")
    private String workflowInstanceId;
    
    @ApiModelProperty("新合同ID")
    private Long fkNewContractId;
    
    @ApiModelProperty("申请人ID")
    private Long applicantUserId;
    
    @ApiModelProperty("申请时间")
    private Date applicationDate;
    
    @ApiModelProperty("审批完成时间")
    private Date approvalDate;
    
    @ApiModelProperty("拒绝原因")
    private String rejectionReason;
}
```

### 3.3 DTO设计

#### 3.3.1 合同到期查询DTO
```java
/**
 * 合同到期查询DTO
 */
@Data
public class ContractExpiryQueryDto {
    
    @ApiModelProperty("合同状态列表")
    private List<String> statusList;
    
    @ApiModelProperty("到期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expiryStartDate;
    
    @ApiModelProperty("到期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expiryEndDate;
    
    @ApiModelProperty("代理ID")
    private Long agentId;
    
    @ApiModelProperty("代理公司ID")
    private Long agentCompanyId;
    
    @ApiModelProperty("提前提醒天数")
    private Integer reminderDays;
}
```

#### 3.3.2 续签申请DTO
```java
/**
 * 合同续签申请DTO
 */
@Data
public class ContractRenewalApplicationDto {
    
    @ApiModelProperty("原合同ID")
    @NotNull(message = "原合同ID不能为空")
    private Long originalContractId;
    
    @ApiModelProperty("续签开始日期")
    @NotNull(message = "续签开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date renewalStartDate;
    
    @ApiModelProperty("续签结束日期")
    @NotNull(message = "续签结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date renewalEndDate;
    
    @ApiModelProperty("续签原因")
    @NotBlank(message = "续签原因不能为空")
    private String renewalReason;
    
    @ApiModelProperty("佣金调整比例")
    private BigDecimal commissionAdjustment;
    
    @ApiModelProperty("附加条款")
    private String additionalTerms;
}
```

### 3.4 服务层设计

#### 3.4.1 合同状态管理服务接口
```java
/**
 * 代理合同状态管理服务接口
 */
public interface IAgentContractStatusService {
    
    /**
     * 检查并更新合同状态
     * @param contractId 合同ID
     * @return 更新后的状态
     */
    String checkAndUpdateContractStatus(Long contractId);
    
    /**
     * 批量检查并更新所有合同状态
     * @return 更新的合同数量
     */
    Integer batchUpdateContractStatus();
    
    /**
     * 获取即将到期的合同列表
     * @param queryDto 查询条件
     * @return 合同列表
     */
    List<AgentContractExpiryVo> getExpiringContracts(ContractExpiryQueryDto queryDto);
    
    /**
     * 手动更新合同状态
     * @param contractId 合同ID
     * @param newStatus 新状态
     * @param reason 变更原因
     */
    void manualUpdateContractStatus(Long contractId, String newStatus, String reason);
}
```

#### 3.4.2 合同提醒服务接口
```java
/**
 * 代理合同提醒服务接口
 */
public interface IAgentContractReminderService {
    
    /**
     * 创建合同到期提醒任务
     * @param contractId 合同ID
     * @param reminderTypes 提醒类型列表
     */
    void createContractReminders(Long contractId, List<String> reminderTypes);
    
    /**
     * 发送到期提醒
     * @param reminderType 提醒类型
     * @return 发送成功的提醒数量
     */
    Integer sendExpiryReminders(String reminderType);
    
    /**
     * 重试失败的提醒
     * @return 重试成功的数量
     */
    Integer retryFailedReminders();
    
    /**
     * 获取提醒发送记录
     * @param contractId 合同ID
     * @return 提醒记录列表
     */
    List<AgentContractReminderVo> getReminderHistory(Long contractId);
}
```

#### 3.4.3 合同续签服务接口
```java
/**
 * 代理合同续签服务接口
 */
public interface IAgentContractRenewalService {
    
    /**
     * 提交续签申请
     * @param applicationDto 申请信息
     * @return 申请ID
     */
    Long submitRenewalApplication(ContractRenewalApplicationDto applicationDto);
    
    /**
     * 审批续签申请
     * @param applicationId 申请ID
     * @param approved 是否批准
     * @param comment 审批意见
     */
    void approveRenewalApplication(Long applicationId, Boolean approved, String comment);
    
    /**
     * 生成续签合同
     * @param applicationId 申请ID
     * @return 新合同ID
     */
    Long generateRenewalContract(Long applicationId);
    
    /**
     * 取消续签申请
     * @param applicationId 申请ID
     * @param reason 取消原因
     */
    void cancelRenewalApplication(Long applicationId, String reason);
    
    /**
     * 获取续签申请列表
     * @param queryDto 查询条件
     * @return 申请列表
     */
    Page<AgentContractRenewalVo> getRenewalApplications(ContractRenewalQueryDto queryDto);
}
```

### 3.5 定时任务设计

#### 3.5.1 合同状态检查任务
```java
/**
 * 代理合同状态检查定时任务
 */
@Component
public class AgentContractStatusCheckJob {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentContractStatusCheckJob.class);
    
    @Resource
    private IAgentContractStatusService contractStatusService;
    
    /**
     * 每日检查合同状态任务
     */
    @XxlJob("agentContractStatusCheckJobHandler")
    public void checkContractStatus() throws Exception {
        XxlJobHelper.log("XXL-JOB, AgentContractStatusCheckJob start...");
        
        try {
            Integer updatedCount = contractStatusService.batchUpdateContractStatus();
            XxlJobHelper.log("合同状态检查完成，更新合同数量: {}", updatedCount);
            
        } catch (Exception e) {
            logger.error("合同状态检查任务执行异常", e);
            XxlJobHelper.log("合同状态检查任务执行异常: {}", e.getMessage());
            throw e;
        }
    }
}
```

#### 3.5.2 合同到期提醒任务
```java
/**
 * 代理合同到期提醒定时任务
 */
@Component
public class AgentContractReminderJob {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentContractReminderJob.class);
    
    @Resource
    private IAgentContractReminderService reminderService;
    
    /**
     * 30天到期提醒任务
     */
    @XxlJob("agentContract30DayReminderJobHandler")
    public void send30DayReminder() throws Exception {
        sendReminderByType(ContractReminderTypeEnum.EXPIRE_30D.getCode());
    }
    
    /**
     * 15天到期提醒任务
     */
    @XxlJob("agentContract15DayReminderJobHandler")
    public void send15DayReminder() throws Exception {
        sendReminderByType(ContractReminderTypeEnum.EXPIRE_15D.getCode());
    }
    
    /**
     * 7天到期提醒任务
     */
    @XxlJob("agentContract7DayReminderJobHandler")
    public void send7DayReminder() throws Exception {
        sendReminderByType(ContractReminderTypeEnum.EXPIRE_7D.getCode());
    }
    
    /**
     * 当天到期提醒任务
     */
    @XxlJob("agentContract1DayReminderJobHandler")
    public void send1DayReminder() throws Exception {
        sendReminderByType(ContractReminderTypeEnum.EXPIRE_1D.getCode());
    }
    
    private void sendReminderByType(String reminderType) throws Exception {
        XxlJobHelper.log("XXL-JOB, AgentContractReminderJob {} start...", reminderType);
        
        try {
            Integer sentCount = reminderService.sendExpiryReminders(reminderType);
            XxlJobHelper.log("{}提醒发送完成，发送数量: {}", reminderType, sentCount);
            
        } catch (Exception e) {
            logger.error("{}提醒任务执行异常", reminderType, e);
            XxlJobHelper.log("{}提醒任务执行异常: {}", reminderType, e.getMessage());
            throw e;
        }
    }
}
```

### 3.6 邮件模板设计

#### 3.6.1 邮件模板枚举扩展
```java
// 在EmailTemplateEnum中添加新的邮件模板
AGENT_CONTRACT_EXPIRE_30D_REMIND("AGENT_CONTRACT_EXPIRE_30D_REMIND", "代理合同30天到期提醒"),
AGENT_CONTRACT_EXPIRE_15D_REMIND("AGENT_CONTRACT_EXPIRE_15D_REMIND", "代理合同15天到期提醒"),
AGENT_CONTRACT_EXPIRE_7D_REMIND("AGENT_CONTRACT_EXPIRE_7D_REMIND", "代理合同7天到期提醒"),
AGENT_CONTRACT_EXPIRE_1D_REMIND("AGENT_CONTRACT_EXPIRE_1D_REMIND", "代理合同当天到期提醒"),
AGENT_CONTRACT_RENEWAL_APPLICATION("AGENT_CONTRACT_RENEWAL_APPLICATION", "代理合同续签申请通知"),
AGENT_CONTRACT_RENEWAL_APPROVED("AGENT_CONTRACT_RENEWAL_APPROVED", "代理合同续签批准通知"),
AGENT_CONTRACT_RENEWAL_REJECTED("AGENT_CONTRACT_RENEWAL_REJECTED", "代理合同续签拒绝通知");
```

#### 3.6.2 邮件Helper实现
```java
/**
 * 代理合同到期提醒邮件Helper
 */
@Component("agentContractExpireReminderEmailHelper")
public class AgentContractExpireReminderEmailHelper extends EmailAbstractHelper {
    
    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            // 1. 解析业务数据
            AgentContractReminderDto reminderDto = JSON.parseObject(
                emailSenderQueue.getBusinessContent(), 
                AgentContractReminderDto.class
            );
            
            // 2. 组装邮件数据
            Map<String, String> templateData = assembleEmailData(reminderDto);
            
            // 3. 获取邮件模板
            String template = getEmailTemplate(reminderDto.getReminderType());
            
            // 4. 替换模板变量
            String emailContent = ReminderTemplateUtils.getReminderTemplate(templateData, template);
            
            // 5. 发送邮件
            EmailSystemMQMessageDto emailMessage = new EmailSystemMQMessageDto();
            emailMessage.setTitle(getEmailTitle(reminderDto));
            emailMessage.setContent(emailContent);
            emailMessage.setToEmail(reminderDto.getAgentEmail());
            
            remindTaskQueueService.sendSystemMail(emailMessage);
            
        } catch (Exception e) {
            log.error("代理合同到期提醒邮件发送失败", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("email_send_failed"));
        }
    }
    
    private Map<String, String> assembleEmailData(AgentContractReminderDto reminderDto) {
        Map<String, String> data = new HashMap<>();
        data.put("agentName", reminderDto.getAgentName());
        data.put("contractNum", reminderDto.getContractNum());
        data.put("expiryDate", DateUtil.formatDate(reminderDto.getExpiryDate()));
        data.put("daysToExpiry", String.valueOf(reminderDto.getDaysToExpiry()));
        data.put("renewalUrl", reminderDto.getRenewalUrl());
        return data;
    }
}
```

### 3.7 工作流集成

#### 3.7.1 续签审批流程定义
```xml
<!-- agent_contract_renewal.bpmn -->
<process id="agentContractRenewalProcess" name="代理合同续签审批流程">
    <startEvent id="start"/>
    
    <userTask id="businessManagerApproval" name="业务经理审批">
        <candidateGroups>business_manager</candidateGroups>
    </userTask>
    
    <exclusiveGateway id="businessApprovalGateway"/>
    
    <userTask id="financeApproval" name="财务审批" activiti:candidateGroups="finance_manager">
        <conditionExpression>${commissionAdjustment != null}</conditionExpression>
    </userTask>
    
    <userTask id="finalApproval" name="最终审批">
        <candidateGroups>contract_manager</candidateGroups>
    </userTask>
    
    <serviceTask id="generateContract" name="生成续签合同">
        <activiti:expression>${agentContractRenewalService.generateRenewalContract(applicationId)}</activiti:expression>
    </serviceTask>
    
    <endEvent id="end"/>
</process>
```

### 3.8 API接口设计

#### 3.8.1 合同状态管理API
```java
/**
 * 代理合同状态管理控制器
 */
@Api(tags = "代理合同状态管理")
@RestController
@RequestMapping("/agentContract/status")
public class AgentContractStatusController {
    
    @Resource
    private IAgentContractStatusService contractStatusService;
    
    /**
     * 获取即将到期的合同列表
     */
    @PostMapping("/expiring")
    @ApiOperation("获取即将到期的合同列表")
    public ResponseBo<List<AgentContractExpiryVo>> getExpiringContracts(
            @RequestBody ContractExpiryQueryDto queryDto) {
        
        List<AgentContractExpiryVo> contracts = contractStatusService.getExpiringContracts(queryDto);
        return new ListResponseBo<>(contracts);
    }
    
    /**
     * 手动更新合同状态
     */
    @PostMapping("/update")
    @ApiOperation("手动更新合同状态")
    @OperationLogger(type = LoggerOptTypeConst.UPDATE, module = LoggerModulesConsts.AGENT_CONTRACT)
    public ResponseBo updateContractStatus(
            @RequestParam Long contractId,
            @RequestParam String newStatus,
            @RequestParam String reason) {
        
        contractStatusService.manualUpdateContractStatus(contractId, newStatus, reason);
        return ResponseBo.ok();
    }
}
```

#### 3.8.2 合同续签API
```java
/**
 * 代理合同续签控制器
 */
@Api(tags = "代理合同续签管理")
@RestController
@RequestMapping("/agentContract/renewal")
public class AgentContractRenewalController {
    
    @Resource
    private IAgentContractRenewalService renewalService;
    
    /**
     * 提交续签申请
     */
    @PostMapping("/apply")
    @ApiOperation("提交合同续签申请")
    @OperationLogger(type = LoggerOptTypeConst.ADD, module = LoggerModulesConsts.AGENT_CONTRACT)
    public ResponseBo<Long> submitRenewalApplication(
            @Valid @RequestBody ContractRenewalApplicationDto applicationDto) {
        
        Long applicationId = renewalService.submitRenewalApplication(applicationDto);
        return ResponseBo.ok(applicationId);
    }
    
    /**
     * 获取续签申请列表
     */
    @PostMapping("/list")
    @ApiOperation("获取续签申请列表")
    public ResponseBo<Page<AgentContractRenewalVo>> getRenewalApplications(
            @RequestBody ContractRenewalQueryDto queryDto,
            SearchBean searchBean) {
        
        Page<AgentContractRenewalVo> applications = renewalService.getRenewalApplications(queryDto);
        return new PageResponseBo<>(applications);
    }
    
    /**
     * 审批续签申请
     */
    @PostMapping("/approve")
    @ApiOperation("审批续签申请")
    @OperationLogger(type = LoggerOptTypeConst.UPDATE, module = LoggerModulesConsts.AGENT_CONTRACT)
    public ResponseBo approveRenewalApplication(
            @RequestParam Long applicationId,
            @RequestParam Boolean approved,
            @RequestParam String comment) {
        
        renewalService.approveRenewalApplication(applicationId, approved, comment);
        return ResponseBo.ok();
    }
}
```

## 4. 技术实现要点

### 4.1 性能优化
- 使用批量查询避免N+1问题
- 合理设计数据库索引
- 使用Redis缓存频繁查询的数据
- 异步处理邮件发送

### 4.2 异常处理
- 统一使用GetServiceException
- 完善的重试机制
- 详细的错误日志记录

### 4.3 事务管理
- 合同状态更新使用事务保证一致性
- 续签申请流程使用分布式事务

### 4.4 监控告警
- 添加关键业务指标监控
- 设置邮件发送失败告警
- 监控定时任务执行状态

## 5. 开发实施计划

### 5.1 第一阶段：数据库和基础设施 (2-3天)
1. **数据库脚本执行**
   - 扩展现有表字段
   - 创建新表结构
   - 添加索引优化

2. **基础枚举和实体类**
   - 创建所有枚举类
   - 创建实体类
   - 配置MyBatis Mapper

### 5.2 第二阶段：核心服务开发 (4-5天)
1. **合同状态管理服务**
   - 实现状态检查逻辑
   - 批量更新功能
   - 状态变更记录

2. **合同提醒服务**
   - 提醒任务创建
   - 邮件发送集成
   - 失败重试机制

3. **合同续签服务**
   - 续签申请流程
   - 工作流集成
   - 合同生成逻辑

### 5.3 第三阶段：定时任务和邮件集成 (2-3天)
1. **XXL-JOB定时任务**
   - 状态检查任务
   - 提醒发送任务
   - 任务配置和监控

2. **邮件系统集成**
   - 邮件模板设计
   - Helper类实现
   - RocketMQ集成

### 5.4 第四阶段：API接口和测试 (2-3天)
1. **REST API开发**
   - Controller层实现
   - 参数验证
   - 返回结果封装

2. **集成测试**
   - 单元测试
   - 接口测试
   - 端到端测试

### 5.5 第五阶段：部署和监控 (1-2天)
1. **部署配置**
   - 环境配置
   - 数据库迁移
   - 服务部署

2. **监控告警**
   - 业务指标监控
   - 告警规则配置
   - 性能优化调试

## 6. 注意事项和最佳实践

### 6.1 开发规范遵循
- 严格遵循项目现有的代码风格
- 使用统一的异常处理机制
- 完善的日志记录和国际化支持
- 每次方法调用前检查方法和属性是否存在

### 6.2 性能和安全考虑
- 数据库操作避免在循环中进行
- 使用连接池和缓存优化性能
- 敏感信息使用AES加密存储
- 完善的权限验证和访问控制

### 6.3 测试和部署
- 充分的单元测试和集成测试
- 分阶段部署和灰度发布
- 完善的回滚策略
- 监控和告警机制

### 6.4 文档和维护
- 完善的API文档
- 数据库变更脚本记录
- 操作手册和故障排查指南
- 定期的代码审查和优化

---

## 附录

### A. 相关技术文档参考
- **CLAUDE.md**: 项目整体技术架构说明
- **TECH_SUMMARY.md**: 核心技术实现细节
- **现有代理管理模块**: `biz-service/ais-sale-center`相关代码

### B. 数据库索引设计建议
```sql
-- 合同状态查询优化
CREATE INDEX idx_contract_status_expiry ON m_agent_contract(contract_status, end_time);

-- 提醒任务查询优化  
CREATE INDEX idx_reminder_type_date ON m_agent_contract_reminder(reminder_type, reminder_date, send_status);

-- 续签申请查询优化
CREATE INDEX idx_renewal_status_date ON m_agent_contract_renewal(application_status, application_date);
```

### C. 配置参数建议
```yaml
# 合同提醒相关配置
agent:
  contract:
    reminder:
      enabled: true
      default-days: [30, 15, 7, 1]
      max-retry-count: 3
      batch-size: 100
    renewal:
      auto-approval: false
      default-duration-months: 12
```

该文档提供了完整的后端技术实现指导，可直接用于开发实施。