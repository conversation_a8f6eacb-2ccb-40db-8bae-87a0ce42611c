package com.get.aismiddle.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 发版信息
 */
@Data
@TableName("m_release_info")
public class MReleaseInfo extends BaseEntity implements Serializable {

    //平台应用Id
    @ApiModelProperty(value = "平台应用Id")
    private Long fkPlatformId;
    //平台应用CODE
    @ApiModelProperty(value = "平台应用CODE")
    private String fkPlatformCode;
    //标题
    @ApiModelProperty(value = "标题")
    private String title;
    //版本号
    @ApiModelProperty(value = "版本号")
    private String versionNum;
    //枚举：0待发布/1已发布/2已撤回
    @ApiModelProperty(value = "枚举：0待发布/1已发布/2已撤回")
    private Integer status;
    //发布时间
    @ApiModelProperty(value = "发布时间")
    private Date releaseTime;
    //发布人
    @ApiModelProperty(value = "发布人")
    private String releaseUser;
    //撤回时间
    @ApiModelProperty(value = "撤回时间")
    private Date withdrawTime;
    //撤回人
    @ApiModelProperty(value = "撤回人")
    private String withdrawUser;

}

