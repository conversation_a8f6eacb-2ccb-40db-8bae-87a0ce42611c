package com.get.aismiddle.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@TableName("m_release_info_item")
public class MReleaseInfoItem extends BaseEntity implements Serializable {
    //发版信息Id
    @ApiModelProperty(value = "发版信息Id")
    private Long fkReleaseInfoId;
    //标题
    @ApiModelProperty(value = "标题")
    private String title;
    //描述（富文本）
    @ApiModelProperty(value = "描述（富文本）")
    private String description;
    //权限类型：0全局/1角色权限
    @ApiModelProperty(value = "权限类型：0全局/1角色权限")
    private Integer permissionType;
    //系统资源Keys，逗号分隔
    @ApiModelProperty(value = "系统资源Keys，逗号分隔")
    private String fkResourceKeys;

}

