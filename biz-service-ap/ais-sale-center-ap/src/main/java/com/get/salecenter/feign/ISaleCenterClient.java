package com.get.salecenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.tool.api.Result;
import com.get.financecenter.dto.BatchDownloadAgentReconciliationDto;
import com.get.financecenter.dto.query.AgentSettlementQueryDto;
import com.get.financecenter.vo.AgentSettlementGrossAmountVo;
import com.get.institutioncenter.dto.NewEmailGetAgentDto;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.rocketmqcenter.dto.InsurancePlanMessageDto;
import com.get.salecenter.dto.CommissionSummaryDto;
import com.get.salecenter.dto.EventBillAccountNoticeDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.PayablePlanDto;
import com.get.salecenter.dto.StaffBdCodeDto;
import com.get.salecenter.dto.UpdatePayablePlanStatusSettlementDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.vo.AgenCommissionAndAgentSearchVo;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.AgentSettlementPageVo;
import com.get.salecenter.vo.CommissionSummaryPageVo;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.EventCostVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.PayablePlanVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.vo.StaffBdCodeVo;
import com.get.salecenter.vo.StudentOfferItemSendEmailVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.vo.StudentPlanVo;
import com.get.salecenter.vo.StudentServiceFeeSummaryVo;
import com.get.salecenter.vo.StudentServiceFeeVo;
import feign.Request;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_SALE_CENTER
)
public interface ISaleCenterClient {

    String API_PREFIX = "/feign";

    /**
     * @Description :feign调用 根据代理ids查找对应代理名称map.
     */
    String GET_AGENT_NAMES_BY_IDS = API_PREFIX + "/get-agent-names-by-ids";
    /**
     * feign 根据应付类型关键字和应付类型对应记录Id 查询
     */
    String GET_PAYABLE_PLAN = API_PREFIX + "/get-payable-plan";
    /**
     * feign 根据应付类型关键字和应付类型对应记录Id 查询
     */
    String GET_PAYABLE_PLAN_ID = API_PREFIX + "/get-payable-plan-id";
    /**
     * feign 应付计划详情
     */
    String GET_PAYABLE_PLAN_DETAIL = API_PREFIX + "/get-payable-plan-detail";
    /**
     * feign 根据应付计划id查询应付计划应付金额
     */
    String GET_PAYABLE_PLAN_AMOUNT_BY_ID = API_PREFIX + "/get-payable-plan-amount-by-id";
    String GET_RECEIVABLE_PLAN_ID = API_PREFIX + "/get-receivable-plan-id";
    /**
     * feign 根据应收计划id查询应收计划应收金额
     */
    String GET_RECEIVABLE_PLAN_AMOUNT_BY_ID = API_PREFIX + "/get-receivable-plan-amount-by-id";
    /**
     * feign 应收计划详情
     */
    String GET_RECEIVABLE_PLAN_BY_ID = API_PREFIX + "/get-receivable-plan-by-id";
    /**
     * feign 根据学生申请国家ids获取名称
     */
    String GET_NAMES_BY_STUDENT_APP_COUNTRY_IDS = API_PREFIX + "/get-names-by-student-app-country-ids";

    String GET_CURRENCY_NUM = API_PREFIX + "/get-currency-num";
    /**
     * feign 根据输入的名称 模糊查询对应的学生id
     */
    String GET_STUDENT_IDS = API_PREFIX + "/get-student-ids";

    String GET_SERVICE_FEE_STUDENT_IDS = API_PREFIX + "/get-service-fee-ids";
    /**
     * feign 根据输入的渠道名称 模糊查询对应的id
     */
    String GET_CHANNEL_IDS = API_PREFIX + "/get-channel-ids";
    /**
     * feign 根据输入的渠道名称 模糊查询对应的id
     */
    String GET_CHANNEL_NAMES_BY_IDS = API_PREFIX + "/get-channel-names-by-ids";
    /**
     * feign 根据输入的学生id 模糊查询对应的名称
     */
    String GET_STUDENT_NAME_BY_ID = API_PREFIX + "/get-student-name-by-id";
    /**
     * feign StudentOffer详情
     */
    String GET_STUDENT_OFFER_DETAIL = API_PREFIX + "/get-student-offer-detail";
    String GET_STUDENT_OFFER_ITEM_BY_ID = API_PREFIX + "/get-student-offer-item-by-id";
    String GET_STUDENT_OFFER_ITEM_BY_IDS = API_PREFIX + "/get-student-offer-item-by-ids";

    String GET_IN_CHANNEL_AND_PROVIDER_IDS = API_PREFIX + "/get-in-channel-and-provider-ids";

    String GET_STUDENT_OFFER_ITEM_BY_PAY_IDS = API_PREFIX + "/get-student-offer-item-by-pay-ids";

    String GET_ITEM_MEDIAS = API_PREFIX + "/get-item-medias";
    /**
     * 根据代理id获取佣金所需附件
     *
     * @Date 14:29 2022/11/29
     * <AUTHOR>
     */
    String GET_AGENT_COMMISSION_MEDIAS = API_PREFIX + "/get-agent-commission-medias";
    /**
     * feign 根据输入学生ids 查询对应的名称map
     */
    String GET_STUDENT_NAME_BY_IDS = API_PREFIX + "/get-student-name-by-ids";

    String GET_INVOICE_STUDENT_SELECTION = API_PREFIX + "/get-invoice-student-selection";
    /**
     * feign 学生下拉框数据
     */
    String GET_STUDENT_LIST = API_PREFIX + "/get-student-list";
    /**
     * feign  目标名称下拉框数据
     */
    String GET_TARGET_NAME = API_PREFIX + "/get-target-name";
    /**
     * feign 修改学生申请方案状态
     */
    String UPDATE_STUDENT_OFFER = API_PREFIX + "/update-student-offer";
    String NETTY_PUSH = API_PREFIX + "/netty-push";
    /**
     * feign 更新m_agent_contract表的status状态
     */
    String UPDATE_CHANGE_STATUS = API_PREFIX + "/update-change-status";
    /**
     * feign 开始流程
     */
    String START_CONTRACT_FLOW = API_PREFIX + "/start-contract-flow";
    /**
     * feign 根据表名更新状态
     */
    String CHANGE_STATUS = API_PREFIX + "/change-status";
    /**
     * feign m_agent_contract 根据id查询某条记录
     */
    String GET_AGENT_CONTRACT_BY_ID = API_PREFIX + "/get-agent-contract-by-id";
    /**
     * feign 根据手机号获取对应的峰会人员姓名
     */
    String GET_NAMES_BY_MOBILES = API_PREFIX + "/get-names-by-mobiles";
    /**
     * feign 获取BD团队配置IAE下的所有大区ids
     */
    String GET_AREA_REGION_IDS_BY_COMPANY_ID = API_PREFIX + "/get-area-region-ids-by-company-id";
    /**
     * feign 下拉(代理id)
     */
    String GET_OFFER_ITEM_SELECT_BY_AGENT_ID = API_PREFIX + "/get-offer-item-select-by-agent-id";
    /**
     * feign 下拉(提供商id)
     */
    String GET_OFFER_ITEM_SELECT_BY_PROVIDER_ID = API_PREFIX + "/get-offer-item-select-by-provider-id";
    String GET_AGENT_IDS = API_PREFIX + "/get-agent-ids";
    /**
     * feign 所属代理下拉框数据
     */
    String GET_AGENT_SELECT = API_PREFIX + "/get-agent-select";
    /**
     * feign 根据代理id查询名称
     */
    String GET_AGENT_NAME_BY_ID = API_PREFIX + "/get-agent-name-by-id";
    /**
     * feign 查找代理的跟进人StaffId,一个代理跟进人只有一个 判断是否激活
     */
    String GET_STAFF_BY_AGENT_ID = API_PREFIX + "/get-staff-by-agent-id";
    /**
     * feign 代理佣金结算列表
     */
    String AGENT_SETTLEMENT_LIST = API_PREFIX + "/agent-settlement-list";

    String GET_AGENT_SETTLEMENT_IDS = API_PREFIX + "/get-agent-settlement-ids";

    /**
     * 检查代理资料是否完善
     *
     * @Date 15:12 2022/11/28
     * <AUTHOR>
     */
    String AGENT_CHECK_AGENT_DATA = API_PREFIX + "/agent-check-agent-data";

    String GET_AGENT_COMPANY_ID = API_PREFIX + "/get-agent_company_id";

    String GET_AGENT_COMPANY_IDS = API_PREFIX + "/get-agent_company_ids";
    /**
     * feign 根据代理id获取该代理下级非结算口的代理ids
     */
    String GET_SUBORDINATE_NOT_PORT_AGENT_IDS_BY_ID = API_PREFIX + "/get-subordinate-not-port-agent-ids-by-id";
    /**
     * feign 财务-应付计划编辑详情回显
     */
    String FINANCE_PLAN_DETAILS = API_PREFIX + "/finance-plan-details";
    /**
     * feign 根据应付计划ids 获取结算标记
     */
    String GET_SETTLEMENT_MARK_BY_PAYABLE_PLAN_IDS = API_PREFIX + "/get-settlement-mark-by-payable-plan-ids";
    /**
     * feign 代理佣金结算提交
     */
    String SUBMIT_SETTLEMENT = API_PREFIX + "/submit-settlement";
    /**
     * feign 提交代理确认结算
     */
    String AGENT_CONFIRM_SETTLEMENT = API_PREFIX + "/agent-confirm-settlement";
    /**
     * feign 代理佣金结算取消
     */
    String CANCEL_SETTLEMENT = API_PREFIX + "/cancel-settlement";
    /**
     * feign 财务确认代理佣金结算
     */
    String FINANCE_CONFIRM_SETTLEMENT = API_PREFIX + "/finance-confirm-settlement";
    /**
     * feign 批量编辑应付计划
     */
    String BATCH_UPDATE_PAYABLE_PLAN = API_PREFIX + "/batch-update-payable-plan";
    /**
     * feign 财务佣金汇总列表
     */
    String COMMISSION_SUMMARY = API_PREFIX + "/commission-summary";
    /**
     * feign 提交财务结算汇总
     */
    String SUBMIT_FINANCIAL_SETTLEMENT_SUMMARY = API_PREFIX + "/submit-financial-settlement-summary";
    /**
     * feign 取消财务确认结算
     */
    String CANCEL_FINANCIAL_CONFIRMATION_SETTLEMENT = API_PREFIX + "/cancel-financial-confirmation-settlement";
    /**
     * feign 财务佣金汇总批次列表
     */
    String COMMISSION_SUMMARY_BATCH_LIST = API_PREFIX + "/commission-summary-batch-list";
    /**
     * 财务佣金汇总批次子项列表
     */
    String COMMISSION_SUMMARY_BATCH_ITEM_LIST = API_PREFIX + "/commission-summary-batch-item-list";
    /**
     * feign 财务佣金汇总批次子项列表详情回显
     */
    String COMMISSION_SUMMARY_BATCH_ITEM_DETAIL = API_PREFIX + "/commission-summary-batch-item-detail";
    /**
     * feign 保存结算汇总表汇率
     */
    String SAVE_EXCHANGE_RATE = API_PREFIX + "/save-exchange-rate";
    String SAVE_IS_EXCHANGE_INPUT = API_PREFIX + "/save-is-exchange-input";


    /**
     * feign 根据财务结算批次号获取代理应付计划
     */
    String GET_AGENT_PAYABLE_PLAN_BY_NUM_SETTLEMENT_BATCH = API_PREFIX + "/get-agent-payable-plan-by-num-settlement-batch";
    /**
     * feign 根据代理银行账号ids获取银行账号信息
     */
    String GET_AGENT_CONTRACT_ACCOUNT_BY_ACCOUNT_IDS = API_PREFIX + "/get-agent-contract-account-by-account-ids";
    /**
     * feign 根据代理ids获取银行账号信息
     */
    String GET_AGENT_CONTRACT_ACCOUNT_BY_AGENT_IDS = API_PREFIX + "/get-agent-contract-account-by-agent-ids";
    /**
     * feign 插入银行结算标记
     */
    String INSERT_PAYABLE_PLAN_SETTLEMENT_AGENT_ACCOUNT = API_PREFIX + "/insert-payable-plan-settlement-agent-account";


//	@GetMapping("getAreaRegionIdsByCompanyId")
//	public Set<Long> getAreaRegionIdsByCompanyId() {
//		return staffBdCodeService.getAreaRegionIdsByCompanyId();
//	}
    /**
     * feign调用，获取代理账户名称
     */
    String GET_AGENT_CONTRACT_BANK_ACCOUNT_NAME_BY_ID = API_PREFIX + "/get-agent-contract-bank-account-name-by-id";
    /**
     * feign 活动费用归口收款单下拉框
     */
    String GET_EVENT_COST_DTO_BY_RECEIPT_FORM_IDS = API_PREFIX + "/get-event-cost-dto-by-receipt-form-ids";
    /**
     * feign 根据receiptFormId获取活动费用归口Dtos
     */
    String GET_EVENT_COST_DTO_BY_RECEIPT_FORM_ID = API_PREFIX + "/get-event-cost-dto-by-receipt-form-id";
    /**
     * feign 根据ids获取应收计划列表
     */
    String GET_RECEIVABLE_PLAN_DTOS_BY_IDS = API_PREFIX + "/get-receivable-plan-dtos-by-ids";
    /**
     * feign 根据ids查询应收详情
     */
    String GET_RECEIVABLE_PLANS_DETAIL = API_PREFIX + "/get-receivable-plans-detail";
    /**
     * feign 验证课程删除逻辑验证
     */
    String DELETE_VALIDATE_COURSE = API_PREFIX + "/delete-validate-course";
    /**
     * feign 根据当前当前登陆人的权限获取可查看的代理ids
     */
    String GET_AGENT_ID_LIST_BY_LOGIN_STAFF_POWER = API_PREFIX + "/get-agent-id-list-by-login-staff-power";
    /**
     * feign 获取iFile Excel信息
     */
    String I_FILE_EXCEL_INFO = API_PREFIX + "/i-file-excel-info";
    /**
     * feign 获取iFile Excel信息
     */
    String I_FILE_GROUP_BY_CURRENCY_INFO = API_PREFIX + "/ii-file-group-by-currency-info";
    /**
     * feign 获取批次号汇率
     */
    String GET_SETTLEMENT_BATCH_EXCHANG = API_PREFIX + "/get-settlement-batch-exchang";
    /**
     * feign 根据应付计划Ids获取应付计划列表
     */
    String GET_PAYABLE_PLAN_BY_IDS = API_PREFIX + "/get-payable-plan-by-ids";
    String GET_PAYABLE_PLAN_DETAIL_BY_IDS = API_PREFIX + "/get-payable-plan-detail-by-ids";
    String FIND_OFFER_ITEM_BY_PAY_IDS = API_PREFIX + "/find-offer-item-by-pay-ids";
    String FIND_OFFER_ITEM_BY_RECEIVABLE_IDS = API_PREFIX + "/find-offer-item-by-receivable-ids";

    String GET_DEFER_ENTRANCE_TIME_BY_IDS = API_PREFIX + "/get-defer-entrance-time-by-ids";
    /**
     * feign 根据ids获取应收计划列表
     */
    String FIND_RECEIVABLE_PLAN_BY_IDS = API_PREFIX + "/find-receivable-plan-by-ids";
    /**
     * feign 获取计划ids
     */
    String GET_PLAN_IDS_BY_TABLE_NAME_AND_CHANNEL_ID = API_PREFIX + "/get-plan-ids-by-table-name-and-channel-id";
    /**
     * feign 批量更新结算标记
     */
    String UPDATE_SETTLEMENT_AGENT_ACCOUNT = API_PREFIX + "/update-settlement-agent-account";
    /**
     * feign 佣金生成付款单后 根据应付计划id更新 分期表付款单id
     */
    String UPDATE_SETTLEMENT_INSTALLMENT_PAYMENT_FORM_ITEM_ID = API_PREFIX + "/update-settlement-installment-payment-form-item-id";
    /**
     * feign 佣金结算 修改实际支付金额
     */
    String UPDATE_INSTALLMENT_AMOUNT_ACTUAL = API_PREFIX + "/update-installment-amount-actual";
    /**
     * feign 根据新增/编辑的 收款单子单信息 更新对应的分期表数据
     */
    String INSERT_SETTLEMENT_INSTALLMENT_BY_RECEIPT_FORM_ITEM = API_PREFIX + "/insert-settlement-installment-by-receipt-form-item";
    /**
     * feign 检查收款单子项是否处于结算中或结算完成 true:没有处于结算中的，可以编辑 并删除批次表数据
     */
    String CHECK_SETTLEMENT_STATUS_BY_RECEIPT_FORM_ITEM_IDS = API_PREFIX + "/check-settlement-status-by-receipt-form-item-ids";
    String AGENT_IS_KEY_EXPIRED = API_PREFIX + "/agent-is-key-expired";
    String GET_OFFER_ITEM_SELECT_BY_CHANNEL_ID = API_PREFIX + "/get-offer-item-select-by-channel-id";
    /**
     * feign 根据ids查询应收详情
     */
    String GET_RECEIVABLE_PLANS_DETAIL_NEW = API_PREFIX + "/get-receivable-plans-detail-new";
    String GET_OFFER_ITEM_SELECT_BY_PEOVIDER_ID_NEW = API_PREFIX + "/get-offer-item-select-by-provider-id-new";
    /**
     * 获取应付计划
     */
    String GET_PAYABLE_INFO_BY_ID = API_PREFIX + "/get-payable-info-by-id";
    /**
     * 获取代理id
     */
    String GET_AGENT_ID_BY_PAYABLE_PLAN_ID = API_PREFIX + "/get-agent-id-by-payable-plan-id";
    String GET_PLAN_IDS_AND_COMPANY_NAME = API_PREFIX + "/get-plan-ids-and-company-name";
    String GET_STUDENT_ACCOMMODATION_ID = API_PREFIX + "/get-student-accommodation-id";

    String GET_STUDENT_ACCOMMODATION_BY_ID = API_PREFIX + "/get-student-accommodation-by-id";

    String GET_STUDENT_INSURANCE_ID = API_PREFIX + "/get-student-insurance-id";

    String GET_STUDENT_INSURANCE_BY_ID = API_PREFIX + "/get-student-insurance-by-id";


    String GET_RECEIPT_FORM_RECEIVABLE_PLAN_PAGINATION_INFO = API_PREFIX + "/get-receipt-form-receivable-plan-pagination-info";
    /**
     * 财务结算汇总锁定代理(第四步锁定代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    String FINANCIAL_SETTLEMENT_AGENT_LOCKING = API_PREFIX + "/financial-settlement-agent-locking";
    /**
     * feign 财务结算汇总解锁代理(第四步解锁代理)
     *
     * @Date 11:46 2022/5/18
     * <AUTHOR>
     */
    String FINANCIAL_SETTLEMENT_AGENT_UNLOCKING = API_PREFIX + "/financial-settlement-agent-unlocking";

    /**
     * feign 删除佣金结算（佣金第一二三步专用）
     *
     * @Date 16:38 2022/5/23
     * <AUTHOR>
     */
    String DELETE_SETTLEMENT = API_PREFIX + "/delete-settlement";

    String GET_BUSINESS_ID = API_PREFIX + "/get-business-id";

    String GET_AGENTS_BY_IDS = API_PREFIX + "/get-agents-by-ids";
    /**
     * 财务结算汇总删除佣金结算(第四步删除佣金结算按钮)
     *
     * @Date 17:24 2021/12/24
     * <AUTHOR>
     */
    String DELETE_FINANCIAL_SETTLEMENT_SUMMARY = API_PREFIX + "/delete-financial-settlement-summary";

    /**
     * 获取学校住宿提供商名称集合
     */
    String GET_A_STUDY_ACCOMMODATION_PROVIDER = API_PREFIX + "/get-a-study-accommodation-provider";
    /**
     *   留学住宿提供商
     */
    String GET_BUSINESS_PROVIDER_ID = API_PREFIX + "/get-business-provider-id";
    String GET_BUSINESS_PROVIDER_NAME_BY_ID = API_PREFIX + "/get-business-provider-name-by-id";
    String GET_BUSINESS_PROVIDER_NAME_BY_IDS = API_PREFIX + "/get-business-provider-name-by-ids";
    String GET_BUSINESS_PROVIDER_ID_BY_ACC_IDS = API_PREFIX +"/get-business-provider-id-by-acc-ids";
    String GET_BUSINESS_OBJECT_SELECTION = API_PREFIX +"/get-business-object-selection";
    String GET_PLAN_AND_TARGET_NAME =API_PREFIX + "/get-plan-and-target-name";

    /**
     * 获取代理
     */
    String GET_AGENT_BY_TARGET_NAME = API_PREFIX + "/get-agent-by-target-name";
    String GET_CANCEL_AGENT_CONFIRM_SETTLEMENT = API_PREFIX + "/get-cancel-agent-confirm-settlement";
    String GET_COMMISSION_ACTIVE_STATUS_BY_INSTITUTION_IDS = API_PREFIX + "/get-commission-active-status-by-institution-ids";

    String GET_RECEIVABLE_AMOUNT_INFO = API_PREFIX + "/get-receivable-amount-info";

    /**
     * 获取代理合同
     */
    String GET_AGENT_CONTRACT_BY_AGENT_IDS = API_PREFIX + "/get-agent-contract-by-agent-ids";

    String GET_CONTRACT_BANK_ACCOUNT_NAME_BY_ID = API_PREFIX + "/get-contract-bank-account-name-by-id";

    /**
     * 获取业务提供商
     */
    String GET_BUSINESS_PROVIDER_BY_TARGET_NAME = API_PREFIX + "/get-business-provider-by-target-name";

    /**
     * 查询代理有合同信息且当前时间在有效期内，否则返回无效合同的代理名
     *
     * @Date 12:49 2022/7/13
     * <AUTHOR>
     */
    String CHECK_AGENT_CONTRACT_BY_AGENT_IDS = API_PREFIX + "/check-agent-contract-by-agent-ids";

    /**
     * 检查应收计划对应的应付计划 应付未付是否为0 true:已支付完 或 已生成过预付后第二次结算的分期数据了 false:未支付完 生成佣金分期表数据
     *
     * @Date 16:40 2022/8/4
     * <AUTHOR>
     */
    String CHECK_PAYMENT_STATUS_BY_RECEIVABLE_PLAN_ID = API_PREFIX + "/check-payment-status-by-receivable-plan-id";

    /**
     * feign 预付生成分期数据
     */
    String IS_PAY_IN_ADVANCE_INSERT_SETTLEMENT_INSTALLMENT = API_PREFIX + "/is_pay_in_advance_insert_settlement_installment";
    /**
     * feign 预付专用：根据应收计划删除应付计划对应的 非预付的分期数据
     */
    String DELETE_PAY_IN_ADVANCE_INSERT_SETTLEMENT_INSTALLMENT = API_PREFIX + "/delete_pay_in_advance_insert_settlement_installment";

    /**
     * feign 根据收款单子单id删除对应的分期表数据
     */
    String DELETE_SETTLEMENT_INSTALLMENT = API_PREFIX + "/delete_settlement_installment";

    String GET_ACCOUNT = API_PREFIX + "/get-account";

    String DATA_IMPORT = API_PREFIX + "/data-import";
    /**
     * feign 财务佣金汇总批次取消（第五步取消）
     */
    String CANCEL_FINANCIAL_BATCH_SETTLEMENT = API_PREFIX + "/cancel-financial-batch-settlement";
    /**
     * feign 根据应付计划id和type获取对应的未处理状态的预付金额
     */
    String GET_PREPAYMENT_AMOUNT_BY_PAYABLE_PLAN_ID = API_PREFIX + "/get-prepayment-amount-by-payable-plan-id";
    /**
     * 根据应收计划id获取对应的应付计划信息
     */
    String GET_PAYABLE_PLAN_BY_RECEIVABLE_PLAN_ID = API_PREFIX + "/get-payable-plan-by-receivable-plan-id";
    String GET_STUDENT_OFFER_ITEM_BY_STUDENT_OFFER_ITEM_STEP_ID = API_PREFIX + "/get-student-offitem-by-student-offer-item-step-id";

    /**
     * 封装应收计划结果
     */
    String PACK_RECEIVABLE_PLAN_RESULT = API_PREFIX + "/pack-receivable-plan-result";

    String GET_ACCOMMODATION_ID = API_PREFIX + "/get-accommodation-id";

    String GET_INSURANCE_ID = API_PREFIX + "/get-insurance-id";

    String GET_AGENT_CONTRACTS_BY_END_TIME = API_PREFIX + "/get-agent-contracts-by-end-time";

    String GET_MEDIA_AND_ATTACHED_BY_IAE_CRM = API_PREFIX + "/get-media-and-attached-by-iae-crm";

    String UPDATE_MEDIA_AND_ATTACHED_BY_ID = API_PREFIX + "/update-media-and-attached-by-id";
    /**
     * 修改付款单代理账户信息  - 修改对应的佣金结算信息
     *
     * @Date 10:42 2022/12/22
     * <AUTHOR>
     */
    String UPDATE_COMMISSION_SETTLEMENT = API_PREFIX + "/update-commission-settlement";

    String AUTO_RELATION_RECEIPTEN = API_PREFIX + "/auto-relation-receipten";

    String GET_AGENT_CONTRACT_PERSON_MOBILE_BY_AGENT_ID = API_PREFIX +"/get-agent-contract-person-mobile-by-agent-id";

    String GET_ITEM_STEP_SELECT = API_PREFIX +"/get-item-step-select";

    String GET_ITEM_STEP_SELECT_BY_STEP_KEY = API_PREFIX + "/get-item-step-select-by-step-key";

    String GET_STUDENT_OFFER_FOR_WORK_FLOW = API_PREFIX +"/get-student-offer-for-work-flow";

    String SEND_EVENT_BILL_ACCOUNT_EMAIL = API_PREFIX +"/send-event-bill-account-email";

    String GET_STUDENT_SERVICE_FEE_RECEIVABLE_PLAN = API_PREFIX + "/get-student-service-fee-receivable-plan";

    String GET_SERVICE_FEE_STUDENT_IDS_BY_IDS = API_PREFIX + "/get-service-fee-student-ids-by-ids";

    String GET_SERVICE_FEE_STUDENT_IDS_BY_ID = API_PREFIX + "/get-service-fee-student-ids-by-id";

    String GET_SERVICE_FEE_BY_ID = API_PREFIX + "/get-service-fee-by-id";

    String GET_MEDIA_AND_ATTACHED_BY_AGENT_IDS = API_PREFIX + "/get-media-and-attached-by-agent-ids";

    String GET_SETTLEMENT_INFO = API_PREFIX + "/get-settlement-info";

    String GET_BD_ID_BY_AGENT_IDS = API_PREFIX + "/get-bd-id-by-agent-ids";

    String GET_PROVIDER_INSTITUTION_ITEM = API_PREFIX + "/get-provider-institution-item";

    String GET_RECEIVABLE_PLANS_BY_SORT = API_PREFIX + "/get-receivable-plans-by-sort";

    String GET_STUDENT_BY_OFFER_ITEM_IDS = API_PREFIX + "/get-student-by-offer-item-ids";

    String GET_CONTACT_PERSON_EMAIL_MAP = API_PREFIX + "get-contact-person-email-map";

    String GET_CONTACT_PERSON_EMAIL_STAFF = API_PREFIX + "get-contact-person-email-staff";

    String DATA_IMPORT_ITEM_EMAIL = API_PREFIX + "data_import_item_email";

    String PENDING_SETTLEMENT_MARK = API_PREFIX + "/pending-settlement-mark";

    String DELETE_PAYABLE_PLAN_SETTLEMENT_FLAG = API_PREFIX + "/delete-payable-plan-settlement-flag";


    String GET_NAME_LABEL_LIST_BY_FK_TABLE_NAME = API_PREFIX + "/get-name-label-list-by-fk-table-name";

    String GET_AGENT_LIST_TO_EXPORT = API_PREFIX + "/get-agent-list-to-export";

    String PREPAYMENT_BUTTON_HTI = API_PREFIX + "/prepayment-button-hti";

    String CHECK_HTI_COMMISSION_IN_SETTLEMENT = API_PREFIX + "/check-hti-commission-in-settlement";

    String INVOICE_AMOUNT_PREPAYMENT_UPDATE_HTI = API_PREFIX + "/invoice-amount-prepayment-update-hti";

    String GET_NEW_AGENT_EMAILS = API_PREFIX + "/get-new-agent-emails";

    String GET_NEW_AGENT_ALL_EMAILS = API_PREFIX + "/get-new-agent-all-emails";

    String MANUAL_SINGLE_COMMISSION = API_PREFIX + "/manual-single-commission";

    String ANALYZE_OFFER=API_PREFIX + "/analyze-offer";

    String GET_STUDENT_OFFER_ITEM_BY_PARENT_ID = API_PREFIX + "/get-student-offer-item-by-parent-id";

    @PostMapping(GET_STUDENT_OFFER_ITEM_BY_PARENT_ID)
    Result<List<StudentOfferItem>> getStudentOfferItemByParentId(@RequestParam(value = "id") Long id);

    String GET_SERVICE_FEE_PROVIDER_IDS_BY_FEE_IDS = API_PREFIX + "/get-service-fee-provider-ids-by-fee-ids";

    String GET_RECEIVABLE_PLAN_SELECT_BY_PROVIDER = API_PREFIX + "/get-receivable-plan-select-by-provider";

    String KPI_PLAN_STATISTICS = API_PREFIX + "/kpi-plan-statistics";

    String GET_BUSINESS_PROVIDER_SELECT_BY_TYPE_KEY = API_PREFIX + "/get-business-provider-select-by-type-key";

    String GET_AGENTS_BY_AGENT_COMMISSION_TYPE_AND_AGENT_IS_IT_BOUND = API_PREFIX + "/get_agents_by_agent_commission_type_and_agent_is_it_bound";

    String UPDATE_PAYABLE_PLAN_STATUS_SETTLEMENT = API_PREFIX + "/update-payable-plan-status-settlement";

    String GET_PAYABLE_PLAN_BY_RECEIVABLE_PLAN_IDS = API_PREFIX + "/get-payable-plan-by-receivable-plan-ids";

    String SAVE_BATCH_MEDIA_AND_ATTACHED = API_PREFIX + "/save-batch-media-and-attached";

    String GET_MEDIA_AND_ATTACHED_DTO_BY_FK_TABLE_IDS = API_PREFIX + "/get-media-and-attached-dto-by-fk-table-ids";


    String GET_AGENT_BY_ID = API_PREFIX + "/get-agent-by-id";

    String GET_STUDENT_BY_ID =  API_PREFIX + "/get-student-by-id";

    String GET_ROLE_AND_STAFF_BY_TABLE_ID_AND_ROLES =  API_PREFIX + "/get-role-and-staff-by-table-id-and-roles";

    String GET_EVENT_BILL_BY_ID = API_PREFIX + "/get-event-bill-by-id";
    String CREATE_INSURANCE_PLAN = API_PREFIX + "/create-insurance-plan";
    String GET_INSURANCE_ORDER_SELECT = API_PREFIX + "/get-insurance-order-select";
    String GET_PLAN_IDS_BY_BUSINESS_PROVIDER_ID = API_PREFIX + "/get-plan-ids-by-business-provider-id";

    String GET_CONVENTION_REGISTRATION_BY_RECEIPT_CODE = API_PREFIX + "/get-convention-registration-by-receipt-code";

    String GET_CONVENTION_BY_ID = API_PREFIX + "/get-convention-by-id";
    String GET_INSURANCE_PROVIDER_NAME_BY_IDS = API_PREFIX + "/get-insurance-provider-name-by-ids";

    String GET_STUDENT_PROJECT_ROLE_BY_ROLE_IDS = "/get-student-project-role-by-ids";

    /**
     * 根据id获取学生服务编号
     */
    String GET_STUDENT_SERVICE_FEE_NUM_BY_ID = API_PREFIX + "/get-student-service-fee-num-by-id";

    String GET_STUDENT_SERVICE_FEE_BY_ID = API_PREFIX + "/get-student-service-fee-by-id";

    /**
     * 获取bd信息
     */
    String GET_BD_INFORMATION = API_PREFIX + "/get-bd-information";
    /**
     * 新增附件信息
     */
    String ADD_MEDIA_AND_ATTACHED_LIST = API_PREFIX + "/add-media-and-attached-list";
    /**
     * 复制partner学生附件
     */
    String COPY_PARTNER_STUDENT_ATTACHED = API_PREFIX + "/copy-partner-student-attached";

    /**
     * 发送代理合同未签署提醒邮件
     */
    String SEND_AGENT_CONTRACT_UNSIGNED_REMINDERS = API_PREFIX + "/send-agent-contract-unsigned-reminders";

    /**
     * feign 发送代理合同未签署提醒邮件
     */
    @PostMapping(SEND_AGENT_CONTRACT_UNSIGNED_REMINDERS)
    Result<Boolean> sendAgentContractUnsignedReminders();

    /**
     * feign 解析offer
     *
     */
    @PostMapping(ANALYZE_OFFER)
    Result<Boolean> analyzeOffer();

    /**
     * @Description :feign调用 根据代理ids查找对应代理名称map
     * @Param [agentIds]
     * <AUTHOR>
     */
    @PostMapping(GET_AGENT_NAMES_BY_IDS)
    Result<Map<Long, String>> getAgentNamesByIds(@RequestBody Set<Long> agentIds);

    /**
     * feign 根据应付类型关键字和应付类型对应记录Id 查询
     *
     * @param typeKey
     * @param targetId
     * @return
     */
    @PostMapping(GET_PAYABLE_PLAN)
    Result<List<PayablePlanVo>> getPayablePlan(@RequestParam("typeKey") String typeKey,
                                               @RequestParam("targetId") Long targetId);

    /**
     * feign 根据应付类型关键字和应付类型对应记录Id 查询
     *
     * @param typeKey
     * @param targetId
     * @return
     */
    @PostMapping(GET_PAYABLE_PLAN_ID)
    Result<List<Long>> getPayablePlanId(@RequestParam(value = "typeKey", required = false) String typeKey,
                                        @RequestParam(value = "targetId", required = false) Long targetId);

    /**
     * feign 应付计划详情
     *
     * @param payPlanId
     * @return
     */
    @GetMapping(GET_PAYABLE_PLAN_DETAIL)
    Result<PayablePlanVo> getPayablePlanDetail(@RequestParam(value = "payPlanId") Long payPlanId);

    /**
     * feign 根据应付计划id查询应付计划应付金额
     *
     * @param id
     * @return
     */
    @PostMapping(GET_PAYABLE_PLAN_AMOUNT_BY_ID)
    Result<BigDecimal> getPayablePlanAmountById(@RequestParam(value = "id") Long id);

    @PostMapping(GET_RECEIVABLE_PLAN_ID)
    Result<List<Long>> getReceivablePlanId(@RequestParam(value = "typeKey", required = false) String typeKey,
                                           @RequestParam(value = "targetId", required = false) Long targetId);

    /**
     * feign 根据应收计划id查询应收计划应收金额
     *
     * @param id
     * @return
     */
    @PostMapping(GET_RECEIVABLE_PLAN_AMOUNT_BY_ID)
    Result<BigDecimal> getReceivablePlanAmountById(@RequestParam(value = "id") Long id);

    /**
     * feign 应收计划详情
     *
     * @param id
     * @return
     */
    @GetMapping(GET_RECEIVABLE_PLAN_BY_ID)
    Result<ReceivablePlanVo> getReceivablePlanById(@RequestParam(value = "id") Long id);

    /**
     * feign 根据学生申请国家ids获取名称
     *
     * @param studentAppCountryIds
     * @return
     */
    @PostMapping(GET_NAMES_BY_STUDENT_APP_COUNTRY_IDS)
    Result<Map<Long, String>> getNamesByStudentAppCountryIds(@RequestBody Set<Long> studentAppCountryIds);

    @GetMapping(GET_CURRENCY_NUM)
    Result<String> getCurrencyNum(@RequestParam Long id);

    /**
     * feign 根据输入的名称 模糊查询对应的学生id
     *
     * @param name
     * @return
     */
    @GetMapping(GET_STUDENT_IDS)
    Result<List<Long>> getStudentIds(@RequestParam(value = "name") String name);

    @GetMapping(GET_SERVICE_FEE_STUDENT_IDS)
    List<Long> getServiceFeeStudentIds(@RequestParam("targetName") String targetName);

    /**
     * feign 根据输入的渠道名称 模糊查询对应的id
     *
     * @param tableName
     * @param channelName
     * @return
     */
    @GetMapping(GET_CHANNEL_IDS)
    Result<List<Long>> getChannelIds(@RequestParam(required = false, value = "tableName") String tableName, @RequestParam(value = "channelName") String channelName);

    /**
     * feign 根据输入的渠道名称 模糊查询对应的id
     *
     * @param ids
     * @return
     */
    @GetMapping(GET_CHANNEL_NAMES_BY_IDS)
    Result<Map<Long, String>> getChannelNamesByIds(@RequestParam(value = "ids") Set<java.lang.Long> ids);

    /**
     * feign 根据输入的学生id 模糊查询对应的名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_STUDENT_NAME_BY_ID)
    Result<String> getStudentNameById(@RequestParam(value = "id") Long id);

    /**
     * feign StudentOffer详情
     *
     * @param id
     * @return
     */
    @GetMapping(GET_STUDENT_OFFER_DETAIL)
    Result<StudentOfferVo> getStudentOfferDetail(@RequestParam(value = "id") Long id);

    @GetMapping(GET_STUDENT_OFFER_ITEM_BY_ID)
    Result<StudentOfferItem> getStudentOfferItemById(@RequestParam(value = "id") Long id);

    @PostMapping(GET_STUDENT_OFFER_ITEM_BY_IDS)
    Result<List<SelItem>> getStudentOfferItemByIds(@RequestBody Set<Long> targetIds);

    @PostMapping(GET_IN_CHANNEL_AND_PROVIDER_IDS)
    Result< Map<String,Set<Long>>> getInChannelAndProviderIds(@RequestBody Set<Long> targetIds);


    @PostMapping(GET_STUDENT_OFFER_ITEM_BY_PAY_IDS)
    Result<List<Long>> getStudentOfferItemByPayIds(@RequestBody Set<Long> ids);

    @PostMapping(GET_ITEM_MEDIAS)
    Result<List<MediaAndAttachedVo>> getItemMedias(@RequestBody Set<Long> ids);

    /**
     * 根据代理id获取佣金所需附件
     *
     * @Date 14:29 2022/11/29
     * <AUTHOR>
     */
    @PostMapping(GET_AGENT_COMMISSION_MEDIAS)
    Result<List<MediaAndAttachedVo>> getAgentCommissionMedias(@RequestBody Set<Long> ids);
    /**
     * feign 根据输入学生ids 查询对应的名称map
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_STUDENT_NAME_BY_IDS)
    Result<Map<Long, String>> getStudentNameByIds(@RequestBody Set<Long> ids);


    @GetMapping(GET_INVOICE_STUDENT_SELECTION)
    List<BaseSelectEntity> getInvoiceStudentSelection(@RequestParam("companyId") Long companyId);


    /**
     * feign  目标名称下拉框数据
     *
     * @param companyId
     * @param tableName
     * @return
     */
    @GetMapping(GET_TARGET_NAME)
    Result<List<BaseSelectEntity>> getTargetName(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "tableName") String tableName);

    /**
     * feign 修改学生申请方案状态
     *
     * @param studentOffer
     * @return
     */
    @PostMapping(UPDATE_STUDENT_OFFER)
    Result<Boolean> updateStudentOffer(@RequestBody StudentOfferVo studentOffer);

    @GetMapping(NETTY_PUSH)
    Result<Boolean> nettyPush(@RequestParam(value = "itemId") Long itemId);

    /**
     * feign 更新m_agent_contract表的status状态
     *
     * @param agentContract
     * @return
     */
    @PostMapping(UPDATE_CHANGE_STATUS)
    Result<Boolean> updateChangeStatus(@RequestBody AgentContract agentContract);

    /**
     * feign 开始流程
     *
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @return
     */
    @GetMapping(START_CONTRACT_FLOW)
    Result<Boolean> startContractFlow(@RequestParam("businessKey") String businessKey,
                                      @RequestParam("procdefKey") String procdefKey,
                                      @RequestParam("companyId") String companyId);

    /**
     * feign 根据表名更新状态
     *
     * @param status
     * @param tableName
     * @param businessKey
     * @return
     */
    @GetMapping(CHANGE_STATUS)
    Result<Boolean> changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey);

    /**
     * feign m_agent_contract 根据id查询某条记录
     *
     * @param id
     * @return
     */
    @GetMapping(GET_AGENT_CONTRACT_BY_ID)
    Result<AgentContractVo> getAgentContractById(@RequestParam(value = "id") Long id);

    /**
     * feign 根据手机号获取对应的峰会人员姓名
     *
     * @param mobiles
     * @return
     */
    @GetMapping(GET_NAMES_BY_MOBILES)
    Result<Map<String, String>> getNamesByMobiles(@RequestParam("mobiles") Set<String> mobiles);

    /**
     * feign 获取BD团队配置IAE下的所有大区ids
     *
     * @return
     */
    @GetMapping(GET_AREA_REGION_IDS_BY_COMPANY_ID)
    Result<Set<Long>> getAreaRegionIdsByCompanyId();

    /**
     * feign 下拉(代理id)
     *
     * @param tableName
     * @param agentId
     * @return
     */
    @GetMapping(GET_OFFER_ITEM_SELECT_BY_AGENT_ID)
    Result<List<BaseSelectEntity>> getOfferItemSelectByAgentId(@RequestParam(value = "tableName") String tableName,
                                                               @RequestParam(value = "agentId") Long agentId);

    /**
     * feign 下拉(提供商id)
     *
     * @param tableName
     * @param providerId
     * @return
     */
    @GetMapping(GET_OFFER_ITEM_SELECT_BY_PROVIDER_ID)
    Result<List<BaseSelectEntity>> getOfferItemSelectByProviderId(@RequestParam(value = "tableName") String tableName,
                                                                  @RequestParam(value = "providerId") Long providerId);

    @GetMapping(GET_AGENT_IDS)
    Result<List<Long>> getAgentIds(@RequestParam String name);

    /**
     * feign 所属代理下拉框数据
     *
     * @param companyId
     * @return
     */
    @GetMapping(GET_AGENT_SELECT)
    Result<List<BaseSelectEntity>> getAgentSelect(@RequestParam("companyId") Long companyId);

    /**
     * feign 根据代理id查询名称
     *
     * @param agentId
     * @return
     */
    @GetMapping(GET_AGENT_NAME_BY_ID)
    Result<String> getAgentNameById(@RequestParam("agentId") Long agentId);

    /**
     * feign 查找代理的跟进人StaffId,一个代理跟进人只有一个 判断是否激活
     *
     * @param fkAgentId
     * @return
     */
    @GetMapping(GET_STAFF_BY_AGENT_ID)
    Result<Long> getStaffByAgentId(@RequestParam("fkAgentId") Long fkAgentId);

    /**
     * feign 代理佣金结算列表
     *
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    @PostMapping(AGENT_SETTLEMENT_LIST)
    AgentSettlementPageVo agentSettlementList(@RequestBody SearchBean<AgentSettlementQueryDto> page);

    @PostMapping(GET_AGENT_SETTLEMENT_IDS)
    List<Long> getAgentSettlementIds(@RequestBody AgentSettlementQueryDto agentSettlementVo, @RequestParam("local") String local, @RequestParam("staffId") Long staffId, @RequestParam("payInAdvanceFlag") boolean payInAdvanceFlag, @RequestParam("exportFlag") boolean exportFlag);

    /**
     * 检查代理资料是否完善
     *
     * @Date 15:12 2022/11/28
     * <AUTHOR>
     */
    @PostMapping(AGENT_CHECK_AGENT_DATA)
    boolean checkAgentData(@RequestBody List<BatchDownloadAgentReconciliationDto> batchDownloadAgentReconciliationVoList);

    @GetMapping(GET_AGENT_COMPANY_ID)
    Integer getAgentCompanyIdById(@RequestParam("id")Long id);

    @GetMapping(GET_AGENT_COMPANY_IDS)
    Map<Long,Long> getAgentCompanyIdByIds(@RequestParam("ids")Set<Long> ids);
    /**
     * feign 根据代理id获取该代理下级非结算口的代理ids
     *
     * @Date 11:15 2021/12/21
     * <AUTHOR>
     */
    @GetMapping(GET_SUBORDINATE_NOT_PORT_AGENT_IDS_BY_ID)
    Result<List<Long>> getSubordinateNotPortAgentIdsById(@RequestParam("agentId") Long agentId);

    /**
     * feign 财务-应付计划编辑详情回显
     *
     * @param planId
     * @return
     */
    @GetMapping(FINANCE_PLAN_DETAILS)
    Result<StudentPlanVo> financePlanDetails(@RequestParam("itemId") Long planId);

//    /**
//     * feign 根据应付计划ids 获取结算标记
//     *
//     * @param payablePlanIds
//     * @return
//     */
//    @PostMapping(GET_SETTLEMENT_MARK_BY_PAYABLE_PLAN_IDS)
//    Result<Map<Long, List<PayablePlanSettlementAgentAccountVo>>> getSettlementMarkByPayablePlanIds(@RequestBody List<Long> payablePlanIds);


    /**
     * feign 财务确认代理佣金结算
     *
     * @param payablePlanIdList
     * @return
     */
//    @PostMapping(FINANCE_CONFIRM_SETTLEMENT)
//    Result<Boolean> financeConfirmSettlement(@RequestBody List<Long> payablePlanIdList);

    /**
     * feign 批量编辑应付计划
     *
     * @param payablePlanDtoList
     * @return
     */
    @PostMapping(BATCH_UPDATE_PAYABLE_PLAN)
    Result<Boolean> batchUpdatePayablePlan(@RequestBody List<PayablePlanDto> payablePlanDtoList);

    /**
     * feign 财务佣金汇总列表
     *
     * @param page
     * @return
     */
    @PostMapping(COMMISSION_SUMMARY)
    Result<CommissionSummaryPageVo> commissionSummary(@RequestBody SearchBean<CommissionSummaryDto> page);

    /**
     * feign 根据财务结算批次号获取代理应付计划
     *
     * @Date 14:30 2021/12/28
     * <AUTHOR>
     */
    @GetMapping(GET_AGENT_PAYABLE_PLAN_BY_NUM_SETTLEMENT_BATCH)
    Result<List<PayablePlanVo>> getAgentPayablePlanByNumSettlementBatch(@RequestParam("numSettlementBatch") String numSettlementBatch);

    /**
     * feign 根据代理银行账号ids获取银行账号信息
     *
     * @Date 16:42 2022/1/6
     * <AUTHOR>
     */
    @PostMapping(GET_AGENT_CONTRACT_ACCOUNT_BY_ACCOUNT_IDS)
    Result<Map<Long, AgentContractAccountVo>> getAgentContractAccountByAccountIds(@RequestBody List<Long> accountIds);

    /**
     * 根据代理ids获取银行账号信息
     * @param agentIds
     * @return
     */
    @PostMapping(GET_AGENT_CONTRACT_ACCOUNT_BY_AGENT_IDS)
    Result<Map<Long, List<AgentContractAccountVo>>> getAgentContractAccountByAgentIds(@RequestBody List<Long> agentIds);

    /**
     * feign调用，获取代理账户名称
     *
     * @param id
     * @return
     */
    @GetMapping(GET_AGENT_CONTRACT_BANK_ACCOUNT_NAME_BY_ID)
    Result<String> getAgentContractBankAccountNameById(@RequestParam("id") Long id);

    /**
     * feign 活动费用归口收款单下拉框
     *
     * @param receiptFormIds
     * @return
     */
    @PostMapping(GET_EVENT_COST_DTO_BY_RECEIPT_FORM_IDS)
    Result<Map<Long, List<EventCostVo>>> getEventCostDtoByReceiptFormIds(@RequestBody Set<Long> receiptFormIds);

    /**
     * feign 根据receiptFormId获取活动费用归口Dtos
     *
     * @param receiptFormId
     * @return
     */
    @GetMapping(GET_EVENT_COST_DTO_BY_RECEIPT_FORM_ID)
    Result<List<EventCostVo>> getEventCostDtoByReceiptFormId(@RequestParam("receiptFormId") Long receiptFormId);

    /**
     * feign 根据ids获取应收计划列表
     *
     * @param fkReceivablePlanIds
     */
    @GetMapping(GET_RECEIVABLE_PLAN_DTOS_BY_IDS)
    Result<List<ReceivablePlanVo>> getReceivablePlanDtosByIds(@RequestParam("fkReceivablePlanIds") List<Long> fkReceivablePlanIds);

    /**
     * feign 根据ids查询应收详情
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_RECEIVABLE_PLANS_DETAIL)
    Result<List<ReceivablePlanVo>> getReceivablePlansDetail(@RequestBody Set<Long> ids);

    /**
     * feign 验证课程删除逻辑验证
     *
     * @param courseId
     * @return
     */
    @GetMapping(DELETE_VALIDATE_COURSE)
    Result<Boolean> deleteValidateCourse(@RequestParam(value = "courseId") Long courseId);

    /**
     * feign 根据当前当前登陆人的权限获取可查看的代理ids
     *
     * @return
     */
    @GetMapping(GET_AGENT_ID_LIST_BY_LOGIN_STAFF_POWER)
    Result<List<Long>> getAgentIdListByLoginStaffPower();


    /**
     * feign 根据应付计划Ids获取应付计划列表
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_PAYABLE_PLAN_BY_IDS)
    Result<List<PayablePlanVo>> getPayablePlanByIds(@RequestBody Set<Long> ids);

    @PostMapping(GET_PAYABLE_PLAN_DETAIL_BY_IDS)
    Result<List<PayablePlanVo>> getPayablePlanDetailsByIds(@RequestBody Set<Long> ids);

    @PostMapping(FIND_OFFER_ITEM_BY_PAY_IDS)
    Result<Map<Long, PayablePlanVo>> findOfferItemByPayIds(@RequestBody Set<Long> ids);

    @GetMapping(FIND_OFFER_ITEM_BY_RECEIVABLE_IDS)
    Result<Map<Long, ReceivablePlanVo>> findOfferItemByReceivableIds(@RequestParam(value = "ids") Set<Long> ids);

    @PostMapping(GET_DEFER_ENTRANCE_TIME_BY_IDS)
    Result<Map<Long, Object>> getDeferEntranceTimeByIds(@RequestBody Set<Long> ids);

    /**
     * feign 根据ids获取应收计划列表
     *
     * @param ids
     * @return
     */
    @PostMapping(FIND_RECEIVABLE_PLAN_BY_IDS)
    Result<List<ReceivablePlanVo>> findReceivablePlanByIds(@RequestBody Set<Long> ids);

    /**
     * feign 获取计划ids
     *
     * @param tableName
     * @param channelId
     * @return
     */
    @GetMapping(GET_PLAN_IDS_BY_TABLE_NAME_AND_CHANNEL_ID)
    Result<List<BaseSelectEntity>> getPlanIdsByTableNameAndChannelId(@RequestParam(value = "tableName") String tableName,
                                                                     @RequestParam(value = "channelId") Long channelId,
                                                                     @RequestParam(value = "receiptFormId") Long receiptFormId,
                                                                     @RequestParam(value = "fkTypeKey") String fkTypeKey,
                                                                     @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                                                     @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize);



    @PostMapping(AGENT_IS_KEY_EXPIRED)
    Result<Boolean> agentIsKeyExpired();

    @PostMapping(GET_ACCOUNT)
    Result<AgentContractAccount> getAccount(@RequestParam("agentId") Long agentId, @RequestParam("backNum") String backNum);

    /**
     * feign 获取学习计划
     *
     * @param
     * @return
     */
    @PostMapping("GET_STUDENT_OFFER_ITEM_BY_STUDENT_OFFER_ITEM_STEP_ID")
    Result<List<StudentOfferItemVo>> getStudentOfferItemByStudentOfferItemStepId(@RequestBody List<Long> fkTableIds);

    /**
     * 根据应收计划id获取对应的应付计划信息
     *
     * @Date 23:55 2022/4/21
     * <AUTHOR>
     */
    @PostMapping(GET_PAYABLE_PLAN_BY_RECEIVABLE_PLAN_ID)
    Result<PayablePlan> getPayablePlanByReceivablePlanId(@RequestParam("fkReceivablePlanId") Long fkReceivablePlanId);

    /**
     * 根据应收计划ids获取对应的应付计划信息
     *
     * @param fkReceivablePlanIdList
     * @return
     */
    @PostMapping(GET_PAYABLE_PLAN_BY_RECEIVABLE_PLAN_IDS)
    Result<List<PayablePlan>> getPayablePlanByReceivablePlanIds(@RequestBody List<Long> fkReceivablePlanIdList);

    @GetMapping(GET_OFFER_ITEM_SELECT_BY_CHANNEL_ID)
    Result<List<BaseSelectEntity>> getOfferItemSelectByChannelId(@RequestParam("tableName") String tableName, @RequestParam("channelId") Long channelId
            , @RequestParam("receiptFormId") Long receiptFormId, @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber, @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize);

    /**
     * feign 根据ids查询应收详情
     *
     * @param ids
     * @return
     */
    @PostMapping(GET_RECEIVABLE_PLANS_DETAIL_NEW)
    Result<List<ReceivablePlanVo>> getReceivablePlansDetailNew(@RequestBody Set<Long> ids);

    @GetMapping(DATA_IMPORT)
    void dataImport();

    @GetMapping(GET_OFFER_ITEM_SELECT_BY_PEOVIDER_ID_NEW)
    Result<List<BaseSelectEntity>> getOfferItemSelectByProviderIdNew(@RequestParam("tableName") String tableName, @RequestParam("providerId") Long providerId,
                                                                     @RequestParam("receiptFormId") Long receiptFormId,
                                                                     @RequestParam(value = "pageNumber", defaultValue = "1") Integer pageNumber, @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize);

    @PostMapping(GET_RECEIPT_FORM_RECEIVABLE_PLAN_PAGINATION_INFO)
    Result<Page> getReceiptFormReceivablePlanPaginationInfo(@RequestParam("tableName") String tableName, @RequestParam("channelId") Long channelId
            , @RequestParam("receiptFormId") Long receiptFormId, @RequestParam("fkTypeKey") String fkTypeKey, @RequestBody Page page);

    /**
     * 封装应收计划结果
     *
     * @param receivablePlanVos
     */
    @PostMapping(PACK_RECEIVABLE_PLAN_RESULT)
    Result<List<ReceivablePlanVo>> packReceivablePlanResult(@RequestBody List<ReceivablePlanVo> receivablePlanVos);

    /**
     * 获取应付计划
     *
     * @param payablePlanId
     * @return
     */
    @GetMapping(GET_PAYABLE_INFO_BY_ID)
    Result<PayablePlan> getPayableInfoById(@RequestParam("payablePlanId") Long payablePlanId);

    /**
     * 获取代理id
     *
     * @param payablePlanId
     * @return
     */
    @GetMapping(GET_AGENT_ID_BY_PAYABLE_PLAN_ID)
    Result<Long> getAgentIdByPayablePlanId(@RequestParam("payablePlanId") Long payablePlanId);

    @GetMapping(GET_PLAN_IDS_AND_COMPANY_NAME)
    Result<List<BaseSelectEntity>> getPlanIdsAndCompanyName(@RequestParam("typeKey") String typeKey
            , @RequestParam("targetId") Long targetId, @RequestParam("receiptFormId") Long receiptFormId);

    @GetMapping(GET_ACCOMMODATION_ID)
    Result<Long> getAccommodationAgentId(@RequestParam("targetId") Long targetId);

    @GetMapping(GET_INSURANCE_ID)
    Result<Long> getInsuranceAgentId(@RequestParam("targetId") Long targetId);


    @GetMapping(GET_STUDENT_ACCOMMODATION_ID)
    Result<Long> getStudentAccommodationId(@RequestParam("targetId") Long targetId);


    @GetMapping(GET_STUDENT_ACCOMMODATION_BY_ID)
    Result<StudentAccommodation> getStudentAccommodationById(@RequestParam("id") Long id);

    @GetMapping(GET_STUDENT_INSURANCE_ID)
    Result<Long> getStudentInsuranceId(@RequestParam("targetId") Long targetId);

    @GetMapping(GET_STUDENT_INSURANCE_BY_ID)
    Result<StudentInsurance> getStudentInsuranceById(@RequestParam("id") Long id);

    @PostMapping(GET_AGENTS_BY_IDS)
    Result<Map<Long, Agent>> getAgentsByIds(@RequestBody Set<Long> agentIds);

    @PostMapping(GET_BUSINESS_ID)
    Result<Set<Long>> getBusinessId(@RequestBody Map<String, Set<Long>> params);


    /**
     * 获取留学住宿提供商名称集合
     * @param ids
     * @return
     */
    @PostMapping(GET_A_STUDY_ACCOMMODATION_PROVIDER)
    Result<Map<Long,String>> getAStudyAccommodationProvider(@RequestBody Set<Long> ids);

    /**
     * 获取留学住宿提供商id
     * @param targetName
     * @return
     */
    @GetMapping(GET_BUSINESS_PROVIDER_ID)
    Result<List<Long>> getBusinessProviderId(@RequestParam("targetName") String targetName);

    @GetMapping(GET_BUSINESS_PROVIDER_NAME_BY_ID)
    String getBusinessProviderNameById(@RequestParam("id")Long id);

    @PostMapping(GET_BUSINESS_PROVIDER_ID_BY_ACC_IDS)
    Result<Set<Long>> getBusinessProviderIdByAccIds(@RequestBody Set<Long> ids);

    /**
     * 获取留学住宿提供商业务对象集合
     * @param companyId
     * @return
     */
    @GetMapping(GET_BUSINESS_OBJECT_SELECTION)
    Result<List<BaseSelectEntity>> getBusinessObjectSelection(@RequestParam("companyId") Long companyId);

    @GetMapping(GET_PLAN_AND_TARGET_NAME)
    Result<List<BaseSelectEntity>> getPlanAndTargetName(@RequestParam("targetId") Long targetId,@RequestParam("receiptFormId") Long receiptFormId);

    /**
     * 查询代理有合同信息且当前时间在有效期内，否则返回无效合同的代理名
     *
     * @Date 12:49 2022/7/13
     * <AUTHOR>
     */
    @PostMapping(CHECK_AGENT_CONTRACT_BY_AGENT_IDS)
    Result<List<String>> checkAgentContractByAgentIds(@RequestBody Set<Long> agentIdSet);

    @PostMapping(GET_AGENT_CONTRACTS_BY_END_TIME)
    Result<List<AgentContractVo>> getAgentContractsByEndTime(@RequestParam("date") String date);

    @GetMapping(GET_MEDIA_AND_ATTACHED_BY_IAE_CRM)
    List<SaleMediaAndAttached> getMediaAndAttachedByIaeCrm();

    @PostMapping(UPDATE_MEDIA_AND_ATTACHED_BY_ID)
    Boolean updateMediaAndAttachedById(@RequestBody SaleMediaAndAttached mediaAndAttached);

    @PostMapping(AUTO_RELATION_RECEIPTEN)
    Result<Boolean> autoRelationReceipten(@RequestBody Set<Long> receivablePlanIds);

    @PostMapping(GET_AGENT_CONTRACT_PERSON_MOBILE_BY_AGENT_ID)
    Map<Long, Object> getAgentContractPersonMobileByAgentId(@RequestBody Set<Long> agentIds);

    @PostMapping(GET_ITEM_STEP_SELECT)
    Result<List<BaseSelectEntity>> getItemStepSelect();

    @PostMapping(GET_ITEM_STEP_SELECT_BY_STEP_KEY)
    Result<Set<String>> getItemStepSelectByStepKey(@RequestBody List<String> stepKeys);

    @PostMapping(GET_STUDENT_OFFER_FOR_WORK_FLOW)
    Result<StudentOfferVo> getStudentOfferForWorkFlow(@RequestParam("id") Long id);

    @PostMapping(SEND_EVENT_BILL_ACCOUNT_EMAIL)
    Result<ResponseBo> sendEventBillAccountEmail(@RequestBody EventBillAccountNoticeDto eventBillAccountNoticeDto);

    /**
     * 收款单获取学生（客户）类型的应收计划
     * @param targetId
     * @param receiptFormId
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @GetMapping(GET_STUDENT_SERVICE_FEE_RECEIVABLE_PLAN)
    Result<List<BaseSelectEntity>> getStudentServiceFeeReceivablePlan(@RequestParam("targetId") Long targetId,@RequestParam("receiptFormId") Long receiptFormId,
                                                                      @RequestParam("pageNumber")Integer pageNumber, @RequestParam("pageSize") Integer pageSize);

    /**
     * Author Cream
     * Description : // 获取留学服务费的学生id
     * Date 2023/2/13 12:00
     * Params:
     * Return
     */
    @PostMapping(GET_SERVICE_FEE_STUDENT_IDS_BY_IDS)
    List<Long> getServiceFeeStudentIdsByIds(@RequestBody List<Long> ids);

    @GetMapping(GET_SERVICE_FEE_STUDENT_IDS_BY_ID)
    Result<Long> getServiceFeeStudentIdsById(@RequestParam("targetId") Long targetId);

    @GetMapping(GET_SERVICE_FEE_BY_ID)
    Result<StudentServiceFeeVo> getServiceFeeById(@RequestParam("targetId") Long targetId);

    @PostMapping(GET_BUSINESS_PROVIDER_NAME_BY_IDS)
    Result<Map<Long, String>> getBusinessProviderNameByIds(@RequestBody Set<Long> businessProviderIds);

    @GetMapping(GET_AGENT_BY_TARGET_NAME)
    List<BaseSelectEntity> getAgentByTargetName(@RequestParam("targetName")String targetName);

    @GetMapping(GET_BUSINESS_PROVIDER_BY_TARGET_NAME)
    List<BaseSelectEntity> getBusinessProviderByTargetName(@RequestParam("targetName") String targetName);


    /**
     * 获取提成激活状态
     * @param institutionIds
     * @return
     */
    @PostMapping(GET_COMMISSION_ACTIVE_STATUS_BY_INSTITUTION_IDS)
    Result<Map<Long, Boolean>> getCommissionActiveStatusByInstitutionIds(@RequestBody Set<Long> institutionIds);

    /**
     * 获取应收金额信息
     * @param receivablePlanIds
     * @return
     */
    @PostMapping(GET_RECEIVABLE_AMOUNT_INFO)
    List<ReceivablePlanVo> getReceivableAmountInfo(@RequestBody Set<Long> receivablePlanIds);


    /**
     * 获取代理合同
     * @param agentIds
     * @return
     */
    @PostMapping(GET_AGENT_CONTRACT_BY_AGENT_IDS)
    Map<Long, List<AgentContract>> getAgentContractByAgentIds(@RequestBody List<Long> agentIds);

    @GetMapping(GET_CONTRACT_BANK_ACCOUNT_NAME_BY_ID)
    Result<String> getContractBankAccountNameById(@RequestParam("fkBankAccountId") Long fkBankAccountId,@RequestParam("fkTypeKey") String fkTypeKey);
    @PostMapping(GET_MEDIA_AND_ATTACHED_BY_AGENT_IDS)
    List<MediaAndAttachedVo> getMediaAndAttachedByAgentIds(@RequestBody List<String> fkAgentIds_);

    /**
     * 根据代理id获取bd的id
     * @param agentIds
     * @return
     */
    @PostMapping(GET_BD_ID_BY_AGENT_IDS)
    Result<Map<Long,List<Long>>> getBdIdByAgentIds(@RequestBody Set<Long> agentIds);

    /**
     * 检验学校提供商与学习计划是否有绑定
     * @param
     * @return
     */
    @GetMapping(GET_PROVIDER_INSTITUTION_ITEM)
    Result<Boolean> getProviderInstitutionItem(@RequestParam("fkInstitutionId") Long fkInstitutionId,@RequestParam("fkInstitutionProviderId")Long fkInstitutionProviderId);

    @PostMapping(GET_RECEIVABLE_PLANS_BY_SORT)
    List<ReceivablePlanVo> getReceivablePlansBySort(@RequestBody Set<Long> planIds, @RequestParam("invoiceAmountSort") Boolean invoiceAmountSort,
                                                    @RequestParam("receiptAmountSort")Boolean receiptAmountSort,
                                                    @RequestParam("studentName")String studentName, @RequestParam("receiptFormId")Long receiptFormId);

    @PostMapping(GET_STUDENT_BY_OFFER_ITEM_IDS)
    List<StudentOfferItemVo> getStudentByOfferItemIds(@RequestBody List<Long> itemIds);

    @PostMapping(GET_CONTACT_PERSON_EMAIL_MAP)
    Map<String,String> getContactPersonEmailMap(@RequestParam("itemId") Long itemId);

    @PostMapping(GET_CONTACT_PERSON_EMAIL_STAFF)
    StudentOfferItemSendEmailVo getContactPersonEmailStaff(@RequestParam("itemId") Long itemId);

    @PostMapping(DATA_IMPORT_ITEM_EMAIL)
    void dataImportItemEmail();

    /**
     * 获取学校业务标记列表
     */
    @PostMapping(GET_NAME_LABEL_LIST_BY_FK_TABLE_NAME)
    Result<Map<Long,NameLabel>> getNameLabelListByFkTableName(@RequestParam("fkTableName") String fkTableName);

    @PostMapping(GET_AGENT_LIST_TO_EXPORT)
    Result<List<AgentSettlementGrossAmountVo>> getAgentListToExport(@RequestBody AgentSettlementQueryDto agentSettlementVo);


    @PostMapping(GET_NEW_AGENT_EMAILS)
    Result<Set<String>> getNewAgentEmails(@RequestBody NewEmailGetAgentDto newEmailGetAgentDto, Request.Options options);

    /**
     * 获取可发新闻 新代理所有邮箱
     */
    @GetMapping(GET_NEW_AGENT_ALL_EMAILS)
    Result<Set<String>> getNewAgentAllEmails(@RequestParam("newsId") Long newsId, @RequestParam("type") Integer type, Request.Options options);

    /**
     * 根据服务费id获取提供商id
     *
     * @param feeIds
     * @return
     */
    @PostMapping(GET_SERVICE_FEE_PROVIDER_IDS_BY_FEE_IDS)
    Result<List<Long>> getServiceFeeProviderIdsByFeeIds(@RequestBody List<Long> feeIds);

    /**
     * 提供商收款单获取应收计划下拉列表
     *
     * @param
     * @param
     * @return
     */
    @PostMapping(GET_RECEIVABLE_PLAN_SELECT_BY_PROVIDER)
    Result<List<BaseSelectEntity>> getReceivablePlanSelectByProvider(@RequestParam("providerId") Long providerId,
                                                             @RequestParam("receiptFormId") Long receiptFormId);

    /**
     * 定时任务，执行KPI方案统计
     */
    @PostMapping(KPI_PLAN_STATISTICS)
    void kpiPlanStatistics();

    /**
     * 通过key获取对应的业务提供商下拉列表
     *
     * @param companyId 公司ID
     * @param typeKey   业务类型关键字
     * @return
     */
    @PostMapping(GET_BUSINESS_PROVIDER_SELECT_BY_TYPE_KEY)
    Result<List<BaseSelectEntity>> getBusinessProviderSelectByTypeKey(@RequestParam("companyId") Long companyId, @RequestParam("typeKey") String typeKey);

    /**
     * 获取和代理等级相关的代理数据
     * @param agentCommissionTypeAgentDto
     * @return
     */
    @PostMapping(GET_AGENTS_BY_AGENT_COMMISSION_TYPE_AND_AGENT_IS_IT_BOUND)
    Result<AgenCommissionAndAgentSearchVo> getAgentCommissionTypeAndAgentIsBind(@RequestBody SearchBean<AgentCommissionTypeAgentDto> agentCommissionTypeAgentDto);

    /**
     * 修改应付计划佣金状态
     * @param updatePayablePlanStatusSettlementDto
     * @return
     */
    @PostMapping(UPDATE_PAYABLE_PLAN_STATUS_SETTLEMENT)
    Result<Boolean> updatePayablePlanStatusSettlement(@RequestBody UpdatePayablePlanStatusSettlementDto updatePayablePlanStatusSettlementDto);

    /**
     * 批量保存销售中心附件
     * @param mediaAndAttachedDtos
     * @return
     */
    @PostMapping(SAVE_BATCH_MEDIA_AND_ATTACHED)
    Result<Boolean> saveBatchMediaAndAttached(@RequestBody List<MediaAndAttachedDto> mediaAndAttachedDtos);

    /**
     * 根据表ids获取批量的附件
     * @param key
     * @param fkTableId
     * @return
     */
    @PostMapping(GET_MEDIA_AND_ATTACHED_DTO_BY_FK_TABLE_IDS)
    Result<Map<Long, List<MediaAndAttachedVo>>> getMediaAndAttachedDtoByFkTableIds(@RequestParam("key") String key, @RequestBody Set<Long> fkTableId);

    @GetMapping(GET_AGENT_BY_ID)
    Result<Agent> getAgentById(@RequestParam("fkAgentId") Long fkAgentId);

    @GetMapping(GET_STUDENT_BY_ID)
    Result<Student> getStudentById(@RequestParam("studentId") Long studentId);

    @GetMapping(GET_ROLE_AND_STAFF_BY_TABLE_ID_AND_ROLES)
    Result<Set<Long>> getRoleAndStaffByTableIdAndRoles(@RequestParam("fkStudentOfferId") Long fkStudentOfferId,@RequestParam("key") String key, @RequestParam("roleList") List<String> roleList);

    @GetMapping(GET_EVENT_BILL_BY_ID)
    Result<EventBillVo> getEventBillById(@RequestParam("eventBillId") Long eventBillId);

    /**
     * 澳小保创建应收应付
     * @param insurancePlanMessageDto
     * @return
     */
    @PostMapping(CREATE_INSURANCE_PLAN)
    Result createInsurancePlan(@RequestBody InsurancePlanMessageDto insurancePlanMessageDto);

    /**
     * 获取澳小保对象下拉框
     * @param companyId
     * @return
     */
    @GetMapping(GET_INSURANCE_ORDER_SELECT)
    Result<List<BaseSelectEntity>> getInsuranceOrderSelect(@RequestParam("companyId") Long companyId);

    /**
     * 获取留学保险提供商可绑定的应收计划
     * @param targetId
     * @param receiptFormId
     * @return
     */
    @GetMapping(GET_PLAN_IDS_BY_BUSINESS_PROVIDER_ID)
    Result<List<BaseSelectEntity>> getPlanIdsByBusinessProviderId(@RequestParam("targetId") Long targetId, @RequestParam("receiptFormId")Long receiptFormId);


    @PostMapping(GET_CONVENTION_REGISTRATION_BY_RECEIPT_CODE)
    Result<List<ConventionRegistrationVo>> getConventionRegistrationByReceiptCode(@RequestBody List<String> receiptCodeList );

    @GetMapping(GET_CONVENTION_BY_ID)
    Result<Convention> getConventionRegistrationById(@RequestParam("id") Long id);

    /**
     * 根据ids获取留学提供商名字map
     * @param ids
     * @return
     */
    @PostMapping(GET_INSURANCE_PROVIDER_NAME_BY_IDS)
    Result<Map<Long, String>> getInsuranceProviderNameByIds(@RequestBody Set<Long> ids);


    /**
     * 根据角色id获取角色信息
     */
    @PostMapping(GET_STUDENT_PROJECT_ROLE_BY_ROLE_IDS)
    List<StudentProjectRole> getStudentProjectRoleListByRoleIds(@RequestBody Set<Long> roleIds);

    /**
     * 根据id获取学生服务编号
     *
     * @param id
     * @return
     */
    @GetMapping(GET_STUDENT_SERVICE_FEE_NUM_BY_ID)
    Result<StudentServiceFeeSummaryVo> getServiceFeeInfoById(@RequestParam("id") Long id);

    @PostMapping(GET_STUDENT_SERVICE_FEE_BY_ID)
    Result<StudentServiceFeeVo> findServiceFeeById(Long fkTableId);

    @PostMapping(GET_BD_INFORMATION)
    Result<List<StaffBdCodeVo>> getBdInformation(@RequestBody SearchBean<StaffBdCodeDto> page );

    /**
     * 批量新增附件信息
     * @param mediaAttachedVos
     * @return
     */
    @PostMapping(ADD_MEDIA_AND_ATTACHED_LIST)
    Result<List<MediaAndAttachedVo>> addMediaAndAttachedList(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 复制partner学生附件
     * @param studentId
     * @param partnerStudentId
     * @return
     */
    @PostMapping(COPY_PARTNER_STUDENT_ATTACHED)
    Result<Boolean> copyPartnerStudentAttached(@RequestParam("studentId") Long studentId, @RequestParam("partnerStudentId") Long partnerStudentId);

}
