## Project Coding Guidelines

- A comprehensive set of coding standards and best practices for enterprise-level Java development
- Covers various aspects of software development, including logging, error handling, method documentation, enum class development, and code reuse principles
- Emphasizes consistency, maintainability, and adherence to established design patterns

## Key Focus Areas

- Detailed guidelines for logging, exception handling, and method commenting
- Strict rules for performance optimization in loop operations
- Comprehensive enum class development standards
- Principles of code reuse and modification
- International resource management
- Error handling and localization strategies

## Development Best Practices

- 每次使用方法或者调用之前, 先看看要调用的方法或者属性存不存在
- 禁止在循环中做sql处理

## Code Review and Standards Reminder

- Always prioritize understanding and following the established coding guidelines
- Review existing implementation before developing new features
- Maintain consistency with project's existing code style and architecture
- Focus on code readability, performance, and maintainability

## Additional Development Insights

- 代码开发和调用前，始终保持好奇心和探索精神
- 不要害怕阅读源代码和已有实现
- 提前预览和理解整个项目的技术架构和设计模式
- 尊重并学习现有代码的优秀实践

## Document Generation Guidelines

- 如果生成文档, 全部写在claude-data文件夹里面

## Project Structure Notes

- 这个claude-data 文件夹是已经存在的, 在/hti-java-ais-v1/claude-data底下, 不需要重新创建

## Additional Code Development Guidelines

- **代码风格规范**:
  - 如果他原本的代码写的很差(不是说逻辑, 是风格很差), 请不要学习他的风格, 并用企业级的要求写代码
  - 全程中文交流
  - 保持尊重原作者代码风格的原则
  - 只在明确要求时才修改原有代码风格

## 重要补充指南 - 关于代码开发和交流原则

- 始终保持尊重和专业的态度
- 如果原代码存在明显不符合企业级标准的问题，可以提出改进建议
- 在进行代码修改时，要权衡改动的必要性和影响范围
- 优先考虑通过添加新方法、新属性来扩展功能，而非直接修改现有实现
- 对于代码风格和格式，除非有明确要求，否则尊重原作者的编码风格
- 在code review和技术讨论中保持开放和谦虚的态度
- 重视沟通，用建设性的语言提出意见和建议

## Compilation and Development Guidelines

- 不允许在这个系统进行编译, 有错误我自己会跟你说