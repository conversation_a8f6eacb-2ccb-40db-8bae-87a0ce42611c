no_data_permission=No company authority
permission_contract_file=Employee contract information
permission_hrevent_file=Employee personnel event information
permission_staff_file=Staff information
permission_head_icon=Employee head
permission_company_icon=Company icon
institution_logo=school logo
institution_pic=School environment map
institution_mov=School introduction video
institution_mso_pdf=MSO-PDF
institution_cover=School cover
no_payable_plan_found=No payable plan found
institution_map_gg=Google Maps
institution_map_bd=baidu Maps
institution_provider_file=School provider information
institution_contract_file=School contract information
institution_country_emblem_pic=National Emblem
institution_country_flag_pic=National flag
institution_country_mso_banner=MSO-Banner
institution_faculty_pic=Institution environment map
institution_faculty_mov=Institution introduction video
institution_zone_pic=Campus environment map
institution_zone_mov=campus introduction video
institution_major_pic=Professional pic
institution_major_mov=Professional introduction video
institution_course_pic=Course Pic
institution_course_mov=Course introduction video
institution_course_file=Course information
institution_alumnus_pic=Picture of well-known alumni
institution_alumnus_mov=Introduction video of well-known alumni
institution_alumnus_file=Famous alumnus information
institution_info_pic=Institution Pic
institution_info_mov=Information introduction video
institution_info_file=Information attachment
institution_news_file=News file
institution_news_title_pic=News title picture
institution_news_cover=News cover image
institution_news_appendix=News attachment
institution_news_mail_appendix=Mail attachment
institution_country_info_description=Institution country information rich text
institution_country_info_pic=Institution country information pic
resume_icon=Resume avatar
resume_file=Resume information
finance_invoice_file=Invoice information
finance_provider_file=Supplier information
finance_receipt_file=Receipt information
finance_payment_file=Payment note information
finance_receipt_item_file=Receivable plan information
finance_payment_item_file=Payable plan information
REPEATED_PREPAYMENT=Prepaid or received money exists, and cannot be prepaid
PAYABLE_PLANS_CANNOT_GENERATE_COMMISSIONS=Payable plans cannot generate commissions.
NOT_PAY_ADVANCE_SETTLEMENT_INSTALLMENT=The prepayment has already been settled for financial commission and cannot be cancelled.
ACCOUNTS_PAYABLE_COMPLETED=The payment has been completed and prepayment cannot be set again.
NON_PREPAYABLE=The total prepayment amount is greater than the payable amount, please reduce the prepayment proportion
SALE_NUM_SETTLEMENT_BATCH_PAYMENT_FORM_EXIST=Payment document already exists for this batch number and cannot be returned
SALE_CANNOT_LOCK_REPEATEDLY=Cannot lock repeatedly
sale_settlement_in_progress=Associated data cannot be modified in a / P plan settlement
sale_convention_description=Summit rich text
sale_convention_procedure_description=Rich text of the summit process
sale_agent_file=Agent information
agent_business_license=Agent Business License
sale_agent_id_card_front=Identity Card National Microface
sale_agent_id_card_back=Image of identity witness
sale_contract_file=Agency contract information
sale_contract_seal_file=Contract Seal File
sale_student_file=Student profile
m_app_student_file=Partner student profile
sale_client_file=Client profile
sale_offer_item_file=Learning plan information
sale_payable_file=Payable plan information
sale_receivable_file=Receivable plan information
comment_comment=Comment file
sale_event_file=Event data
DEFER_ENTRANCE_TIME_NOT_EXISTS=The delayed enrollment time cannot be empty
PLAN_ONE_TO_ONE=Only one a / R plan can be bound to one a / P plan
student_accommodation_pic=Study abroad accommodation map
student_accommodation_mov=Study abroad accommodation introduction video
student_accommodation_file=Study abroad accommodation materials
student_insurance_pic=Study abroad insurance map
student_insurance_mov=Study abroad Insurance Introduction Video
student_insurance_file=Study abroad insurance information
d_list_export=list export
export_empty= No exportable data
1=processing
2=finished
0=failure
DEBIT_CARD=Debit Card
PASSBOOK=Pass Book
CREDIT_CARD=Credit Card
SEMI_CREDIT_CARD=Semi Credit Card
PREPAID_CARD_FEE=Prepaid card fee
OVERSEAS_CARD=Overseas card
m_student=student
m_institution_provider=institutionprovider
m_business_provider = business provider
institution_news_bulletin_board=MSO BULLETIN BOARD
geographical_division=GEOGRAPHICAL DIVISION
ROUTINE=Regular
u_area_country=country
u_area_state=state
m_institution=institution
m_institution_course=course
m_provider=provider
m_agent=Agent Name
GLB=Global
US=U.S.A
UK=britain
ANZ=ANZ
CAN=Canada
EUASIA=Eurasian
BLANK=No link required
URL=URL link
COMPONENT=Module link
NEWS_INFO=News information
COUNTRY_INFO=National Information
COUNTRY_ABOUT=Title of enrollment country
NEWS_ABOUT=Study abroad information title
NEWS_DETAILS_ID=News detail
submitted_media=Application submission completion voucher data
m_student_service_fee_invoice=Study abroad service fee invoice
m_student_service_fee_media=Information on service fees for studying abroad
m_student_service_fee_cost_media=Attached is the cost of study abroad service fee
m_insurance_order=Insurance Order
app_received_media=College received voucher data
admitted_media=Accepted and received offer voucher data
deposit_media=Deposit paid voucher data
cascoe_media=CAS / COE certificate data
visa_submitted_media=Submit visa completion certificate
VISA_SUBMITTED_MEDIA_MY_SG=Visa submission completed (Malaysia, Singapore)
visa_granted_media=Obtain visa certificate information
entrance_success_media=Enrollment completion certificate data
entrance_fail_media=Enrollment failure certificate data
notified_payment_media=Payment voucher information has been notified
registered_media=Registered certificate information
application_for_extension_media=Documents of application for extension
postponed_media=Extended certificate information
apply_for_deposit_refund_media=Application for deposit refund voucher information
tuition_media=Paid Tuition Voucher Information
offer_accepted_media=Offer accepted
missing_media=Lack of information
AWARD=Award
ONE_AWARD=One-time award
SPECIAL=special(give priority to match)
TRANSFER_AGENT_STUDENT=transfer agent student
PERCENTAGE_OF_SCHOLARSHIP_FEES = Scholarships account for over 60% of tuition fees
app_ucas_code=UCAS CODE
personal_statement=personal statement
admission_requirements=admission requirements
interview_preparation=interview preparation
cncc=Certificate of No Criminal Record
app_cricos_code=CRICOS CODE
app_program_code=PROGRAM CODE
IELTS=IELTS
TOEFL_IBT=TOEFL-IBT
TOEFL_PBT=TOEFL-PBT
PTE=PTE
HKDSE_ENG=HKDSE-ENG
CET=CET
DUOLINGO=DUOLINGO
BEC=BEC
LANGUAGE_CERT=LanguageCert
NCEE=National College Entrance Exam
HKDSE=HKDSE (Hong Kong)
ALEVEL=GCE A Level
IB=IB Diploma
MSS=Middle School Score
AP=Advanced Placement
UG=UG
CLASS=Class
GPA=GPA
GCE=GCE
GCSE=GCSE
GPA4=GPA(out of 4 points)
GPA4_2=GPA(out of 4.2, retain two decimal places)
GPA4_5=GPA(out of 4.5, retain two decimal places)
GPA5=GPA(out of 5 points)
GPA7=GPA(out of 7 points)
GPA9=GPA(out of 9, retain two decimal places)
PERCENTAGE=Percentage
GRADING=Grading
GU=GU
OTHER_REQUIREMENTS=other requirements
AGE=Age
WORK_EXP=Work Experience
ACCEPT_MAJOR_TRANSFER=Accept major transfer, recommended major background
OLEVEL=GCE O Level
OSSD=Ontario Secondary School Diploma
NCUK=NCUK
ATAR=ATAR
GAOKAO=GaoKao (China)
SAT1=SAT1 (USA)
SAT2=SAT2 (USA)
ACT=ACT (USA)
AD=Advanced Diploma (Sinfgapore)
STPM=STPM (Malaysia)
MUFY=Monash University Foundation year (Malaysia)
PM=Program Matrikulasi (Malaysia)
UEC=UEC (Malaysia)
ISCE=Indian School Certificate Examination (Malaysia)
DS=Diploma Studies (Mapaysia)
SPM=SPM(Mapaysia)
SMA3=SMA3 (Indonesia)
SMA2=SMA2 (Indonesia)
MATAYOM6=Matayom 6 (Thailand)
NE=National Exam (Vietnam)
GPA4_3=GPA(out of 4.3)
GPA10=GPA(out of 10 points)
GPA12=GPA(out of 12 points)
GRE=GRE
GMAT=GMAT
WC_INSTITUTION_CONSULT=School information applets
APP_FEE_BACHELOR=Bachelor
APP_FEE_MASTER_DEGREE=Master Degree
APP_FEE_COMMUNITY=Community
NUMBER_OF_STUDENTS=Number of students
FEE_OF_STUDENTS=fee
COURSE_DURATION_WEEK=Course length (weeks)
COURSE_DURATION_MONTH=Course length (month)
COURSE_DURATION_YEAR=Course length (year)
COURSE_DURATION_SEMESTER=Course length (semester)
STUDENT_OFFER_ITEM_COURSE_DURATION_WEEK=weeks
STUDENT_OFFER_ITEM_COURSE_DURATION_MONTH=month
STUDENT_OFFER_ITEM_COURSE_DURATION_YEAR=year
STUDENT_OFFER_ITEM_COURSE_DURATION_SEMESTER=semester
ACCUMULATED_TUITION=accumulated tuition
NOT_PUBLIC=Not public
PUBLIC=Public
PUBLIC_STUDENTS=Students
PUBLIC_AGENT=Agent
PUBLIC_MSO_CN=MSO_Mainland China
PUBLIC_MSO_HK=MSO_Hong Kong
PUBLIC_MSO_TW=MSO_Taiwan
PUBLIC_HKISO=HKISO
PUBLIC_IBS=IBS
PUBLIC_ISSUE=ISSUE
PUBLIC_CURRENCY_COMMON=Common currency
PUBLIC_COUNTRY_HOME=Country home page
PUBLIC_CURRENCY_ACC=Study abroad cabin
MPS=MPS
PUBLIC_COUNTRY_COMMON=Country Commonly used
SYSTEM_CONFIGURATION=System configuration
BUSINESS_CONFIGURATION=Business configuration
WORLD_RANKINGS_QS=QS World Rankings
WORLD_RANKINGS_TIMES=TIMES World Rankings
ASIA_RANKINGS_QS=QS Asia ranking
ASIA_RANKINGS_TIMES=Times Asia ranking
INTERNATIONAL_RANKINGS_QS=QS most international ranking
INTERNATIONAL_RANKINGS_TIMES=Times most international ranking
COUNTRY_RANKINGS=Country Ranking
CUG_RANKINGS=CUG ranking
REF_RANKINGS=REF ranking
FACULTY_STUDENT_RATIO=Teacher-student ratio
SEX_RATIO=Male to female ratio
PERCENTAGE_OF_INTERNATIONAL_STUDENTS=Percentage of international students
PERCENTAGE_OF_CHINESE_STUDENTS=Percentage of Chinese students
STUDENTS_NUM=Number of students
VIDEO_WEBSITE_01=Video URL
ACCEPTANCE_RATE=acceptance rate
ENROLLMENT=enrollment
INFO_SCHOOL_ADVANTAGE=School advantages
INFO_IB_AP_OSSD_AL=Fractional system
INFO_CURRICULUM_JUNIOR=Length of schooling_ primary
INFO_CURRICULUM_SENIOR=Length of schooling_ senior
INFO_CHINESE_FONT=Chinese font
INFO_TUITION_FEE_HIGHEST=Maximum tuition
INFO_CLASS_STRUCTURE=Class structure
INFO_GENDER=Boys' school and girls' school
RELIGION=RELIGION
TEL=TEL
FAX=FAX
SPECIFIC=SPECIFIC
LIVING_EXPENSES=Living Expenses
CLOTHING_EXPENSES=Clothing expenses
CATERING_EXPENSES=Catering expenses
ACCOMMODATION_EXPENSES=Accommodation expenses
TRANSPORTATION_EXPENSES=Transportation costs
TEACHING_LANGUAGE=Instructional language
SET_UP_NATURE=Set up nature
INFO_IB_AVERAGE=Average IB
INFO_IB_40=IB 40+%
INFO_AP_AVERAGE=Average AP
INFO_AP_4=AP 4+%
INFO_A_A=A*/A%
INFO_A_B=A*/B%
INFO_TOP_20_RATE=Top 20U admission rate
INFO_APPLICATION_DEADLINE=Registration deadline
INFO_ONLINE_APPLICATION=Online application
INFO_APPLICATION_FEE=Registration fee
INFO_APPLICATION_RENEWAL_FEE=Application renewal fee
INFO_ASSESSMENT_FEE=Assessment fee
INFO_CAPITAL_LEVY=Capital tax
INFO_DEPRECIATING_DEBENTURES=Depreciation bond
INFO_DEPRECIATED_BY=depreciation
CAREER_PATH=CareerPath
LIVING_EXPENSES_STUDYING_ABROAD=LivingExpensesStudyingAbroad
TOTLE_EXPENSES_STUDYING_ABROAD=TotleExpensesStudyingAbroad
LIFE_SAFETY=LifeSafety
COUNTRY_RANKING=Country Ranking
WORLD_RANKING=World Ranking
RENEW=Continue to sign
NEWSIGNING=New Siging
SMALL_RECOMMENDATION=Small Recommendation
MEDIUM_RECOMMENDATION=Medium recommendation
BIG_RECOMMENDATION=Big recommendation
GET_THE_FIRST_STAGE_COMMISSION=Get the first stage Commission
GET_THE_SECOND_STAGE_COMMISSION=Get the second stage Commission
GET_THE_THIRD_STAGE_COMMISSION=Get the third stage Commission
GET_THE_FOURTH_STAGE_COMMISSION=Get the fourth stage Commission
READ_ONLY_PHASE_ONE=Read only phase 1
READ_ONLY_PHASE_TWO=Read only phase 2
READ_ONLY_PHASE_THREE=Read only phase 3
READ_ONLY_PHASE_FOUR=Read only phase 4
ACADEMIC_REQ=academic req
ENGLISH_REQ=english req
SOPHOMORE_ACADEMIC_REQ=sophomore academic req
SOPHOMORE_ENGLISH_REQ=sophomore english req
SPECIFIC_SUBJECT_REQ=specific subject req
UN_DECIDED=Undetermined
FACE_TO_FACE=Face to face
ONLINE_CLASS=Online class
BUY_SCHOOL_INSURANCE=buy school insurance
BUY_OTHER_INSURANCE_OTHER=other
PAYMENT_BY_FEI_HUI=Feihui
PAYMENT_BY_YI_SI_HUI=Yisihui
PAYMENT_BY_ALIPAY=Alipay
PAYMENT_BY_BANK=bank
PAYMENT_BY_CREDIT_CARD=card
PAYMENT_BY_OTHER_REMITTANCE_PLATFORM=Other remittance platforms
PAYMENT_BY_WECHAT=WeChat
PAYMENT_BY_INTERAC=Interac e-Transfer
BUY_HTI_INSURANCE=buying insurance through Hti
BUY_OTHER_INSURANCE=buying insurance through other institutions
EVENT_SEARCH_BY=Event search by
SEARCH_BY_TARGET_COUNTRY=target country
SEARCH_BY_STATE=state hold
SEARCH_BY_CITY=city hold
SEARCH_BY_STAFF=staff
SEARCH_BY_STAFF2=second staff
SEARCH_BY_EVENT_TYPE=event type
staff_bind_superior_error=The employee is already associated with a business subordinate and cannot be bound as a business boss
supervisor_set_error=You or your subordinates cannot be set as direct supervisors
app_course_website=Course details website
app_course_req_website=Course requirements website
scholarship_info=Financial Aid Statistics
scholarship_website=Scholarship website
BIRTHDAY=Birthday
EMAIL=Email
MOBILE=Mobile
PASSPOST=Passpost
STUDENT_EXIST=Already Existed
STUDENT_AGENT_IS=The Student Is Currently Bound To The Agent
STUDENT_APPLICATION_STATUS_TOP=Current highest application status
IS_EXIST_STUDENT=[View student details] -- Click to pop up a new TAB to view information
#Selling Centre
STUDENT_NOT_QUALIFIED=The student does not meet the requirements
EXISTENCE_LABEL=For the same target object, there cannot be multiple labels.
COURSE_MAJOR_LEVEL_IS_NULL=Course level cannot be empty,Please contact the administrator.
COURSE_LEVEL_TYPE_IS_MISSING=The selected courses do not define the course level or type. Please provide the details and then submit again.
COURSE_TYPE_IS_NULL=Course type cannot be empty,Please contact the administrator.
REPEATED_PHONE_NUMBER=This phone number has already been registered, please do not register again.
RECEIVABLE_AMOUNT_NOT_ZERO=The receivable amount cannot be 0
EXISTS_ITEM_FINANCIAL_DATA=The application plan has generated financial data. Please contact the finance department for assistance.
NON_REVIEWER=In the application review process, you are not the reviewer and cannot click pass
contact_person_mobile=contact mobile phone
contact_person_tel=Contact landline
contact_person_email=Contact Email
bank_account_name=Account name
bank_account_num=Account num
agent_file_download_failed=[{0} Agent] failed to export the file. The agent operation is suspended.
agent_file_download_idcard_failed=[{0} Agent] The ID card is missing and the export file failed. The agent operation is paused.
agent_file_download_license_failed=[{0} Agent] The business license information is missing, the export file failed, and the agent operation is suspended.
PAYABLE_PLAN_SETTLEMENT_MARK_NOT_EXISTS=A/P plan settlement flag does not exist
PRIMARY_AGENT_SETTLEMENT_PORT_NOT_EXISTS=The primary agent must be a settlement outlet
INVOICE_PREPAYMENT_SETTLEMENT_IN_PROGRESS=The prepayment has already been settled through financial commission, and the invoice binding amount cannot be modified.
PREPAYMENT_SETTLEMENT_IN_PROGRESS=Prepayment cannot be made again in the existing prepayment settlement
PAYABLE_PLAN_PROGRESS=The A / P plan is being settled or has been settled. It cannot be modified
SETTLEMENT_BANK_ACCOUNT_IS_NULL=The settlement bank account number cannot be blank.
STATUS_SETTLEMENT_ABNORMAL=Abnormal settlement status of a / P plan
STUDENT_OFFER_ITEM_PROGRESS=The learning plan is being settled and cannot be modified
plan_receipt_form_item_exists=The A / R plan bound to the learning plan has a binding relationship with the collection document :{0}, so the A / R and a / P plan cannot be regenerated
plan_payment_form_item_exists=The A / P plan bound to the learning plan has a binding relationship with the payment document :{0}, so the A / R and a / P plan cannot be regenerated
receipt_form_balance_not_enough=The centralized expense amount is greater than the binding balance of the collection document
agent_followAgent_data_association=This agent has subordinate agents
agent_contract_data_association=There is data association in the contract under this agent
agent_event_data_association=There is a data association for the event under this agent
agent_student_data_association=Students under this agent have data associations
agent_convention_data_association=There is a data association for the summit under this agent
agent_contractFormula_data_association=There is data association in the contract formula under this agent
student_contactPerson_data_association=The student has a data association with the contact
student_event_data_association=The student has a data association with the student event
student_offer_data_association=The student has a data association with the application plan
client_contactPerson_data_association=The client has a data association with the contact
client_event_data_association=The client has a data association with the student event
client_offer_data_association=The client has a data association with the application plan
offer_offerItem_data_association=The offer is data-associated with the learning plan
step_offerItem_data_association=This step has a data association with the learning plan
contract_contractAccount_data_association=There is a data association between the contract and the contract account
hotel_hotelRoom_data_association=There is a data association between the hotel room type and the opened room
hotelRoom_person_data_association=The room has a data association with the person
registration_table_data_association=There is a data association between registration for the summit and the training table
person_hotelRoom_data_association=The participant has been assigned a room
person_table_data_association=The participant has been assigned a table
person_procedure_data_association=The participant has been assigned a process
procedure_person_data_association=There are already personnel involved in this process
convention_person_data_association=There are already participants in this summit
convention_table_data_association=There are already tables for this summit
convention_hotel_data_association=The summit has hotel room types
convention_procedure_data_association=This summit has a summit process
convention_registration_data_association=The summit has already registered for the summit
table_registration_data_association=This table has a booth configured
table_person_data_association=The table has staff assigned
item_payable_data_association=The learning plan is associated with the A / P plan
offer_financial_association=This scheme has associated financial data and cannot be voided
insurance_payable_data_association=The learning plan is bound to the A/P plan. Please void the A/P plan first
item_receivable_data_association=The learning plan is associated with the A / R plan
event_cost_data_association=There is a cost association for this event
event_registration_data_association=There is registration roster data for this activity
eventType_event_data_association=The event type has a data association with the event
staff_agent_data_association=The role has a data association with the agent
student_offer_project_role_data_association=There is a data association between this role and the learning plan
staff_agent_project_role_data_association=This role has data association with employees and agents
table_countDown=The seats are full, the number of seats cannot be reduced
edit_companyId=Configure at least one company
agent_not_configure_company=The selected company is not configured for the current agent
offerItem_id_null=The item Id of the application proposal cannot be empty
offer_id_null=Student item Id cannot be empty
student_id_null=Student Id cannot be empty
agent_id_null=Agent Id cannot be empty
bd_agent_company_inconsistency=The agency's company is inconsistent with BD's company
bd_agent_company_data_association=The company of the agent now includes the company of the bound BD agent. Modifying the company of the agent failed. To operate, first unbind BD, and then adjust the company of the agent.
bd_agent_exit=The same BD already exists
company_id_null=The company id cannot be empty
event_id_null=The event id cannot be empty
seting_enrol_Failure_unsuccessful = The current learning plan is a sub-plan and it is set to follow the parent plan. It cannot be operated. Please operate its corresponding parent plan.
step_no_reverse=The step sequence is irreversible
budget_amount_null=End of activity setting: budget amount; required
actual_amount_null=At the end of setting activity: actual amount; required
attended_count_null=at the end of setting activity: number of participants; required
remark_null=When setting activity end: activity evaluation; required
attended_count_actual_amount_budget_amount_remark_null=At the end of the activity: budget amount, actual amount, number of participants and activity evaluation must be filled in
attended_count_actual_amount_budget_amount_null=At the end of the activity: budget amount, actual amount and number of participants must be filled in
staff_bd_null=Failed to create agent. The currently logged in user has no BD role configured
role_bound_exits=The project members have been bound
agent_bound_exits=This agent has been bound
required_bd=At least one BD needs to be bound and cannot be unbound
required_agentId=At least one agent is bound
person_arrange_table=The person has been arranged table
table_full=The table is full
required_rate_or_amount =commissionRateAg/receivableRateAg/fixedAmountAg, fill in at least one
table_arrange_refresh=The table has been arranged, please refresh to view
room_num_stay_day=Please fill in the number of rooms and staying days
ROOM_IS_FULL=Kind reminder, this room type is full. We suggest you choose another room type
contract_formulas_null=Step setting failed! Unable to find the school supplier contract configuration, please contact the contract administrator.
invalidation_of_agency_contract=The contract signed by the selected agent has expired and cannot be imported. Expired agent:{0}
missing_agent_data=Please complete the data required by the prompt and then import the document.
student_condition_type_null=Step setting failed!Invalid business scenarios, please contact the contract administrator.
receivable_amount_error=Step setting failed! The system calculates that the receivable and payable amount is 0, please contact the contract administrator.
search_offer_null=Learning application scheme not found
student_info_null=Student information not found
rate_null=exchange rate not found
agent_contract_currency_type_no_match=The agent contract formula does not match the currency of the learning plan. Please contact the contract administrator.
agent_account_exist=Only one account in the same currency can be activated.
agent_account_contract_data_association=There are data associations in the contract of this account
agent_account_settlement_data_association=There is data association between this account and commission settlement
agent_account_contract_currency_exist=Only one account can be bound to the same currency.
student_offer_item_failure_abnormal_state=Failure record status exception.
hotelRoomNum_exists=The hotel Room Number exists,please contact the administrator to change.
staff_or_bacode_exists=The employee name or BD number already exists
hotel_room_num_has_been_updated=The hotel room number has been updated.
parent_agent_bind_error=Unable to bind itself as the parent agent
agent_account_is_default_exist=The same agent can only have one preferred account
BUSINESS_PROVIDER_DATA_ASSOCIATION=This service provider has business data and cannot be directly deleted.
REPEATED_PUSH_EMAILS=Please repeat the push, please try again later
EVENT_PLAN=Plan
EVENT_END=End
EVENT_CANCEL=Cancel
EVENT_POSTPONE=Postponed
agent_bind_parent_error=The agent is already associated with the sub-agent and cannot be bound as the parent agent
INSTITUTION_AGENT=Institution agent
AGENT=Agent
GUEST=Guest
STAFF=Staff
WORKING_PERSON=Working person
person_has_configured_bed=The person has been arranged
CYCLING_REGISTRATION_ID_TYPE=ID card
CYCLING_REGISTRATION_PASSPORT_TYPE=passport
CYCLING_REGISTRATION_PERMIT_TYPE=Hong Kong and Macao return permit
CYCLING_REGISTRATION_TAIWANESE_TYPE=Taiwanese syndrome
CYCLING_REGISTRATION_FAILURE_PAY_STATUS=failure
CYCLING_REGISTRATION_SUCCESS_PAY_STATUS=succeed
m_student_offer=student offer
m_student_offer_item=student offer item
m_student_insurance=study abroad insurance
m_student_accommodation=accommodation
m_student_service_fee_cost=fee cost
client_source=client source
m_student_service_fee = service fee for studying abroad
m_business_channel_ins=study abroad insurance channel
m_business_provider_ins=Study Abroad Insurance Provider
m_business_channel_acc=accommodation channel
m_business_provider_acc=Study Abroad Accommodation Provider
m_company = company collection

# Partner user registration related exception messages
partner_user_register_process_failed=Partner user registration process failed, Agent ID: {0}
partner_user_register_and_email_failed=Partner user registration and email sending failed, Agent ID: {0}
contact_persons_empty=Contact persons list is empty, Agent ID: {0}
build_register_dto_params_null=Parameters for building registration DTO cannot be null
bd_staff_null=BD staff information is null, Agent ID: {0}
app_agent_null=Agent application information is null, Agent ID: {0}
target_person_type_null=Target person type is null, Agent ID: {0}
bd_staff_email_incomplete=BD staff email information is incomplete, Agent ID: {0}
duplicate_emails_found=Duplicate email addresses found, Agent ID: {0}, Duplicate emails: {1}
contact_person_role_key_empty=Contact person role key cannot be empty
LACK_OF_INFORMATION=lack of information
NOT_OPEN= not open
NOT_MARKED=not marked
m_event_bill=event bill
SALE_STUDENT_OFFER=Study abroad application
SALE_STUDENT_OFFER_ITEM=Study abroad application
SALE_STUDENT_ACCOMMODATION=Study abroad accommodation
SALE_STUDENT_INSURANCE=Study abroad insurance
SALE_STUDENT_SERVICE_FEE=Service fee for studying abroad
COMPANY_COLLECTION=company collection
N=Waiting to run
T=In operation
C=Completed
E=error
K=Manual error, no processing required
S=Manual error, continue to check
m_agent_contract=Agent Ref.
STUDENT_OFFER_CLOSE_STATUS=Close
STUDENT_OFFER_OPEN_STATUS=Open
STUDENT_OFFER_END_STATUS=Termination
STUDENT_OFFER_FINISH_STATUS=Successful closure
end_time_not_null=End time cannot be empty
student_offer_item_has_been_parent=Parent student offer item is already a sub student offer item
duplicate_proxy=Repeated agents?
Bound_BD=Binding BD?
IS_EXIST_AGENT=Name Already Exists
AGENT_STAFF=The Current Proxy Binding Is
PROPERTIES_FOR=Properties For
PERSON=Person
COMPANY=Company
WORKROOM=Work Room
OTHER=Other
AGENT_ACCOUNT_NAME=Agent Account Name
AGENT_ACCOUNT_NO=Agent Account No
AGENT_NAME=Agent Name
CONTACT_PHONE=Contact Phone
CONTACT_EMAIL=Contact Email
CONTACT_TEL=Contact TEL
NAME_OF_A_LEGAL_PERSON=Name Of A Legal Person
INTERNATIONAL_SCHOOL=International School
TAX_NUMBER_OT_THE_COMPANY=Tax Number Of The Company
IS_EXIST_AGENT_MSG=[View agent details] -- Click to pop up a new TAB to view the information
A_R_AND_A_P_NOT_CREATED=A_R_AND_A_P_NOT_CREATED
A_R_NOT_CREATED=A_R_NOT_CREATED
A_P_NOT_CREATED=A_P_NOT_CREATED
A_R_CREATED=A_R_CREATED
A_P_CREATED=A_P_CREATED
A_R_AND_A_P_CREATED=A_R_AND_A_P_CREATED
NO_COMMISSION=NO_COMMISSION
student_offer__is_not_close=The student offer item or its sub-plan bound to the student offer has already created an receivable or payable  plan, and it fails to be invalidated
student_offer_item_is_not_close=This student offer item or its sub-student offer item has been created with receivable or payable plan, and the invalidation failed
receivable_plan_is_not_close=The receivable plan has been bound to the receipt form, and the invalidation failed
receivable_plan_is_null=The receivable plan is empty
receivable_plan_not_close_pay_plan = Void failed, payable plan does not exist, or has been paid or entered into settlement.
is_not_back=This student offer item or its sub-student offer item has been created with receivable or payable plan, no step back is allowed
no_fallback = The application has been confirmed and cannot be returned
add_student_insurance_error=The student did not fill in the passport information, and the new insurance failed
match_plan_null=The receivable plan or the payable plan must choose one of them to fill in the information
fixed_amount_cannot_zero=The fixed amount cannot be zero
adjust_search=The exported data is larger than {0}. Please adjust the search criteria and export again
other=Other income
REWARD_MONEY=Reward amount
INCENTIVE_REWARD=Incentive rewards
INSURANCE_REWARD=Insurance amount
EVENT_COST=Activity cost
OTHER_INCOME=Other income
FAM_TRIP=Fam Trip
SETTLEMENT_NOT_DONE=Processing
SETTLEMENT_FINISHED=Finished
AP_STATUS_UNPAID=Unpaid
AP_STATUS_PARTIALLY_PAID=Partially paid
AP_STATUS_PAID_UP=Paid up
AP_STATUS_UNPAID_CREATED_PAYABLE_PLAN=Unpaid [Accounts Payable Plan Created]
AR_STATUS_NOT_RECEIVED=Not received
AR_STATUS_PARTIALLY_RECEIVED=Partially received
AR_STATUS_RECEIVED=Received
GET_MSO=MSO
GET_ISSUE=ISSUE
GET_ISSUE_OLD=ISSUE_OLD
GET_IB=IBS
GET_HKISO=HKISO
GET_BMS=BMS
SALE_STUDENT=student
m_institution_channel=institution channel
INSTITUTION_PROVIDER=institution provider
BUSINESS_CHANNEL_INS=study abroad insurance channel
BUSINESS_PROVIDER_INS=Study Abroad Insurance Provider
BUSINESS_CHANNEL_ACC=accommodation channel
INSTITUTION_PROVIDER_CHANNEL=institution channel
NUMBERING=Numbering
enroll_failure=Application Plan: School Name: {0}, Course Name: {1}, Actual Receipt Record Exists, Cannot Set Enrollment Failure
sale_event_bill_file=Event bill attachment
sale_business_provider_agreement=Business provider agreement
sale_business_provider_agreement_file=Business provider agreement file
convention_gopro_nucleic_acid_file=GoPro nucleic acid file
GOPRO_HEADQUARTERS=Headquarters
GOPRO_SOUTH_CHINA=South China
GOPRO_EAST_CHINA=East China
GOPRO_CENTRAL_CHINA=Central China
GOPRO_SOUTHWEST_CHINA=Southwest China
GOPRO_WEST_CHINA=West China
GOPRO_NORTHWEST_CHINA=Northwest China
GOPRO_NORTH_CHINA=North China
plan_and_invoice_has_generated=Receivable plan and invoice have been generated and cannot be created repeatedly
event_bill_has_bound=Failed to void the activity expense initiation plan. Some activities have been bound. Please delete the bound activities first
event_bill_has_bound_plan_and_invoice=Failed to void activity expense initiation plan. Receivable plan and invoice have been created
event_bill_has_bound_receipt_form=Failed to void activity expense initiation plan. The collection business record already exists
financial_documents_update_status_fail=Failed to void the financial document, the receipt business record already exists
EB_STATUS_UNASSIGNED=Unassigned
EB_STATUS_ASSIGNED=Assigned
EB_STATUS_COMPLETE_ASSIGNMENT=Complete assignment
ACC_INACTIVE=Inactive
ACC_ACTIVE=Active
ACC_SUCCESS=Success
ACC_POSTPONE=Postpone
APPROVAL_FAILURE=Failure
event_not_bind=The summit is not bound to an activity. Please bind the corresponding activity first
convention_and_event_exist_binding=The enrollment roster or sponsor roster has been bound to the activity fee and cannot be disassociated
add_convention_and_event_exist_binding=The target activity already has an association and cannot be bound
convention_sponsor_sponsor_fee_association=The sponsorship fee type of the summit is related to the sponsor
currency_not_same=The project fee and other fees are in different currencies and cannot be submitted
sale_event_incentive_file=Rewards Promotion Information
incentive_policy_file=Rewards Information
event_bill_has_bound_event_incentive=Failed to void the activity expense initiation plan. Some incentive activities have been bound. Please delete the bound activities first
only_has_one_account=Only one account can be created for each currency
only_has_one_default=Only one account can be set as primary
app_agent_contract_need_attached=Missing attachment
APP_STATUS_NEW=Pending approval
APP_STATUS_REVIEW=Review
APP_STATUS_AGREE=Agree
APP_STATUS_REJECT=Reject
AGENT_NATURE_COMPANY=Company
AGENT_NATURE_PERSON=Personal
AGENT_NATURE_STUDIO=Studio
AGENT_NATURE_INTERNATIONAL_SCHOOL=International School
AGENT_NATURE_OTHER=Other
AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY=Personal account company
STATISTICS_IN_PROGRESS = The new report data is being counted, please wait until the completion of the new statistical operation
login_id_ne_start_id = The logged in user is not the originator!
can_not_set_participate_or_not_participate=For delayed activities, the registration list cannot be set [Participate] or [Not participate]
has_not_commission_record=No billable record!
CONVENTION_STAFF_PROHIBIT=Forbidden
CONVENTION_STAFF_ALLOW=Allowed
not_new_app_status=The application plan is no longer in the New App status, please confirm the course!
not_new_app_item_follow_status=The subsequent courses for this application plan are empty. Please confirm the courses!
PUSH = Push
DIRECT = Direct
export_data_error=Export data error!
sign_encryption_failed=sign encryption failed!
Reach_the_limit=The number of applicants reached the maximum
CREATE_COUNT_SORT_TYPE=create count sort type
CONFIRMATION_COUNT_BY_STUDENT_SORT_TYPE=confirmation count by student sort type
SUCCESS_COUNT_BY_STUDENT_SORT_TYPE=success count by student sort type
repeat_email=Duplicate email address, please check and re-enter!
MAIL_STATUS_NOT_SEND=Not Send
approve_comment_null=Approval comment cannot be empty
agent_app_data_error=Agent application data not found
MAIL_STATUS_SUCCESS=Succeeded
MAIL_STATUS_FAIL=Fail
MAIL_STATUS_REJECTION=rejection
REVIEW_STATUS_NOT_REVIEW=Unaudited
REVIEW_STATUS_REVIEW=Audited
REVIEW_STATUS_DIFFERENTIAL=Difference in provision
different_theme = Can't move between different themes
EVENT_PLAN_THEME_ONLINE = event plan theme online
EVENT_PLAN_THEME_OFFLINE = event plan theme offline
EVENT_PLAN_THEME_WORKSHOP = event plan theme workshop
delete_offline_item_fail = delete event plan theme offline item fail,the registration list exists
delete_online_fail = delete event plan theme online fail,the registration list exists
delete_workshop_fail = delete event plan theme workshop fail,the registration list exists
apply_for_overtime_fail = The submission time is during normal working hours or overtime hours of less than 2 hours
#apply_for_overtime_fail = Overtime hours must be greater than 2 hours before you can apply
delete_fail_group_item_exist = delete kpi plan group fail,the kpi plan group item exists
delete_fail_plan_group_exist = delete kpi plan fail,the kpi plan group exists
kpi_plan_statistics_excel_export_fail = KPI scheme statistical export failed
BD = BD
STUDENT_OFFER_STAFF = student offer staff
CPM = PM
PERSONAGE = personage
TEAM = team
update_obj_null = Update object is empty
update_fail_target_group_not_exist = Update failed,the target group does not exist
can_not_add_oneself_to_team = Can't add yourself to the form of a team
receipt_form_item_exist_cancel_fail = The receipt already exists and cannot be cancelled
pay_form_item_exist_cancel_fail = The payment already exists and cannot be cancelled
associated_event_id = The associated activity does not exist
FILL_IN_STUDENT_EDUCATION_BACKGROUND=Please fill in the student's domestic or international learning background information, and if there are grades, please fill them in together!
DATE_FORMAT_CONVERSION_ERROR=Date format conversion error
ILLEGAL_OPERATION_PLAN_STUDENT_MISMATCH=Illegal operation, the plan does not match the student
DUPLICATE_APPLICATION_PLAN_SUBMIT_TOO_FREQUENT=The same application plan has already been created and submitted too frequently. Please try again later.
ALREADY_FOLLOWING_PARENT_PLAN=Following my father's plan is already
STATUS_FILL_IN_OFFER_STUDENT_ID=Status, please fill in the student ID (Offer Student ID)
PLEASE_SELECT_PROVIDER_AND_CHANNEL=Please select supplier and channel
ILLEGAL_FILE_TYPE=Illegal type file
SIGN_PARSE_FAILED=Sign parsing failed!
NON_COMPANY_AGENT_CANNOT_UPLOAD_LICENSE=Non corporate agents cannot upload their business license

#Permission Center
staff_followStaff_data_association=The employee's business subordinates have data associations
staff_country_data_association=There is a data association for the employee's business country
staff_office_data_association=The staff office has data association
staff_contract_data_association=The employee's labor contract has data association
staff_event_data_association=The employee's personnel record has data association
staff_supervisor_data_association=The employee's personnel record has data association
staff_config_data_association=The employee has data association with the configuration
staff_resource_data_association=The employee has data association with the resource
staff_group_grade_data_association=The employee has data association with permission group level
company_followCompany_data_association=There are subordinate companies under this company
company_department_data_association=There are data associations in the department of this company
company_position_data_association=The company position has data association
company_office_data_association=Data association exists in the office of this company
company_staff_data_association=There is data association for employees in this company
company_permissionGrade_data_association=There is a data association under the permission level of this company
company_permissionGroup_data_association=Data association exists in the permission group under this company
department_position_data_association=Data association exists for the position in this department
department_staff_data_association=There is data association for employees in this department
position_staff_data_association=The employee under this position has data association
office_staffOffice_data_association=There is a data association between this office and the employee business office
office_staff_data_association=The office has data associations with employees
group_resource_data_association=This group has data association with permissions
group_staff_data_association=This group has data association with employees
grade_resource_data_association=This level has data association with permissions
grade_staff_data_association=This grade has data associations with employees
grade_null=The permission level is empty
group_null=The permission group is empty
topCompany_exist=Top head office already exists
staff_id_null=The query staff ID cannot be empty
department_id_null=The query department id cannot be empty
position_id_null=The query position id cannot be empty
parent_id_null=parentId cannot be empty
user_no_active=Account is not activated
agent_no_active=Agent is not activated
old_pwd_incorrect=Old password is incorrect
pwd_check_msg=Please fill in a password containing uppercase letters, lowercase letters, numbers and special characters, 8-12 digits
superior_bind_error=The business boss cannot bind himself
CHINA=Chinese Mainland +86
HONG_KONG=Hong Kong(China) +852
TAI_WAN=Taiwan(China) +886
VIETNAM=Vietnam +84
INDONESIA=Indonesia +62
MALAYSIA=Malaysia +60
LOCAL_NON_AGRICULTURAL_ACCOUNT=Local non-agricultural account
LOCAL_AGRICULTURAL_ACCOUNT=Local agricultural account
NON_AGRICULTURAL_ACCOUNT_IN_OTHER_PLACES=Non agricultural household registration in other places
REMOTE_AGRICULTURAL_ACCOUNT=Remote agricultural account
HONGKONG_MACAO_TAIWAN=Hong Kong, Macao and Taiwan
FOREIGN=foreign
IDENTITY_CARD=identity card
PASSPORT=passport
PASS=pass
RETURN_PERMIT=return permit
THREE_ONE=3+2
TWO_TWO=2+2
FOUR_ZERO=4+0
EXCHANGE_STUDENTS=exchange students
DOUBLE_DEGREE=receive double degree
INTERNATIONAL_DEGREE=receive international degree
DOMESTIC_DEGREE=receive domestic degree
event_bill_is_not_close=The event bill has been bound to the receipt form, voiding failed
event_currency_not_same=The currency of the activity for which the expenses are centralized is different from the currency of the reward promotion activity and cannot be bound.
event_incentive_currency_not_same=The currency is different from the central currency of the fee and cannot be bound
source_permission_group_grade_null = The original website resource is empty
target_permission_group_grade_null = The target network has already configured resources and cannot be overwritten. Please delete the existing configuration before proceeding.
staff_email_exist=Failed to set [{0}] email address, {1} employee has already set the email address.
cannot_delete_message=Only resigned employees can delete all mail settings
simulated_login_exception=Simulated login exception
not_configured_yet_userName_key_or_value=Not configured yet userName key or value
not_configured_yet_password_key_or_value=Not configured yet password key or value
get_page_exception=Get page exception
email_statistics_null=Email statistics are empty
default_config_not_found = default_config_not_found
MISSING_RELATED_BUSINESS_CONFIGURATION=Lack of relevant business configuration
PLEASE_UPLOAD_CORRESPONDING_NUMBER_OF_IMAGES_FOR_COMPARISON=Please upload the corresponding number of images for comparison
FACE_MATCH_URL_NOT_CONFIGURED=Facial matching URL address has not been configured yet, please contact the administrator
FAILED_TO_GET_PMP_REVIEW_LIST=Failed to obtain PMP audit list

#School center
institution_name_exists=institution name already exists
course_name_exists=course name already exists
institution_faculty_data_association=There is a data association between the school and the college
institution_provider_data_association=The institution has a data association with the provider
institution_contactPerson_data_association=There is a data association between the school and the contact
institution_news_data_association=The institution has a data association with news
institution_character_data_association=The institution has a data association with the characteristic
countryInfoType_countryInfo_data_association=The country info type has been used
country_countryInfo_data_association=There is a data association between this country and country information
country_news_data_association=This country has data associations with news
cityInfoType_cityInfo_data_association=The city info type has been used
city_cityInfo_data_association=There is a data association between this city and city information
city_cityDivision_data_association=There is a data association between this city and city division
institution_pathway_data_association=An existing school is bound to this bridge school, and the type cannot be modified
greater_than_the_company_range_of_the_supplier=The configuration company range cannot be greater than the company range of the supplier
missing_maximum_or_minimum=Missing maximum or minimum value of statistical target
commission_or_amount_fill_least_one=Fill in at least one Commission / amount
commission_or_refund_fill_least_one=Please fill in at least one refund
Contract_type_name_exists=Contract type name already exists
course_contract_data_association=There is a data association between the course and the contract
course_coursemajorlevel_data_association=There is a data association between the course and the course level
course_coursetype_data_association=The course has data association with the course type
course_coursezone_data_association=There is data association between the course and the campus
course_faculty_data_association=The course is linked to college data
course_formulains_data_association=There is data association between the course and formula
courseType_course_data_association=There is data association between the course type and the course
courseType_courseTypeGroup_data_association=There is data association between the course type and the course type group
courseTypeGroup_courseType_data_association=There is data association between the course type group and the course type
courseId_null=courseId is null
bridge_courseId_null=Bridge course ID cannot be empty
faculty_course_data_association=There is data association between the college and the course
code_or_name_exists=The number or name already exists
name_exists=name exists
code_exists=code exists
bridge_school_cannot_bound_bridge_school=Bridge school cannot be bound to bridge school
bridge_schoolId_empty=Bridge school ID cannot be empty
providerId_null=Provider ID cannot be empty
provider_type_name_or_key_exists=School provider type name or type key already exists
provider_institution_data_association=The provider has a data association with the institution
institution_contract_data_association=The institution has a data association with the contract
institution_contractformula_data_association=The institution has a data association with the contractformula
provider_contact_person_data_association=The provider has a data association with the contact person
provider_contract_data_association=The provider has a data association with the contract
provider_contract_formula_data_association=The provider has a data association with the contract formula
provider_news_data_association=The provider has a data association with the news
provider_item_data_association=The provider has data association with the learning plan
provider_type_provider_data_association=The provider type has a data association with the provider
course_pathway_course_data_association=There is a data association between school curriculum and bridge school curriculum
types_not_allowed_modified=Existing schools are bound to this bridge school, and types are not allowed to be modified.
course_student_character_collection_data_association=Please check whether the course is applied for, collected, or has special application information
course_item_data_association=There is a data association between the course and the learning plan
institutionId_null=School ID cannot be empty
course_news_data_association=There is a data association between the course and news
institutionTypeName_null=School type name cannot be empty
zone_course_data_association=There is data association between the campus and the course
majorLevelName_exists=Professional grade name already exists
Simplified_Chinese_not_translation=Simplified Chinese does not need translation
student_offer_not_exists=student offer not exists
client_offer_not_exists = client offer not exists
pay_plan_exists=Payment collection plan already exists
plan_state_abnormal=Plan status exception, unable to generate plan
region_has_association=Associated data exists in this region and cannot be deleted
target_login_id_not_exists=Target account does not exist
agent_status_not_allow_renewal=Current contract has been rejected/under review, renewal email cannot be sent!
student_offer_item_step_not_exists=student offer item step not exists
invalid_student_offer_not_exists=invalid student offer not exists
INTERLLIGENT_CALSSIFCATION=MSO intelligent classification
PROFESSIONAL_CLASSIFCATION=MSO domestic professional categories
OFFER_ITEM_CLASSIFCATION=BMS specialty classification program
MSO_SIMPLE_CLASSIFCATION=MSO simple classification
MSO_MODULE_CLASSIFCATION=MSO module classifcation
AREA_COUNTRY_ID_NATIONALITY_IS_NULL=The student's nationality is blank. Please enter the student's personal information
news_type_not_news_type_not_support_email=The news type does not support sending news mail.
#Financial Center
COMMISSION_PAYABLE_PLAN_NOT_EXIST=The payment plan does not exist and the commission cannot be activated.
AGENT_STATEMENT_EXPORT_EXCEPTION=Agent statement export exception, please contact the administrator
PAYMENT_FORM_EXISTS=Payment document bound
UNCOLLECTED=uncollected
PARTIALLY_RECEIVED=partially received
COLLECTION_COMPLETED=collection completed
provider_account_data_association=The provider has a data association with the settlement account
provider_contactPerson_data_association=The provider and the contact have a data association
plan_collect_amount=The plan has received all funds
RECEIVABLES_CANNOT_BE_SETTLED=Receivables cannot be settled and commission cannot be activated
bound_repeat=Cannot bind repeatedly
totalPay_greater_formAmount=The total actual payment amount is greater than the payment order amount
code_repeat=Have the same lottery number
totalPay_greater_payablePlanAmount=The total actual payment amount is greater than the payment plan amount
totalReceipt_greater_formAmount=The total amount actually received is greater than the amount of the receipt
totalReceipt_greater_receivablePlanAmount=The total amount actually received is greater than the amount of the receivable plan
fcy_not_count=Exchange rate cannot be obtained for foreign currency
receipt_fee_type_is_not_exists=Collection expense type does not exist
service_fee_insufficient_balance=The total binding service charge cannot be greater than the total service charge of the collection document
payment_form_amount_less_binding_amount=The payment document amount is less than the total amount of the bound payment sub documents
receipt_form_amount_less_binding_amount=The amount of the collection document is less than the total amount of the bound collection sub documents
receipt_fee_type_can_not_change=The receipt form has been bound, and the receipt fee type cannot be modified
currency_type_num_can_not_change=The receipt form has been bound to the centralized activity expense, and the expense currency cannot be modified
invoice_num_is_exist=Invoice number already exists
no_provider_found=No provider found,Please complete the information
invoice_bind_fail=There are multiple accounts receivable plans bound to the invoice, and the binding failed! Please confirm whether the invoice information is correct.
SELECT_RECEIPT_FORM_TO_MODIFY=Please select the payment receipt to be modified
CURRENCY_MISMATCH_REAL_EXCHANGE_RATE_NOT_ONE=Inconsistent currency. The actual exchange rate cannot be 1
NO_VOUCH_OPERATION_CONFIG=Can not find the credential operation configuration, please contact the system administrator to improve the credential operation configuration

#Talent Center
industryType_resumeWork_data_association=There is a data association between the industry type and resume work experience
otherType_resumeOther_data_association=This additional information is associated with other resume data
resumeType_resume_data_association=The resume type has a data association with the resume
skillType_resumeSkill_data_association=There is a data association between this skill type and resume skills
edit_resume_fail=Current employee status cannot edit resume
resume_id_null=resume id cannot be empty
#Workflow Center
start_process_fail_status=The process is no longer in a pending state and cannot be initiated. Please refresh
process_already_start_cannot_Invalid=The process has been initiated and cannot be invalidated
process_already_start_cannot_delete=The process has been initiated and cannot be deleted
process_already_delete=The process has been deleted. Contact the operator to modify the form status
redirect_fail=Redirect failed
downLoad_bpmn_fail=Download failed, please design the process first and save it successfully
model_null_publish_fail=The model data is empty, please design the process and save it successfully before publishing
import_model_fail=Failed to import model
deploy_model_fail=Deploy model failed
create_model_fail=Failed to create model
file_type_error=File format error
history_process_null=History process instance is empty
process_instance_exist=This process has already been run and cannot be deleted
task_obj_null=task is null
task_completed = Task completed, status cannot be modified
insert_task_item_null=The added subtask is null
exist_processed_task_item_cannot_delete=There are already processed subtasks that cannot be deleted
task_item_obj_null=task item is null
id_or_deleteReason_null=id or delete reason is null
deploy_process_fail=Deploy process failed
Paging_error=paging is error
get_userInfo_fail=Failed to obtain user information
model_error=The data model is invalid. There must be a master flow
update_false=update false
back_error=Is not the last approved user, not allowed to back
return_the_task_error=It cannot be returned if the current logon is not an approver or candidate or the current node is a modified data node
not_get_or_sign=not_get_or_sign
#System Center
resource_groupGrade_data_association=There is data association between system resources and group level
default_agreement_message=accept
default_disagreement_message=refuse
form_not_found=The form not found!
form_status_update_fail=The form status update fail
student_offer_not_found=Student offer not found
student_offer_status_update_fail=Student offer status update fail


#File Center
search_file_null=cannot find file
guid_null=guid is null
file_name_null=The file name is empty
file_format_error=File format cannot be uploaded
file_not_exist=file does not exist
picture_too_big=Picture cannot be greater than 1m
video_too_big=Video cannot be greater than 50m
pdf_too_big=Pdf cannot be greater than 20m
file_too_big=File cannot be larger than 10m
competition_item_logo_file=Competition item logo
competition_item_banner_file=Competition item banner
app_agent_contract_account_statement_file=statement file
app_agent_id_card=personal identity card
download_failed=Download failed:
FILE_UPLOAD_ERROR_PLEASE_RETRY=File upload error, please try again

#Platform Center
info_type_or_component_name_least_one=Fill in at least one info type / component name
COUNTRY_CHANNEL=country channel
INFO_CHANNEL_COUNTRY=info channel country
INFO_CHANNEL_NEWS=info channel news
AGENT_INFO_EXITS=agent info already exists
KEY_CODE_INFO_EXITS=key code already exists
information_collection_configuration_attachment_data_association=The attachment type has a data association with the information collection configuration
app_form_division_configure_data_association=The app form division has a data association with the configuration
institution_character_item_data_association=The institution character has a data association with the field configuration
app_form_config_attachment_division_data_association=The config has a data association with the attachment
IMPORT_FROM_OLD_DATA=import from old data
MANUAL_INPUT=Manual entry
IMPORT_FROM_OFFICIAL_DATA=import from office
IMPORT_FROM_BUSINESS_DATA=Business data import
IMPORT_FROM_OFFICIAL_DATA2=Business data import two
NEED_TO_ADD=need to add
COMPLETE_COLLECTION=complete collection
menus_contain_subMenus=menus contain subMenus
type_is_null=The check-in status cannot be empty


#Help Center
help_was_used_to_be_parent_help=This help was used to be parent help
DIRECT_DISPLAY=direct display
SMALL_LAYER_PLAIN_TEXT_DISPLAY=small layer plain text display
LARGE_LAYER_RICH_TEXT_DISPLAY=large layer rich text display
keyCode_exists=The same keyCode already exists
helpType_has_child_helpType=This helpType has child helpType
helpType_is_used=This helpType is used
keyCode_not_exists=The keyCode is not exists
upload_failure=Upload failed, please try again


#Office Center
start_time_end_time_error=start time end time error
annualLeaveBase_not_enough=annualLeaveBase not enough
compensatoryLeaveBase_not_enough=compensatoryLeaveBase not enough
disease_vacation_not_enough=disease vacation not enough
annualLeaveBase_not_enough_examine=Insufficient annual leave (including annual leave under approval)
compensatoryLeaveBase_not_enough_examine=Insufficient compensatory leave (some of which are under approval)
disease_vacation_not_enough_examine=Insufficient sick leave (including sick leave under approval)
leaveApplicationForm_days_error=leave application days can not less than one hour
leaveApplicationForm_is_revoke=the leaveApplicationForm was revoked
childLeaveApplicationForm_to_be_initiated=childLeaveApplicationForm is to be initiated
childLeaveApplicationForm_is_approval_in_progress=childLeaveApplicationForm is approval in progress
office_leave_application_form_file=Office leave certificate
finance_expense_claim_form_file=Expense Reimbursement Voucher
finance_prepay_application_form_file=Loan Application Voucher
finance_travel_claim_form_file=Travel Expense Reimbursement Voucher
finance_payment_application_form_file=Payment Request Voucher
workScheduleDateConfig_duplication=The company has a schedule for the current day
stock_cannot_be_negative=stock_cannot_be_negative
workScheduleTimeConfig_not_set=The relevant department has not set the schedule time
attachedRangeTimeConfig_not_set=Attendance time range is not configured
workScheduleTimeConfig_duplication = There are multiple scheduled shift times set by the department
effective_time_overlap = The effective time of the configuration overlaps with the effective time of the existing special staff scheduling time configuration

ANNUAL_REFRESH_ANNUAL_LEAVE=Annual refresh annual leave
OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF=Overtime to increase compensatory time off
COMPENSATORY_LEAVE_DEDUCTION=Compensatory leave deduction
ANNUAL_LEAVE_DEDUCTION=Annual leave deduction
WORKFLOW_CANCELLATION=Workflow Cancellation
HUMAN_RESOURCES_UPDATE=Human resource update
HUMAN_RESOURCES_ADD=Human resource add
EXPIRY_DATE_RESET=Expiry date reset
UNPAID_LEAVE_DEDUCTION=Unpaid leave deduction
SICK_LEAVE_DEDUCTION=Sick leave deduction
SICK_LEAVE_QUARTERLY_REFRESH=Sick leave quarterly refresh
SYSTEM_ADD_STOCK=System replenishment of obsolete inventory
TO_BE_INITIATED=to be initiated
APPROVAL_FINISHED=approval finished
APPROVAL_IN_PROGRESS=approval in progress
APPROVAL_REJECT=approval reject
APPROVAL_ABANDONED=approval abandoned
CANCELLATION=cancellation
REVOKED=Revoke
leave_form_miss_mediaAndAttached=The corresponding attachment is missing, please upload the attachment and then try to initiate the application
leave_form_overtime_no_revoke=The form cancellation time has expired and cancellation is prohibited. Please contact the administrator.
work_schedule_time_config_null = No schedule time setting found
leave_form_cannot_be_void=The vacation order is a cancellation order, can not be void!
get_accessToken_failed=Obtaining enterprise credentials accessToken failed.
not_logged_in=User is not logged in!
startup_exception=The application cannot be initiated because the initiation time is later than the application start time or the system allows. You need to add an attachment to explain the cause.
approval_exception=Cannot pass the approval,the approval time allowed by the system, please explain the reason in the approval comment.
failed_to_send_message=Failed to send message
type_error=Please select the corresponding vacation application type
date_error=The current application time is a non-working day
void_exception=The vacation order is a cancellation order, can not be void!
result_exceed_limit=The Search results exceed the upper limit
no_immediate_superior= The current user has not specified a direct supervisor, so they cannot submit a work leave application.
WORKING_TIME_SETTING_EXISTS=The working time setting already exists
FAILED_TO_GET_JSAPI_TICKET=Failed to obtain enterprise JsApiTicket!
AUTHORIZATION_SETUP_FAILED=Authorization setting failed
USER_ID_FETCH_FAILED=Failed to retrieve userId!
THIRD_PARTY_GET_ENTERPRISE_ACCESS_TOKEN_FAILED=Third party acquisition of enterprise AccessToken failed!


#General Information
id_null=id cannot be empty
search_result_null=No results were found
tableId_null=tableId is null
upload_file_null=The uploaded file cannot be empty
upload_vo_null=The upload object cannot be empty
search_vo_null=The query parameter cannot be empty
insert_vo_null=New parameter cannot be empty
update_vo_null=Modify parameter cannot be empty
delete_obj_null=There is no deleted object
tableName_null=The table name cannot be empty
delete_fail=Delete failed
insert_fail=Add failed
update_fail=Failed to modify
help_fail=The help information is the root node, and the pre-problem cannot be set
file_upload_fail=File upload failed, please try again
file_export_fail=File export failed, please try again
num_exist=Number already exists
account_exist=Account Number already exists
record_exist=The same record already exists
type_name_exists=type name already exists
parameter_missing=Missing parameter
status_effective=Effective
status_invalid=Invalid
illegal_type = Illegal type
search_fail=Search failed
missing_required_configuration=Missing required configuration
has_associated=Associated child data exists and cannot be deleted
exception_message=Exception message:
feign_execution_failed=The feign execution failed
SYSTEM_BUSY=System busy, please try again later


#exam center
examination_has_been_associated=The test has been associated with the test paper
examinationPaper_has_been_associated=The test paper has been associated with the test record
examination_not_active=examination not active
examinationPaper_not_active=examinationPaper not active
examination_start_time=examination startTime
examination_end_time=examination endTime
examinationPaper_start_time=examinationPaper startTime
examinationPaper_end_time=examinationPaper endTime
please_try_again_later=please try again later
examination_is_over=examination is over
complete_answer_examinationPaper=complete answer examinationPaper
no_examination_assigned=no examination assigned
answer_all_examination=answer all examination
examinationPaper_have_answer_records=examinationPaper have answer records.cannot modified generated questionCount
type_data_exists=Exist Question Data Cant Delete
answer_exist=The answer has been answered and cannot be deleted
question_has_been_examinationPaper=question has been examinationPaper
question_has_been_assigned=question has been assigned
questionType_has_been_assigned=questionType has been assigned
exam_unassigned_applet=exam unassigned applet
question_count_no_zero=question count no zero
#Voting Center
votingRule_associated=There are associated voting rules and cannot be deleted
VOTING_ITEM_NOT_STARTED=Not started
VOTING_ITEM_HAVE_IN_HAND=In hand
VOTING_ITEM_HAS_END=End
voting_theme_not_equal=Voting theme is not equal
voting_item_option_associated=There are associated voting options and cannot be deleted
is_repeat_voting_can_not_null=Is repeat voting cannot be null
voting_item_null=voting item null
voting_item_option_null=voting item option null
no_more_person_luck_draw=no more person luck draw
#\u00E6\u008F\u0090\u00E9\u0086\u0092\u00E4\u00B8\u00AD\u00E5\u00BF\u0083
remind_event_type_key_associated_template=remind event type key associated template
remind_event_type_key_exist=remind event type key exist
task_start_time_need_greater_now=task start time need greater now
end_time_need_greater_start_time=end time need greater start time
staff_email_null=staff email null
staff_mobile_or_mobileArea_null=staff mobile or mobileArea null
remind_task_max=remind task max
remind_emil_send_fail_user=Email reminder failed to send, failed to send user:
news_emil_send_fail=Failed to send mail
task_does_not_exist=Task does not exist
task_not_open=Task not open
task_receiver_is_empty=The task receiver is empty
staff_is_null=staff is null
mailbox_is_empty=The staff mailbox is empty
mailbox_template_is_empty=The mailbox template does not exist. You need to configure the mailbox template
phone_number_is_empty=The staff mobile phone number is empty
phone_area_code_is_empty=The staff mobile phone area code is empty
failed_to_send=Failed to send a short message, the exception message is as follows:
result_null=Failed to send SMS messages, and nothing was returned. Procedure
template_is_empty=Template is empty
INVALID_PARAMS_TYPE_FOR_EMAIL=invalid params type for email


#Competition Center
COMPETITION_ITEM_LOGO=Competition item logo
COMPETITION_ITEM_BANNER=competition item banner
#schoolgate
template_pic=Resume template image
SG_TRANSCRIPT_PIC=SG Resume Transcript
SG_USER_AVATAR=SG Common user profile picture
SG_REPORT_PIC=SG Resume Activity Form
SG_PERSONAL_ALBUM=SG Personal Album/Personal Album Logo
#xxljob Center
login_failure=Login failure
get_job_list_fail=Description Failed to obtain the jobList

# SG Contact Type (Enumeration, Parent-1, Mother-2, Guardian-3) ',
APP_SCHOOLGATE_FATHER=Father
APP_SCHOOLGATE_MOTHER=Mother
APP_SCHOOLGATE_GUARDIAN=Guardian

APP_SCHOOLGATE_TRANSCRIPT=Transcript
APP_SCHOOLGATE_ACADEMIC=Academic
APP_SCHOOLGATE_MUSIC=Music
APP_SCHOOLGATE_ARTS=Arts
APP_SCHOOLGATE_SPORTS=Sports
APP_SCHOOLGATE_LANGUAGE=Language
APP_SCHOOLGATE_SERVICE=Service
APP_SCHOOLGATE_SPEECH=Speech / Debate
APP_SCHOOLGATE_HOBBIES=Organization / Hobbies
APP_SCHOOLGATE_OTHERS=Others

APP_SCHOOLGATE_BACKGROUND=Background/ Introduction
APP_SCHOOLGATE_GOALS=Goals and Soclal Contributions
APP_SCHOOLGATE_REASON=Reason for choosing school
APP_SCHOOLGATE_LETTER=Letter of recommendation


//ClientStaff
fixed_amount_receivable_and_rate_ratio_cannot_be_empty_at_the_same_time= The fixed amount receivable and rate ratio cannot be empty at the same time
id_is_null = ID is null
clientId_is_null = Student resource ID cannot be null
staffId_is_null = Staff ID cannot be null
staff_is_exist = Staff is already bound
isActive_is_null = Activation status cannot be null

PUBLIC_PARTNER=Huaton Partner
institution_provider_do_not_hava_area_country=The service provider does not have a business country
news_type_mapping_not_found = News type not found
institution_provider_not_found = Institutional provider does not exist
no_valid_country_context = No valid national information available
file_not_found = Attachment does not exist
department_config_not_found = Department configuration does not exist
department_nums_not_found = The department does not exist
news_emil_template_not_found = Email template does not exist
news_email_send_fail = Email sending failed
PUBLIC_PMP=PMP

sale_student_file_from_rec = Identify information
cannot_be_invalidated = The service fee is either receivable or payable, but has failed to be invalidated
service_fee_does_not_exist = Service fee does not exist
no_data_search = The query result is empty
service_type_use = This type is currently in use, deletion failed
is_bind_null = Is it bound? The field is empty
agent_commission_type_id_is_null = Agent commission type ID is empty

agent_relation_exist=Agent tagging relationship already exists
agent_label_request_null = Agent tag request is empty
label_list_null =The agent tag list is empty
email_format_error = Email format error

delete_id_null = Delete ID as empty
delete_agent_commission_type_use = The commission type of the agent has already been bound to a commission scheme and cannot be deleted
inconsistent_data = Frequent mobile operations,please try again
event_activity_fees_exceed_budget = Exceeding the allocated activity expenses by {0}
currency_type_num_not_null = The currency type parameter is empty
type_key_exists = Type system key already exists
SUBMITTED_TO_FINANCE = Submitted to Finance

file_export_failed_no_service_fee = No service fee, export failed
group_name_null = Group name cannot be empty
institution_permission_group_id = Institutional permission group ID cannot be empty

#PMP
PMP_NO_PERMISSION=You do not have permission to operate
PMP_PLAN_RECORD_NOT_FOUND=Commission plan approval record does not exist
PMP_NOT_APPROVER=Approval failed, current user is not the designated approver
PMP_END_DATE_REQUIRED=End date cannot be null
PMP_START_DATE_REQUIRED=Start date cannot be null
PMP_DUPLICATE_PLAN=Duplicate commission plan exists, do not add again
PMP_PLAN_LOCKED=Plan is locked, no permission to operate
PMP_PLAN_NOT_FOUND=Commission plan does not exist
PMP_SCHOOL_REQUIRED=School cannot be null, please complete school info in the contract
PMP_COURSE_REQUIRED=Please publish at least one course
PMP_COURSE_LEVEL_MISSING=Course level information is missing
PMP_PLAN_IN_APPROVAL=Operation failed, plan is under approval
PMP_PLAN_SUBMITTED=Plan has been submitted for approval
PMP_NOT_ASSIGNED_APPROVER=Approval failed, current user is not the assigned approver
PMP_PLAN_STATE_CONFLICT=There are plans in pending/approved/rejected state, cannot change provider
PMP_SUBMIT_PLAN_EMPTY=Submitted plan list cannot be empty
PMP_TERRITORY_REQUIRED=Territory cannot be null
PMP_PENDING_PLAN_EXISTS=Pending plan exists, please do not submit another
PMP_CONTRACT_FILE_MISSING=Contract file not uploaded, please upload first
PMP_PLAN_INHERITED=Plan has been inherited by agent plan, cannot delete
PMP_TERRITORY_RULE_CONFLICT=Include/Exclude/Unspecified rules cannot coexist
PMP_DUPLICATE_COUNTRY=Duplicate country/region exists
PMP_CONTRACT_SCHOOL_MISSING=School cannot be empty
PMP_CONTRACT_NOT_FOUND=Contract does not exist
PMP_CONTRACT_SUBMITTED=Contract has been submitted
PMP_PROVIDER_CONTRACT_NOT_FOUND=Provider contract does not exist
PMP_UPDATE_CONTRACT_STATUS_FAIL=Failed to update provider contract status
PMP_ORIGINAL_CONTRACT_NOT_FOUND=Original contract does not exist
PMP_NEW_CONTRACT_DATE_OVERLAP=New contract time overlaps with original contract
PMP_CONTRACT_ID_REQUIRED=Contract ID cannot be null
PMP_PLAN_EXIST_UNDER_CONTRACT=Commission plans exist under contract, cannot delete
PMP_PARAM_REQUIRED=Parameter cannot be null
PMP_APPROVER_NO_COUNTRY_PERMISSION=Approver has no permission for the business country, please reselect
PMP_FILE_DOWNLOAD_FAILED=File download failed
PMP_SAVE_SUCCESS=Saved successfully
PMP_SUBMIT_SUCCESS=Submitted successfully
PMP_APPROVE_SUCCESS=Approved successfully
PMP_PUBLISH_SUCCESS=Published successfully
PMP_UNPUBLISH_SUCCESS=Unpublished successfully
PMP_REJECT_PROVIDER_PLAN_FAIL=The commission plan is still under review and cannot be rejected at the same time. Please try again after the review is complete.


#PARTNER
PARTNER_EMAIL_PASSWORD_EMPTY=Email password is empty, please complete it
PARTNER_ADMIN_ROLE_NOT_EXIST=Admin role does not exist
PARTNER_SAVE_USER_FAILED=Failed to save user
PARTNER_EMAIL_TEMPLATE_TYPE_ERROR=Email template type error
PARTNER_EMAIL_TEMPLATE_NOT_EXIST=Email template does not exist
PARTNER_EMAIL_INFO_EMPTY=Email information cannot be empty
PARTNER_CREATE_FAILED_USER_EXISTS=Creation failed, the following accounts already exist:

inaccurate_data = Inaccurate data
application_plan_delay_date_not_set = The application plan has set a delayed enrollment mark, but there is no record of the delayed date. Please supplement and submit again!
this = This
is_not_new_app_step = not the steps for applying for a new app
study_plan_not_exist = the course information in the study plan is empty. Please fill in the course information first!
study_plan_provider_not_exist = the provider information for the learning plan is empty. Please fill in the provider information first!
sub_application_not_selected_course = The sub application has not yet selected a course. Please select it before proceeding with the operation.
key_word_exist = The keyword already exists
illegal_target_type = Illegal target type
receipt_code_error = Incorrect receipt code
FAIRMONT_SANYA_HAITANG_BAY_RESTRICTIONS=Fairmont Sanya Haitang Bay allows a maximum of one room reservation. Please choose another hotel.
receipt_code_null = The receipt code is empty
convention_hotel_room_person= Convention hotel room person is null
institution_name_is_null = Institution name is null
provider_name_is_null = Provider name is null
convention_registration_id_is_null = Convention registration id is null
delete_without_permission = Delete without permission
convention_person_not_found = Convention person not found
parameter_is_empty = Parameter is empty
parameter_is_not_empty = Parameter cannot be empty
email_send_success = The email has already been sent and cannot be resent!
sign_encrypt_fail= Signature encryption failed
agent_not_exist= Proxy does not exist
nature_is_empty = nature is empty
id_card_num_is_empty = ID card number is empty
tax_code_is_empty = Tax ID parameter is empty
not_find_information = No relevant information found
express_info_error = express information error
not_yet_configured= not yet configured
entry_failed = Entry failed, invalid invitation code! (Code error.)
students_do_not_meet_the_requirements =The student does not meet the requirements！
phone_number_is_been_used = Phone number is been used
cannot_find_room_type = Cannot find room type!
no_room_type_price_set= No room type price set！
wechat_unified_ordering_exception = WeChat unified ordering exception
failed_to_obtain_wechat_pay_order_status = Failed to obtain WeChat Pay order status
failed_to_set_creator_creation_time = Failed to set creator creation time:
failed_to_set_the_update_person_update_time = Failed to set the update person update time:
the_cohabitant_does_not_exist = The cohabitant does not exist
room_type_name_not_found = Room type name found
student_statistics_failed = Student statistics failed, please try again later
the_strategy_does_not_exist = This strategy does not exist
data_is_being_compiled =The data is currently being compiled, please try again later!
invalid_cancellation_of_actual_payment_received = Invalid cancellation of actual payment received, please try again later
the_booth_has_been_ticked = The booth has been ticked
currency_is_not_unique = Currency is not unique
notification_information_cannot_be_empty = The notification information sent cannot be empty
the_payable_amount_should_be_negative = The payable amount should be negative
lack_of_business_configuration = Missing {0} business configuration
illegal_payment_order_type = Illegal payment order type
issue_merge_failed_operation_rollback = Issue merge failed, operation rollback
issue_remote_interface_exception = Issue remote interface exception, unable to merge
step_does_not_exist = The step does not exist, saving failed
the_receivable_amount_should_be_negative = The receivable amount should be negative
accounts_receivable_do_not_exist = Accounts receivable do not exist
cancel_fixed_type_accounts_receivable_plans = Can only invalidate study abroad application plan type accounts receivable plan
no_delayed_enrollment_time = Please set a delayed enrollment time before submitting!
save_error = Save failed, system is busy, please try again later.
no_relevant_information_found = No relevant information found
all_operators_have_been_assigned_permissions = The selected operators have been assigned permissions!
lack_of_configuration = Lack of configuration
please_select_the_contract_to_renew =Please select the contract to renew
illegal_renewal_type = Illegal renewal type
select_at_least_one_accounts_receivable_plan = Please select at least one accounts receivable plan
this_feature_is_not_currently_available = This feature is not currently available
cannot_find_preferred_account = Unable to find preferred account, generation failed
consistent_with_the_current_summit = The copied summit is consistent with the current summit and does not need to be copied!
no_replicable_configuration = No replicable configuration
copy_permission_configuration_failed = Copy permission configuration failed
the_payment_cannot_be_settled_for_commission = The payment cannot be settled for commission
the_agents_bound_to_the_students_are_inconsistent = The agents bound to the students who need to be merged are inconsistent, and merging is not currently supported
signature_verification_failed =Signature verification failed
request_has_expired = Request has expired
the_invoice_has_been_bound_to_the_payment_receipt = This invoice has been bound to a receipt and cannot be voided
please_select_supplier_and_channel = Please select supplier and channel
batch_matching_in_progress = In batch matching, do not click again. Please check the results later!
project_members_cannot_be_empty = Project members cannot be empty
must_bind = as a mandatory project member, please bind it before submitting
data_exception = Data exception
the_same_application_plan_has_been_created = The same application plan has already been created and submitted too frequently. Please try again later.
the_proportion_of_commission_payable_and_the_fixed_amount_payable_cannot_coexist_at_the_same_time = The proportion of commission payable and the fixed amount payable cannot coexist at the same time
process_revocation_failed = Process revocation failed
illegal_type_file = Illegal type file
only_one_employee_can_be_bound_to_the_same_role_in_the_same_country = Only one employee can be bound to the same role in the same country
illegal_student_data = Illegal student data, merge failed
end_time_is_not_null = The end time cannot be empty
there_is_settlement_data_for_the_strategy = This strategy has settlement data and cannot be operated
booth_is_null = The booth is empty
unable_to_find_lottery_ticket = Can't find the lottery ticket!
no_valid_lottery_tickets_available = No valid ticket available, please contact the staff
no_configured_email_found = No configured email found
this_phone_number_has_already_been_used = This phone number has already been used. Please change to a different phone number or contact the administrator to modify it!
lack_of_necessary_parameters = Lack of necessary parameters
translation_error = Translation error
at_least_select_a_country = At least select a country
no_data_available_for_export = No data available for export
illegal_state = Illegal state
unable_to_find_corresponding_agent = Unable to find corresponding agent
no_agent_reminder = No agent reminder
user_information_does_not_exist = User information does not exist
user_deactivated =User deactivated
invalid_id_card = Invalid ID card, please upload again
name_is_null = Name is empty
the_status_cannot_be_empty = The status cannot be empty
date_format_conversion_exception = Date format conversion exception
cannot_modify_order = The order has been paid or cancelled and cannot be modified
unable_to_find_employee = Can't find employee!!
analysis_failed = Analysis failed
authentication_failed = Authentication failed, please contact the administrator
same_target_type_information_already_exists = The same target type information already exists
the_length_of_id_card_number_is_incorrect = The length of ID card number is incorrect
non_corporate_agents_cannot_upload_their_business_license = Non-corporate agents cannot upload their business license
cannot_modify_course = There is already financial data for actual payment received, and the course, start time, and course length information cannot be modified!
non_target_type = The target type of this news is not [country] or [school], and operation is not allowed
user_does_not_exist = User does not exist
the_corresponding_accounts_receivable_plan_has_been_linked_to_invoices = The corresponding accounts receivable plan has already been linked to the invoice. Please unbind it before proceeding!
only_supports_types_student_application_and_study_abroad_insurance = Only supports international student application plans and accounts receivable plans for study abroad insurance types
platform_authorization_code_has_not_been_configured_yet = The platform authorization code for login has not been configured yet. Please contact the administrator
platform_type_not_yet_configured_for_login = The platform type fkPlatformTypes that has not been configured for login, please contact the administrator
this_platform_does_not_yet_support_cross_system_login = This platform does not yet support cross system login
illegal_operation_the_plan_does_not_match_the_student = Illegal operation, the plan does not match the student
failed_to_send_sms_verification_code = Failed to send SMS verification code
get_exchange_rate_error = Get exchangeRate error
translate_fail = Translation failed
failed_to_send_email_on_behalf_of_project_member = [Failed to send email on behalf of project member]
email_password_resolution_failed= Email password parsing failed!
the_registration_quota_is_full = The registration quota is full
as_a_mandatory_project_member = As a mandatory project member, this role cannot be removed
already_been = already_been
cannot_be_added_repeatedly = add as assessor, cannot add again
a_signature_with_the_same_title_already_exists = A signature with the same title already exists
agent_affiliated_company = Agent_company

contract_formula_read_only = The read-only phase of the contract formula does not match the business scenario
pre_school_conditions_do_not_match = Pre school conditions do not match
students_have_prerequisite_school_conditions = The contract formula does not limit the prerequisite schools, students have prerequisite school conditions
pre_group_mismatch = Pre group mismatch
students_have_prerequisite_group_conditions = The contract formula does not limit the prerequisite group, but students have prerequisite group conditions
study_plan_has_no_prerequisite_course_conditions = The contract formula has course prerequisites, but the learning plan does not have prerequisite course conditions
pre_course_mismatch = Pre course mismatch
students_have_prerequisite_course_requirements = The contract formula does not limit prerequisite courses, students have prerequisite course conditions

the_current_reward_policy_does_not_exist = The current reward policy does not exist
the_current_reward_policy_has_been_settled = The current reward policy has been settled
repeated_selection_of_application_plan = Please do not repeatedly select application plans that have already been manually included
no_application_plans_that_can_be_manually_deleted= There are no application plans that can be manually deleted. Please refresh and try again
the_application_plan_has_been_settled = Only unsettled application plans can be manually deleted. Please do not select settled application plans
duplicate_deletion_application_plan = Please do not repeatedly select application plans that have already been manually deleted
reward_policy_does_not_exist = Reward policy does not exist, please refresh and try again
the_reward_policy_has_been_settled = The reward policy has been settled, please refresh and try again
no_unsettled_application_plans_available = There are no application plans available for settlement. Please refresh and try again
no_cancellable_application_plans = There are no cancellable application plans, please refresh and try again
only_unsettled_application_plans_can_be_cancelled_manually = Only unsettled application plans can be cancelled from manual settings. Please do not select settled application plans
only_application_plans_that_are_not_included_in_the_system_can_cancel_manual_settings = Only application plans that are not included in the system can be manually set. Please do not select application plans that are included in the system.
no_reward_rule_matching = No reward rule matching
not_meeting_the_requirements_of_the_reward_rules = The total number of statistics does not meet the minimum number requirement of the reward rules
there_is_data_being_settled = Data is currently being settled, please try again later!
reward_rules_have_not_been_configured_yet = The reward rules have not been configured yet. Please configure the reward rules
the_prize_name_already_exists = The prize name already exists
the_prize_is_currently_in_use = The prize is currently in use
full_seats_available = Full seats, please choose another training table
insufficient_seat_balance = Insufficient seat balance, unable to switch event venues

file_type_key_null = The file type cannot be empty
duplicate_name = The name is duplicated,Please modify it and submit it
sort_param_error = Sorting parameter error
accounting_item_id_null = The accounting ID cannot be empty
accounting_item_code_null = The accounting item code cannot be empty
accounting_item_code_name_null = The accounting item name cannot be empty
accounting_item_type_null = The accounting item type cannot be empty
accounting_item_grade_null =  The accounting item grade cannot be empty
accounting_item_direction_null = The accounting item direction cannot be empty
accounting_item_is_active_null = The accounting item is active cannot be empty
accounting_item_bound_payment_method_type = This account is bound to a payment method type and cannot be deleted
accounting_item_bound_payment_fee_type = This account is bound to a payment fee type and cannot be deleted
accounting_item_bound_receipt_fee_type = This account is bound to a receipt fee type and cannot be deleted
accounting_item_bound_expense_claim_fee_type = This account is bound to an expense claim fee type and cannot be deleted
accounting_item_bound_travel_claim_fee_type = This account is bound to a travel claim fee type and cannot be deleted
accounting_item_bound_receipt_method_type = This account is bound to a receipt method type and cannot be deleted
accounting_item_bound_expense_claim_agent_content = This account is bound to an agent-related expense claim and cannot be deleted
the_name_or_code_already_exists = Name or code already exists
type_name_null = The type name cannot be empty
vouch_type_null = Voucher type cannot be empty
receipt_method_type_id_null =The receipt method type cannot be empty
receipt_amount_null =The receipt amount cannot be empty
receipt_amount_must_be_greater_than_zero = The received amount must be greater than zero！
receipt_date_null = The receipt date cannot be empty
business_date_null = Business date cannot be empty
vouch_item_null = Voucher details cannot be empty or missing debit and credit information (voucher details should be filled with paired debit and credit information)
delete_obj_used_by_vouch_receipt_register =  Used and cannot be deleted (using object: vouch receipt register)
cancel_obj_used_by_vouch_receipt_register =  Used and cannot be invalidated (using object: vouch receipt register)
cancel_obj_used_by_travel_claim_form_item =  This type has been used and cannot be invalidated (target: travel claim form item)
character_limit_exceeded = The character length exceeds the limit
no_replicable_template_data = No replicable template data
company_profit_and_loss_item_data_already_exists = The company's profit and loss data has been created and cannot be used as a template

REGISTRATION_CONFIRMATION = The exhibitor has already registered, please confirm if you want to proceed with the registration?
get_bms = BMS
get_issue = ISSUE
AEAS = AEAS

the_area_country_of_the_school_provider_cannot_be_modified = The area country of the school provider has already been linked to the corresponding school and cannot be modified
# 添加新的国际化信息
agent_contact_person_count_invalid=Agent application contact person needs to provide 3 contacts
agent_new_contact_person_type_invalid=The contact type for proxy application is invalid and must include the enterprise manager (ADMIN), commission settlement manager (COMMISSION), and emergency contact (EMERGENCE)
agent_emergency_contact_duplicate=The emergency contact person cannot be the same person as the enterprise manager/mini program administrator and commission settlement manager!

staffBdId_is_null = BD id cannot be empty
contact_person_type_already_exists=This contact person type already exists
contact_person_already_exists=A contact person with the same name and email already exists
contact_person_empty=Contact person cannot be empty
contact_person_type_empty=Contact person type cannot be empty
contact_type_duplicate=Contact type is duplicated, only one contact of each type can be added
emergency_contact_duplicate=Only one emergency contact can be added
admin_contact_duplicate=Only one enterprise admin can be added
commission_contact_duplicate=Only one commission manager can be added
contact_person_type_exists=Contact person type "{0}" already exists
contact_person_type_conflict=Contact person type conflict, please check type combination
bd_staff_id_null=BD staff ID cannot be empty, unable to get sender information
agent_ids_null=Agent ID set is empty, unable to get BD staff information
agent_staff_not_found=Agent staff association information does not exist, unable to get BD staff information  
bd_staff_id_empty=BD staff ID set is empty, unable to get BD staff information
bd_info_not_found=BD information query failed, email sending failed
bd_staff_info_get_failed=Failed to get BD staff information, staff ID: {0}
bd_staff_email_null=BD staff email is empty, cannot be used as sender, staff ID: {0}
bd_staff_email_password_null=BD staff email password is empty, cannot send email, staff ID: {0}
unsupported_contact_person_type=Unsupported contact person type: {0}
partner_user_register_failed=Failed to register partner user: {0}

# Contract renewal related exceptions
contract_status_rejected_or_pending=Current contract has been rejected/pending approval, cannot send renewal email
save_agent_application_failed=Failed to save agent application

# Agent contact person exceptions
agent_contact_person_cannot_be_empty=Agent contact person cannot be empty
agent_application_id_cannot_be_empty=Agent application id cannot be empty

# Email related exceptions
email_recipient_not_empty=Recipient email cannot be empty
email_template_not_empty=Email template cannot be empty
email_format_invalid=Invalid email format
email_parameter_parse_failed=Email parameter parsing failed
email_template_process_failed=Email template processing failed

# Contract approval related exception messages
approval_id_required=Approval record ID cannot be empty
approval_record_not_found=Approval record not found, ID: {0}
approval_status_no_email_required=Approval status {0} does not require email notification
contract_id_required=Contract ID cannot be empty
approval_status_required=Approval status cannot be empty
contract_not_found=Contract not found, ID: {0}
agent_not_found=Agent not found, ID: {0}
bd_staff_info_not_found=BD staff information not found for agent, Agent ID: {0}
bd_staff_email_empty=BD staff email address is empty, Staff ID: {0}
partner_user_register_error=Failed to register partner user
contact_person_not_found=Contact person not found, ID: {0}

cannot_support_retrieving_attributes = Unsupported search attribute
decryption_failed = Decryption failed

agent_person_id_card_duplicate=A personal agent with this ID card number already exists under the current company
agent_company_tax_code_duplicate=A company agent with this tax code already exists under the current company
