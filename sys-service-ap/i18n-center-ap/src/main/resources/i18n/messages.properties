no_data_permission=没有公司权限
permission_contract_file=员工合同资料
permission_hrevent_file=员工人事事件资料
permission_staff_file=员工资料
permission_head_icon=员工头像
permission_company_icon=公司图标
institution_logo=学校logo
institution_pic=学校环境图
institution_mov=学校介绍视频
institution_mso_pdf=MSO-PDF
institution_cover=学校封面图
institution_map_gg=google地图
no_payable_plan_found=找不到应付计划
institution_map_bd=百度地图
institution_provider_file=学校提供商资料
institution_contract_file=学校合同资料
institution_country_emblem_pic=国徽
institution_country_flag_pic=国旗
institution_country_mso_banner=MSO-Banner
institution_faculty_pic=学院环境图
institution_faculty_mov=学院介绍视频
institution_zone_pic=校区环境图
institution_zone_mov=校区介绍视频
institution_major_pic=专业图
institution_major_mov=专业介绍视频
institution_course_pic=课程图
institution_course_mov=课程介绍视频
institution_course_file=课程资料
institution_alumnus_pic=知名校友图
institution_alumnus_mov=知名校友介绍视频
institution_alumnus_file=知名校友资料
permissions_insufficient=权限不足
institution_info_pic=资讯图
institution_info_mov=资讯介绍视频
institution_info_file=资讯附件
institution_news_file=新闻文件
institution_news_title_pic=新闻标题图片
institution_news_cover=新闻封面图
institution_news_appendix=新闻附件
institution_news_mail_appendix=邮件附件
institution_country_info_description=国家资讯富文本
institution_country_info_pic=国家资讯配图
resume_icon=简历头像
resume_file=简历资料
finance_invoice_file=发票资料
finance_provider_file=供应商资料
finance_receipt_file=收款单资料
finance_payment_file=付款单资料
finance_receipt_item_file=应收计划资料
finance_payment_item_file=应付计划资料
REPEATED_PREPAYMENT=存在已预付或已收钱情况，不能预付
PAYABLE_PLANS_CANNOT_GENERATE_COMMISSIONS=该应付计划不能生成佣金.
NOT_PAY_ADVANCE_SETTLEMENT_INSTALLMENT=预付已经进行财务佣金结算，不能取消。
ACCOUNTS_PAYABLE_COMPLETED=应付已经完成，不能再设置预付。
NON_PREPAYABLE=总预付金额大于应付金额，请减少预付比例
SALE_NUM_SETTLEMENT_BATCH_PAYMENT_FORM_EXIST=该批次号已存在付款单，不可回退
SALE_CANNOT_LOCK_REPEATEDLY=不可以重复锁定
sale_settlement_in_progress=应付计划结算中，不能修改关联的数据
sale_convention_description=峰会富文本
sale_convention_procedure_description=峰会流程富文本
sale_agent_file=代理资料
agent_business_license=代理营业执照
sale_agent_id_card_front=身份证国微面
sale_agent_id_card_back=身份证人像面
sale_contract_file=代理合同资料
sale_contract_seal_file=公司盖章合同
sale_student_file=学生资料
m_app_student_file=partner学生资料
sale_client_file=客户资料
sale_offer_item_file=学习计划资料
sale_payable_file=应付计划资料
sale_receivable_file=应收计划资料
comment_comment=评论文件
sale_event_file=活动资料
DEFER_ENTRANCE_TIME_NOT_EXISTS=延迟入学时间不能为空
PLAN_ONE_TO_ONE=一条应收计划只能绑定一条应付计划
student_accommodation_pic=留学住宿图
student_accommodation_mov=留学住宿介绍视频
student_accommodation_file=留学住宿资料
student_insurance_pic=留学保险图
student_insurance_mov=留学保险介绍视频
student_insurance_file=留学保险资料
d_list_export=列表导出
export_empty= 没有可导出的数据
1=进行中
2=完成
0=失败
DEBIT_CARD=借记卡
PASSBOOK=存在
CREDIT_CARD=信用卡
SEMI_CREDIT_CARD=准贷记卡
PREPAID_CARD_FEE=预付卡费
OVERSEAS_CARD=境外卡
m_student=学生
m_institution_provider=学校提供商
m_business_provider = 业务提供商
institution_news_bulletin_board=MSO公告栏
geographical_division=地理划分
ROUTINE=常规
u_area_country=国家
u_area_state=州省
m_institution=学校
m_institution_course=课程
m_provider=提供商
m_agent=学生代理
GLB=全球
US=美国
UK=英国
ANZ=澳新
CAN=加拿大
EUASIA=欧亚
BLANK=不需要链接
URL=网址链接
COMPONENT=模块链接
NEWS_INFO=新闻资讯
COUNTRY_INFO=国家资讯
COUNTRY_ABOUT=升学国家标题
NEWS_ABOUT=留学资讯标题
NEWS_DETAILS_ID=新闻详情
submitted_media=申请提交完成凭证资料
m_student_service_fee_invoice=留学服务费发票
m_student_service_fee_media=留学服务费附件
m_student_service_fee_cost_media=成本支出附件
m_insurance_order=留学保险（澳小保）
app_received_media=院校已收件凭证资料
admitted_media=已录取收到OFFER凭证资料
deposit_media=已付押金凭证资料
cascoe_media=CAS/COE凭证资料
visa_submitted_media=递交签证完成凭证资料
VISA_SUBMITTED_MEDIA_MY_SG=递交签证完成(马来西亚、新加坡)
visa_granted_media=获得签证凭证资料
entrance_success_media=入学登记完成凭证资料
entrance_fail_media=入学失败凭证资料
notified_payment_media=已通知缴费凭证资料
registered_media=已挂号凭证资料
application_for_extension_media=申请延期凭证资料
postponed_media=已延期凭证资料
apply_for_deposit_refund_media=申请退押金凭证资料
tuition_media=已付学费凭证资料
offer_accepted_media=已接受Offer
missing_media=缺资料
AWARD=奖励
ONE_AWARD=一次性奖励
SPECIAL=特殊（优先匹配）
TRANSFER_AGENT_STUDENT=转代理学生
PERCENTAGE_OF_SCHOLARSHIP_FEES = 奖学金占学费的 60% 以上
NUMBER_OF_STUDENTS=累计学生数
FEE_OF_STUDENTS=学费
COURSE_DURATION_WEEK=课程长度(周)
COURSE_DURATION_MONTH=课程长度(月)
COURSE_DURATION_YEAR=课程长度(年)
COURSE_DURATION_SEMESTER=课程长度(学期)
STUDENT_OFFER_ITEM_COURSE_DURATION_WEEK=周
STUDENT_OFFER_ITEM_COURSE_DURATION_MONTH=月
STUDENT_OFFER_ITEM_COURSE_DURATION_YEAR=年
STUDENT_OFFER_ITEM_COURSE_DURATION_SEMESTER=学期
LACK_OF_INFORMATION=缺资料
NOT_OPEN=未开放
NOT_MARKED=无标记(空值)
ACCUMULATED_TUITION=累计学费
NOT_PUBLIC=不公开
PUBLIC=公开
PUBLIC_STUDENTS=学生
PUBLIC_AGENT=代理
PUBLIC_MSO_CN=MSO_中国内地
PUBLIC_MSO_HK=MSO_香港
PUBLIC_MSO_TW=MSO_台湾
PUBLIC_HKISO=HKISO
PUBLIC_IBS=IBS
PUBLIC_ISSUE=ISSUE
PUBLIC_CURRENCY_COMMON=常用币种
PUBLIC_COUNTRY_HOME=国家_首页
PUBLIC_CURRENCY_ACC=留学小屋
MPS=MPS
PUBLIC_COUNTRY_COMMON=国家_常用
SYSTEM_CONFIGURATION=系统配置
BUSINESS_CONFIGURATION=业务配置
WORLD_RANKINGS_QS=QS世界排名
WORLD_RANKINGS_TIMES=TIMES世界排名
ASIA_RANKINGS_QS=QS亚洲排名
ASIA_RANKINGS_TIMES=TIMES亚洲排名
INTERNATIONAL_RANKINGS_QS=QS最国际化排名
INTERNATIONAL_RANKINGS_TIMES=TIMES最国际化排名
COUNTRY_RANKINGS=国家排名
CUG_RANKINGS=CUG排名
REF_RANKINGS=REF排名
FACULTY_STUDENT_RATIO=师生比例
SEX_RATIO=男女比例
PERCENTAGE_OF_INTERNATIONAL_STUDENTS=国际学生百分比
PERCENTAGE_OF_CHINESE_STUDENTS=中国学生百分比
STUDENTS_NUM=学生人数
VIDEO_WEBSITE_01=视频网址
ACCEPTANCE_RATE=录取率
ENROLLMENT=入学人数
INFO_SCHOOL_ADVANTAGE=学校优势
INFO_IB_AP_OSSD_AL=分制
INFO_CURRICULUM_JUNIOR=学制_初级
INFO_CURRICULUM_SENIOR=学制_高级
INFO_CHINESE_FONT=中文使用字体
INFO_TUITION_FEE_HIGHEST=最高学费
INFO_CLASS_STRUCTURE=班级结构
INFO_GENDER=男校女校
RELIGION=宗教
TEL=电话
FAX=传真
SPECIFIC=学校特色
LIVING_EXPENSES=生活费用
CLOTHING_EXPENSES=服装费用
CATERING_EXPENSES=餐饮费用
ACCOMMODATION_EXPENSES=住宿费用
TRANSPORTATION_EXPENSES=交通费用
TEACHING_LANGUAGE=教学语言
SET_UP_NATURE=成立性质
INFO_IB_AVERAGE=平均IB
INFO_IB_40=IB 40+%
INFO_AP_AVERAGE=平均AP
INFO_AP_4=AP 4+%
INFO_A_A=A*/A%
INFO_A_B=A*/B%
INFO_TOP_20_RATE=前20U录取率
INFO_APPLICATION_DEADLINE=报名截止日期
INFO_ONLINE_APPLICATION=在线申请
INFO_APPLICATION_FEE=报名费
INFO_APPLICATION_RENEWAL_FEE=申请更新费
INFO_ASSESSMENT_FEE=评估费
INFO_CAPITAL_LEVY=资本税
INFO_DEPRECIATING_DEBENTURES=折旧债券
INFO_DEPRECIATED_BY=折旧
CAREER_PATH=职业规划
LIVING_EXPENSES_STUDYING_ABROAD=留学生活费
TOTLE_EXPENSES_STUDYING_ABROAD=留学总费用
LIFE_SAFETY=生活安全指数
COUNTRY_RANKING=国家排名
WORLD_RANKING=世界排名
RENEW=续签
NEWSIGNING=新签
SMALL_RECOMMENDATION=小推荐
MEDIUM_RECOMMENDATION=中推荐
BIG_RECOMMENDATION=大推荐
GET_THE_FIRST_STAGE_COMMISSION=获得第一阶段佣金
GET_THE_SECOND_STAGE_COMMISSION=获得第二阶段佣金
GET_THE_THIRD_STAGE_COMMISSION=获得第三阶段佣金
GET_THE_FOURTH_STAGE_COMMISSION=获得第四阶段佣金
READ_ONLY_PHASE_ONE=只读第一阶段
READ_ONLY_PHASE_TWO=只读第二阶段
READ_ONLY_PHASE_THREE=只读第三阶段
READ_ONLY_PHASE_FOUR=只读第四阶段
ACADEMIC_REQ=升读学术要求
ENGLISH_REQ=升读英语要求
SOPHOMORE_ACADEMIC_REQ=升读大二学术要求
SOPHOMORE_ENGLISH_REQ=升读大二英语要求
SPECIFIC_SUBJECT_REQ=升读特定科目要求
UN_DECIDED=未定
FACE_TO_FACE=面授
ONLINE_CLASS=网课
BUY_SCHOOL_INSURANCE=买学校保险
BUY_HTI_INSURANCE=通过Hti买保险
BUY_OTHER_INSURANCE=通过其他机构买保险
BUY_OTHER_INSURANCE_OTHER=其他
PAYMENT_BY_FEI_HUI=飞汇
PAYMENT_BY_YI_SI_HUI=易思汇
PAYMENT_BY_ALIPAY=支付宝
PAYMENT_BY_BANK=银行
PAYMENT_BY_CREDIT_CARD=信用卡
PAYMENT_BY_OTHER_REMITTANCE_PLATFORM=其他汇款平台
PAYMENT_BY_WECHAT=微信
PAYMENT_BY_INTERAC=Interac e-Transfer
EVENT_SEARCH_BY=活动数据总览搜索维度
SEARCH_BY_TARGET_COUNTRY=活动对象国家
SEARCH_BY_STATE=举办省份
SEARCH_BY_CITY=举办城市
SEARCH_BY_STAFF=负责人
SEARCH_BY_STAFF2=第二负责人
SEARCH_BY_EVENT_TYPE=活动类型
staff_bind_superior_error=该员工已与业务下属有关联，无法绑定为业务上司
supervisor_set_error=自身或者下属不能设置为直属上司
app_course_website=课程详细网址
app_course_req_website=课程要求网址
scholarship_info=奖学金信息
scholarship_website=奖学金网址
app_ucas_code=UCAS CODE
personal_statement=个人陈述
admission_requirements=入学试收生要求
interview_preparation=面试准备
cncc=无犯罪记录证明书
app_cricos_code=CRICOS CODE
app_program_code=PROGRAM CODE
BIRTHDAY=生日
EMAIL=邮箱
MOBILE=移动电话
PASSPOST=护照
STUDENT_EXIST=已经存在
STUDENT_AGENT_IS=学生当前绑定代理
STUDENT_APPLICATION_STATUS_TOP=当前最高申请状态
IS_EXIST_STUDENT=【查看学生详情】--点击弹出新tab，查看信息
IELTS=IELTS
TOEFL_IBT=TOEFL-IBT
TOEFL_PBT=TOEFL-PBT
PTE=PTE
HKDSE_ENG=HKDSE-ENG
CET=CET
DUOLINGO=DUOLINGO
BEC=BEC
LANGUAGE_CERT=LanguageCert
NCEE=National College Entrance Exam
HKDSE=HKDSE (Hong Kong)
ALEVEL=GCE A Level
IB=IB Diploma
MSS=Middle School Score
AP=Advanced Placement
UG=UG
CLASS=Class
GPA=GPA
GCE=GCE
GCSE=GCSE
GPA4=GPA(out of 4 points)
GPA4_2=GPA(out of 4.2, retain two decimal places)
GPA4_5=GPA(out of 4.5, retain two decimal places)
GPA5=GPA(out of 5 points)
GPA7=GPA(out of 7 points)
GRE=GRE
GMAT=GMAT
GPA9=GPA(out of 9, retain two decimal places)
PERCENTAGE=Percentage
GRADING=Grading
GU=GU
OTHER_REQUIREMENTS=other requirements
AGE=Age
WORK_EXP=Work Experience
ACCEPT_MAJOR_TRANSFER=Accept major transfer, recommended major background
OLEVEL=GCE O Level
OSSD=Ontario Secondary School Diploma
NCUK=NCUK
ATAR=ATAR
GAOKAO=GaoKao (China)
SAT1=SAT1 (USA)
SAT2=SAT2 (USA)
ACT=ACT (USA)
AD=Advanced Diploma (Sinfgapore)
STPM=STPM (Malaysia)
MUFY=Monash University Foundation year (Malaysia)
PM=Program Matrikulasi (Malaysia)
UEC=UEC (Malaysia)
ISCE=Indian School Certificate Examination (Malaysia)
DS=Diploma Studies (Mapaysia)
SPM=SPM(Mapaysia)
SMA3=SMA3 (Indonesia)
SMA2=SMA2 (Indonesia)
MATAYOM6=Matayom 6 (Thailand)
NE=National Exam (Vietnam)
GPA4_3=GPA(out of 4.3)
GPA10=GPA(out of 10 points)
GPA12=GPA(out of 12 points)
WC_INSTITUTION_CONSULT=学校资讯小程序
APP_FEE_BACHELOR=本科
APP_FEE_MASTER_DEGREE=硕士
APP_FEE_COMMUNITY=社区

#销售中心
STUDENT_NOT_QUALIFIED=该学生不符合条件！
EXISTENCE_LABEL=同一个目标对象，不能存在多个标签。
COURSE_MAJOR_LEVEL_IS_NULL=课程等级不能为空，请联系管理员。
COURSE_LEVEL_TYPE_IS_MISSING=所选课程没有定义课程等级或类型，请补充后再提交。
COURSE_TYPE_IS_NULL=课程类型不能为空，请联系管理员。
REPEATED_PHONE_NUMBER=该手机号已参加报名，请不要重复报名。
RECEIVABLE_AMOUNT_NOT_ZERO=应收金额不能为0
EXISTS_ITEM_FINANCIAL_DATA=该申请计划已产生财务数据，请联系财务操作.
NON_REVIEWER=申请资料审核中，你并不是审核人，不能点击通过
contact_person_mobile=联系人移动电话
contact_person_tel=联系人固话
contact_person_email=联系人电邮
bank_account_name=账户名称
bank_account_num=账号
agent_file_download_failed=【{0}代理】导出文件失败，该代理操作暂停处理。
agent_file_download_idcard_failed=【{0}代理】身份证缺失，导出文件失败，该代理操作暂停处理。
agent_file_download_license_failed=【{0}代理】营业执照信息缺失，导出文件失败，该代理操作暂停处理。
PAYABLE_PLAN_SETTLEMENT_MARK_NOT_EXISTS=应付计划结算标记不存在
PRIMARY_AGENT_SETTLEMENT_PORT_NOT_EXISTS=一级代理必须为结算口
INVOICE_PREPAYMENT_SETTLEMENT_IN_PROGRESS=预付已经进行财务佣金结算，不能修改发票绑定金额。
PREPAYMENT_SETTLEMENT_IN_PROGRESS=已有预付结算中，不可再次预付
PAYABLE_PLAN_PROGRESS=该应付计划正在结算中或已结算完成，不能修改
SETTLEMENT_BANK_ACCOUNT_IS_NULL=结算银行账号不能为空
STATUS_SETTLEMENT_ABNORMAL=应付计划结算状态异常
STUDENT_OFFER_ITEM_PROGRESS=该学习计划正在结算中，无法修改
plan_receipt_form_item_exists=该学习计划所绑定的应收计划与收款单:{0}存在绑定关系，无法重新生成应收应付计划
plan_payment_form_item_exists=该学习计划所绑定的应付计划与付款单:{0}存在绑定关系，无法重新生成应收应付计划
receipt_form_balance_not_enough=费用归口金额大于收款单可绑定余额
agent_followAgent_data_association=该代理存在下属代理
agent_contract_data_association=该代理下合同存在数据关联
agent_event_data_association=该代理下事件存在数据关联
agent_student_data_association=该代理下学生存在数据关联
agent_convention_data_association=该代理下峰会存在数据关联
agent_contractFormula_data_association=该代理下合同公式存在数据关联
student_contactPerson_data_association=该学生与联系人存在数据关联
student_event_data_association=该学生与学生事件存在数据关联
student_offer_data_association=该学生与申请方案存在数据关联
client_contactPerson_data_association=该客户与联系人存在数据关联
client_event_data_association=该客户与客户事件存在数据关联
client_offer_data_association=该客户与申请咨询方案存在数据关联
offer_offerItem_data_association=该方案与学习计划存在数据关联
step_offerItem_data_association=该步骤与学习计划存在数据关联
contract_contractAccount_data_association=该合同与合同账号存在数据关联
hotel_hotelRoom_data_association=该酒店房型与已开房间存在数据关联
hotelRoom_person_data_association=该房间与人员存在数据关联
registration_table_data_association=该峰会报名与培训桌位存在数据关联
person_hotelRoom_data_association=该参会人员已被安排房间
person_table_data_association=该参会人员已被安排桌位
person_procedure_data_association=该参会人员已被安排流程
procedure_person_data_association=该流程已有人员参与
convention_person_data_association=该峰会已有参会人员
convention_table_data_association=该峰会已有桌台
convention_hotel_data_association=该峰会已有酒店房型
convention_procedure_data_association=该峰会已有峰会流程
convention_registration_data_association=该峰会已有峰会报名
table_registration_data_association=该桌台已配置展台
item_payable_data_association=该学习计划与应付计划存在数据关联
offer_financial_association=该方案有关联的财务数据，不允许作废
insurance_payable_data_association=该学习计划与应付计划存在绑定关系,请先作废该应付计划
item_receivable_data_association=该学习计划与应收计划存在数据关联
table_person_data_association=该桌台已配置人员
event_cost_data_association=该活动存在费用归口
event_registration_data_association=该活动存在报名名册数据
eventType_event_data_association=该活动类型与活动存在数据关联
staff_agent_data_association=该BD与代理存在数据关联
student_offer_project_role_data_association=该角色与学习计划存在数据关联
staff_agent_project_role_data_association=该角色与员工和代理存在数据关联
table_countDown=座位已安排满，无法减少座位数
edit_companyId=至少配置一个所属公司
agent_not_configure_company=当前代理未配置所选公司
offerItem_id_null=申请方案项目Id不能为空
offer_id_null=学生项目Id不能为空
student_id_null=学生Id不能为空
agent_id_null=代理Id不能为空
code_repeat=有相同奖券号码
bd_agent_company_inconsistency=代理所属公司与bd所属公司不一致
bd_agent_exit=已有相同bd存在
bd_agent_company_data_association=现时代理所属公司，包含了绑定的BD代理所在的公司，修改代理所属公司失败。若要操作，请先解除BD绑定，再进行代理所属公司调整。
company_id_null=公司id不能为空
event_id_null=活动id不能为空
budget_amount_null=设置活动结束时：预算金额，必须填写
actual_amount_null=设置活动结束时：实际金额，必须填写
attended_count_null=设置活动结束时：参加人数，必须填写
remark_null=设置活动结束时：活动评价，必须填写
attended_count_actual_amount_budget_amount_remark_null=设置活动结束时：预算金额，实际金额，参加人数，活动评价，必须填写
attended_count_actual_amount_budget_amount_null=设置活动结束时：预算金额，实际金额，参加人数，必须填写
seting_enrol_Failure_unsuccessful = 当前学习计划为子计划并且设置了跟随父计划，不能进行操作，请操作其相应父计划
step_no_reverse=步骤顺序不可逆
staff_bd_null=创建代理失败,当前登录用户未配置BD角色
role_bound_exits=已绑定该项目成员
agent_bound_exits=已绑定该代理
required_bd=至少需要绑定一个BD，不能取消BD绑定
required_agentId=至少绑定一个代理
person_arrange_table=该人员已被安排桌台
table_full=桌台已坐满
required_rate_or_amount=学费百分比/应收百分比/固定 至少填一个
table_arrange_refresh=该桌台已被安排，请刷新查看
room_num_stay_day=请填写房间数及住店天数
ROOM_IS_FULL=温馨提示，此房间类型已满，建议您选择其他房型
contract_formulas_null=步骤设置失败！找不到学校供应商合同配置，请联系合同管理员。
invalidation_of_agency_contract=所选代理所签合同已过有效期，不允许导单。过期代理：{0}
missing_agent_data=请完成提示所需的资料，再进行导单。
student_condition_type_null=步骤设置失败！业务场景无效，请联系合同管理员。
receivable_amount_error=步骤设置失败！系统计算得出应收应付金额为0，请联系合同管理员。
search_offer_null=找不到学习申请方案
student_info_null=找不到学生信息！请联系系统管理员。
rate_null=找不到汇率
agent_contract_currency_type_no_match=代理合同公式与学习计划币种不匹配，请联系合同管理员。
agent_account_exist=相同币种账户只能有一个处于激活状态
agent_account_contract_data_association=该账户和代理合同存在数据关联
agent_account_settlement_data_association=该账户和佣金结算存在数据关联
agent_account_contract_currency_exist=同一种币种只能绑定一个账户
student_offer_item_failure_abnormal_state=失败记录状态异常
hotelRoomNum_exists=酒店房号已填写，请联系管理员修改
staff_or_bacode_exists=员工名称或者BD编号已存在
hotel_room_num_has_been_updated=当前房间号信息已更新，请确认。
parent_agent_bind_error=无法绑定自身为所属父代理
agent_account_is_default_exist=同一个代理只能有一个首选账户
BUSINESS_PROVIDER_DATA_ASSOCIATION=该业务提供商存在业务数据，无法直接删除。
EVENT_PLAN=计划
EVENT_END=结束
EVENT_CANCEL=取消
EVENT_POSTPONE=延期
agent_bind_parent_error=该代理已与子代理有关联，无法绑定为所属父代理
INSTITUTION_AGENT=校代
AGENT=代理
GUEST=嘉宾
STAFF=员工
WORKING_PERSON=工作人员
person_has_configured_bed=该人员已被安排
CYCLING_REGISTRATION_ID_TYPE=身份证
CYCLING_REGISTRATION_PASSPORT_TYPE=护照
CYCLING_REGISTRATION_PERMIT_TYPE=港澳回乡证
CYCLING_REGISTRATION_TAIWANESE_TYPE=台胞证
CYCLING_REGISTRATION_FAILURE_PAY_STATUS=失败
CYCLING_REGISTRATION_SUCCESS_PAY_STATUS=成功
m_student_offer=留学申请方案
m_student_offer_item=留学申请计划
m_student_insurance=留学保险
m_student_accommodation=留学住宿
m_student_service_fee_cost=留学服务费
client_source=本地市场来源
m_student_service_fee =留学服务费

# 合作伙伴用户注册相关异常消息
partner_user_register_process_failed=合作伙伴用户注册流程失败，代理ID: {0}
partner_user_register_and_email_failed=合作伙伴用户注册和邮件发送失败，代理ID: {0}
contact_persons_empty=联系人列表为空，代理ID: {0}
build_register_dto_params_null=构建注册DTO的参数不能为空
bd_staff_null=BD员工信息为空，代理ID: {0}
app_agent_null=代理申请信息为空，代理ID: {0}
target_person_type_null=目标联系人类型为空，代理ID: {0}
bd_staff_email_incomplete=BD员工邮件信息不完整，代理ID: {0}
duplicate_emails_found=发现重复邮箱地址，代理ID: {0}，重复邮箱: {1}
contact_person_role_key_empty=联系人角色键不能为空
m_business_channel_ins=留学保险渠道
m_business_provider_ins=留学保险提供商
m_business_channel_acc=留学住宿渠道
m_business_provider_acc=留学住宿提供商
m_company=公司代收
m_event_bill=申请活动费用
SALE_STUDENT_OFFER=留学申请
SALE_STUDENT_OFFER_ITEM=留学申请
SALE_STUDENT_ACCOMMODATION=留学住宿
SALE_STUDENT_INSURANCE=留学保险
SALE_STUDENT_SERVICE_FEE=留学服务费
COMPANY_COLLECTION=公司代收
N=等待运行
T=运行中
C=已完成
E=错误
K=手动错误，不需要处理
S=资料错误，需要人手检查继续提交
R=系统特殊问题，待处理
m_agent_contract=学生代理合同
STUDENT_OFFER_CLOSE_STATUS=关闭
STUDENT_OFFER_OPEN_STATUS=打开
STUDENT_OFFER_END_STATUS=终止
STUDENT_OFFER_FINISH_STATUS=成功结案
end_time_not_null=结束时间不能为空
student_offer_item_has_been_parent=该学习计划不能设置为子学习计划的子学习计划
duplicate_proxy=重复的代理：
Bound_BD=绑定的BD：
IS_EXIST_AGENT=名称已经存在
AGENT_STAFF=代理当前绑定BD
PROPERTIES_FOR=性质为
PERSON=个人
COMPANY=公司
AGENT_NAME=代理名称
CONTACT_PHONE=联系人手机
CONTACT_EMAIL=联系人邮箱
CONTACT_TEL=联系人电话
WORKROOM=工作室
OTHER=其他
AGENT_ACCOUNT_NAME=代理账户名
AGENT_ACCOUNT_NO=代理账户号
NAME_OF_A_LEGAL_PERSON=法人名称
INTERNATIONAL_SCHOOL=国际学校
TAX_NUMBER_OT_THE_COMPANY=公司税号
IS_EXIST_AGENT_MSG=【查看代理详情】--点击弹出新tab，查看信息
A_R_AND_A_P_NOT_CREATED=未创建应收和应付
A_R_NOT_CREATED=未创建应收
A_P_NOT_CREATED=未创建应付
A_R_CREATED=已创建应收
A_P_CREATED=已创建应付
A_R_AND_A_P_CREATED=已创建应收和应付
NO_COMMISSION=无佣金
student_offer_is_not_close=该申请方案绑定的学习计划或其子计划已创建有应收或应付计划，作废失败
student_offer_item_is_not_close=该学习计划或其子计划已创建有应收或应付计划，作废失败
receivable_plan_is_not_close=该应收计划已绑定收款单，作废失败
receivable_plan_is_null=应收计划为空
receivable_plan_not_close_pay_plan = 作废失败，应付计划不存在，或已付款或进入结算。
is_not_back=该学习计划或其子计划已创建有应收或应付计划，不允许回退步骤
no_fallback = 申请已确定，不可回退
add_student_insurance_error=该学生未填写护照信息，新增保险失败
match_plan_null=应收计划或者应付计划必须选择其中一项进行信息填写
fixed_amount_cannot_zero=定额金额不能为0
adjust_search=导出数据大于{0}，请调整搜索条件后重新导出
SETTLEMENT_NOT_DONE=处理中
SETTLEMENT_FINISHED=已完成
AP_STATUS_UNPAID=未付
AP_STATUS_PARTIALLY_PAID=已付部分
AP_STATUS_PAID_UP=已付齐
AR_STATUS_NOT_RECEIVED=未收
AR_STATUS_PARTIALLY_RECEIVED=部分已收
AR_STATUS_RECEIVED=已收齐
GET_MSO=MSO
GET_ISSUE=ISSUE
GET_ISSUE_OLD=ISSUE_OLD
GET_IB=IBS
GET_BMS=BMS
GET_HKISO=HKISO
SALE_STUDENT=学生
m_institution_channel=学校渠道
INSTITUTION_PROVIDER=学校提供商
BUSINESS_CHANNEL_INS=留学保险渠道
BUSINESS_CHANNEL_ACC=留学住宿渠道
INSTITUTION_PROVIDER_CHANNEL=学校渠道
NUMBERING=编号
enroll_failure=申请计划：学校名称：{0}，课程名称：{1}，存在实收记录，不能设置入学失败
sale_event_bill_file=活动汇总费用资料
sale_business_provider_agreement=业务提供商合作协议
sale_business_provider_agreement_file=附件文件
convention_gopro_nucleic_acid_file=GoPro核酸资料
GOPRO_HEADQUARTERS=总部
GOPRO_SOUTH_CHINA=华南区
GOPRO_EAST_CHINA=华东区
GOPRO_CENTRAL_CHINA=华中区
GOPRO_SOUTHWEST_CHINA=西南区
GOPRO_WEST_CHINA=西区
GOPRO_NORTHWEST_CHINA=西北区
GOPRO_NORTH_CHINA=华北区
plan_and_invoice_has_generated=应收计划和发票已经生成，无法重复创建
event_bill_has_bound=作废活动费用发起计划失败，有活动归口已经绑定，请先删除绑定的活动
event_bill_has_bound_plan_and_invoice=作废活动费用发起计划失败，已经创建了应收计划和发票
event_bill_has_bound_receipt_form=财务一键作废失败，已经存在收款业务记录
financial_documents_update_status_fail=作废财务单据失败，已经存在收款业务记录
EB_STATUS_UNASSIGNED=未分配
EB_STATUS_ASSIGNED=已经分配
EB_STATUS_COMPLETE_ASSIGNMENT=完成分配
ACC_INACTIVE=作废
ACC_ACTIVE=有效
ACC_SUCCESS=成功
ACC_POSTPONE=延期
APPROVAL_FAILURE=失败
event_not_bind=峰会未绑定活动，请先绑定对应活动
convention_and_event_exist_binding=报名名册或赞助商名册已绑定活动费用，不能解除关联
add_convention_and_event_exist_binding=目标活动已存在关联，无法绑定
convention_sponsor_sponsor_fee_association=该峰会赞助费用类型与赞助商存在数据关联
currency_not_same=项目费用和其他费用币种不同，无法提交
event_currency_not_same=费用归口的活动币种与奖励推广活动的币种不同，无法绑定
event_incentive_currency_not_same=币种与费用归口币种不同，无法绑定
sale_event_incentive_file=奖励推广活动资料
incentive_policy_file=奖励活动资料
event_bill_has_bound_event_incentive=作废活动费用发起计划失败，有奖励推广活动归口已经绑定，请先删除绑定的活动
only_has_one_account=每个币种只能创建一个账户
only_has_one_default=只能设置一个账户为首要
app_agent_contract_need_attached=缺少附件
APP_STATUS_NEW=待审核
APP_STATUS_REVIEW=审核中
APP_STATUS_AGREE=已通过
APP_STATUS_REJECT=已拒绝
AGENT_NATURE_COMPANY=公司
AGENT_NATURE_PERSON=个人
AGENT_NATURE_STUDIO=工作室
AGENT_NATURE_INTERNATIONAL_SCHOOL=国际学校
AGENT_NATURE_OTHER=其他
AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY=个人账户公司
STATISTICS_IN_PROGRESS = 新报表数据统计中，请待完成后再发起新的统计操作
login_id_ne_start_id = 登录用户不是发起人！
approve_comment_null=审批意见不能为空
agent_app_data_error=代理申请数据不存在
app_status_not_review=代理申请状态不是审核中，无法执行拒绝操作
can_not_set_participate_or_not_participate=延迟状态的活动,报名名册不能设置【参加】或【不参加】
has_not_commission_record=无可结算记录！
CONVENTION_STAFF_PROHIBIT=禁止进入
CONVENTION_STAFF_ALLOW=允许进入
PUSH = Push
DIRECT = Direct
not_new_app_status=申请计划已不是New App状态，请确认课程！
not_new_app_item_follow_status=该申请计划后续课程为空，请确认课程！
export_data_error=导出数据错误！
sign_encryption_failed=sign加密失败！
Reach_the_limit=报名人数达到上限
CREATE_COUNT_SORT_TYPE=按新建学生数
CONFIRMATION_COUNT_BY_STUDENT_SORT_TYPE=按定校量
SUCCESS_COUNT_BY_STUDENT_SORT_TYPE=按成功入学量
MAIL_STATUS_NOT_SEND=未发
MAIL_STATUS_SUCCESS=已发
MAIL_STATUS_FAIL=失败
MAIL_STATUS_REJECTION=拒收
REVIEW_STATUS_NOT_REVIEW=未审核
REVIEW_STATUS_REVIEW=已审核
REVIEW_STATUS_DIFFERENTIAL=提差异
different_theme = 不能在不同的主题之间移动
EVENT_PLAN_THEME_ONLINE = 线上活动
EVENT_PLAN_THEME_OFFLINE = 线下活动
EVENT_PLAN_THEME_WORKSHOP = 线下专访
delete_offline_item_fail = 删除线下子项失败，存在报名名册
delete_online_fail = 删除线上活动失败，存在报名名册
delete_workshop_fail = 删除线下专访失败，存在报名名册
#apply_for_overtime_fail = 加班时长需要大于2小时，才可以进行申请
apply_for_overtime_fail = 提交时间为正常工作时间 或 加班时长不足2小时

delete_fail_group_item_exist = 删除KPI方案组别失败，存在KPI方案组别子项
delete_fail_plan_group_exist = 删除KPI方案失败，存在KPI方案组别
BD = BD
STUDENT_OFFER_STAFF = 项目成员
CPM = PM
update_obj_null = 更新对象为空
update_fail_target_group_not_exist = 更新失败，目标所属组别不存在
can_not_add_oneself_to_team = 不能增加自己为团队
receipt_form_item_exist_cancel_fail = 已经存在收款单，不能作废
pay_form_item_exist_cancel_fail = 已经存在付款单，不能作废
FILL_IN_STUDENT_EDUCATION_BACKGROUND=请填写学生国内或国际学习背景资料，若有成绩请一起填写！
DATE_FORMAT_CONVERSION_ERROR=日期格式转换错误
ILLEGAL_OPERATION_PLAN_STUDENT_MISMATCH=非法操作，方案与学生不匹配
DUPLICATE_APPLICATION_PLAN_SUBMIT_TOO_FREQUENT=相同的申请计划已经创建，提交过于频繁，请稍后再操作。
ALREADY_FOLLOWING_PARENT_PLAN=跟随父计划已经是
STATUS_FILL_IN_OFFER_STUDENT_ID=状态，请填写学生编号（Offer Student ID）
PLEASE_SELECT_PROVIDER_AND_CHANNEL=请选择供应商和渠道
ILLEGAL_FILE_TYPE=非法类型文件
SIGN_PARSE_FAILED=sign解析失败！
NON_COMPANY_AGENT_CANNOT_UPLOAD_LICENSE=非公司性质代理不能上传营业执照

#权限中心
staff_followStaff_data_association=该员工业务下属存在数据关联
staff_country_data_association=该员工业务国家存在数据关联
staff_office_data_association=该员工业务办公室存在数据关联
staff_contract_data_association=该员工劳动合同存在数据关联
staff_event_data_association=该员工人事记录存在数据关联
staff_supervisor_data_association=该员工有下属存在数据关联
staff_config_data_association=该员工与配置存在数据关联
staff_resource_data_association=该员工与资源存在数据关联
staff_group_grade_data_association=该员工与权限组别等级存在数据关联
company_followCompany_data_association=该公司下存在下属公司
company_department_data_association=该公司下部门存在数据关联
company_position_data_association=该公司下职位存在数据关联
company_office_data_association=该公司下办公室存在数据关联
company_staff_data_association=该公司下员工存在数据关联
company_permissionGrade_data_association=该公司下权限级别存在数据关联
company_permissionGroup_data_association=该公司下权限组别存在数据关联
department_position_data_association=该部门下职位存在数据关联
department_staff_data_association=该部门下员工存在数据关联
position_staff_data_association=该职位下员工存在数据关联
office_staffOffice_data_association=该办公室与员工业务办公室存在数据关联
office_staff_data_association=该办公室与员工存在数据关联
group_resource_data_association=该组别与权限存在数据关联
group_staff_data_association=该组别与员工存在数据关联
grade_resource_data_association=该级别与权限存在数据关联
grade_staff_data_association=该级别与员工存在数据关联
grade_null=权限等级为空
group_null=权限组别为空
topCompany_exist=Top总公司已存在
staff_id_null=查询员工ID不能为空
department_id_null=查询部门id不能为空
position_id_null=查询职位id不能为空
parent_id_null=parentId不能为空
user_no_active=账户未激活
agent_no_active=代理未激活
old_pwd_incorrect=旧密码不正确
pwd_check_msg=请填写包含大写字母，小写字母,数字和特殊字符的密码，8-12位
superior_bind_error=业务上司不能绑定自身
CHINA=中国大陆 +86
HONG_KONG=中国香港 +852
TAI_WAN=中国台湾 +886
VIETNAM=越南 +84
INDONESIA=印尼 +62
MALAYSIA=马来西亚 +60
LOCAL_NON_AGRICULTURAL_ACCOUNT=本地非农业户口
LOCAL_AGRICULTURAL_ACCOUNT=本地农业户口
NON_AGRICULTURAL_ACCOUNT_IN_OTHER_PLACES=外地非农业户口
REMOTE_AGRICULTURAL_ACCOUNT=外地农业户口
HONGKONG_MACAO_TAIWAN=港澳台
FOREIGN=外籍
IDENTITY_CARD=身份证
PASSPORT=护照
PASS=通行证
RETURN_PERMIT=回乡证
THREE_ONE=3+2
TWO_TWO=2+2
FOUR_ZERO=4+0
EXCHANGE_STUDENTS=交换生
DOUBLE_DEGREE=获得双学位
INTERNATIONAL_DEGREE=获得国际学位
DOMESTIC_DEGREE=获得国内学位
other=其他收入
REWARD_MONEY=奖励金额
INCENTIVE_REWARD=Incentive奖励
INSURANCE_REWARD=保险金额
EVENT_COST=活动费用
OTHER_INCOME=其他收入
FAM_TRIP=Fam Trip
event_bill_is_not_close=活动费用已绑定收款单，作废失败
source_permission_group_grade_null = 原网点资源为空
target_permission_group_grade_null = 目标网点已经配置资源，不能覆盖，请删除已有配置再进行操作。
staff_email_exist=设置【{0}】电邮地址失败，{1}员工已经设置了该邮件地址。
cannot_delete_message=只有离职的员工才可以删除所有邮件设置
simulated_login_exception=模拟登录异常
not_configured_yet_userName_key_or_value=用户名的key或者value未配置
not_configured_yet_password_key_or_value=密码的key或者value未配置
get_page_exception=获取网页信息异常
repeat_email=重复的邮箱，请检查后重新输入！
email_statistics_null=邮箱统计数据为空！
default_config_not_found = 默认配置未找到
MISSING_RELATED_BUSINESS_CONFIGURATION=缺少相关业务配置
PLEASE_UPLOAD_CORRESPONDING_NUMBER_OF_IMAGES_FOR_COMPARISON=请上传对应数量图片进行比对
FACE_MATCH_URL_NOT_CONFIGURED=尚未配置脸部匹配URL地址，请联系管理员
FAILED_TO_GET_PMP_REVIEW_LIST=获取PMP审核列表失败


#学校中心
institution_name_exists=学校名字重复
course_name_exists=课程名字重复
institution_faculty_data_association=该学校与学院存在数据关联
institution_provider_data_association=该学校与提供商存在数据关联
institution_contactPerson_data_association=该学校与联系人存在数据关联
institution_news_data_association=该学校与新闻存在数据关联
institution_character_data_association=该学校与特性存在数据关联
countryInfoType_countryInfo_data_association=该国家资讯类型已被使用
country_countryInfo_data_association=该国家与国家资讯存在数据关联
country_news_data_association=该国家与新闻存在数据关联
cityInfoType_cityInfo_data_association=该城市资讯类型已被使用
city_cityInfo_data_association=该城市与城市资讯存在数据关联
city_cityDivision_data_association=该城市与城市区域存在数据关联
institution_pathway_data_association=已有学校绑定此桥梁学校，类型不允许修改
greater_than_the_company_range_of_the_supplier=配置公司范围不能大于所属提供商的公司范围
missing_maximum_or_minimum=统计目标最大值或最小值缺失
commission_or_amount_fill_least_one=佣金/金额 至少填写一个
commission_or_refund_fill_least_one=返佣/返额 至少填写一个
Contract_type_name_exists=合同类型名称已存在
courseType_course_data_association=该课程类型与课程存在数据关联
courseType_courseTypeGroup_data_association=该课程类型与课程类型分组存在数据关联
courseTypeGroup_courseType_data_association=该课程类型组别与课程类型存在数据关联
course_contract_data_association=该课程与合同存在数据关联
course_coursemajorlevel_data_association=该课程与课程级别存在数据关联
course_coursetype_data_association=该课程与课程类型存在数据关联
course_coursezone_data_association=该课程与校区存在数据关联
course_faculty_data_association=该课程与学院数据关联
course_formulains_data_association=该该课程与公式存在数据关联
courseId_null=课程id不能为空
bridge_courseId_null=桥梁课程id不能为空
faculty_course_data_association=该学院与课程存在数据关联
code_or_name_exists=编号或者名称已存在
name_exists=名称已存在
code_exists=编号已经存在
bridge_school_cannot_bound_bridge_school=桥梁学校不能绑定桥梁学校
bridge_schoolId_empty=桥梁学校id不能为空
providerId_null=提供商id不能为空
provider_type_name_or_key_exists=学校提供商类型名称或类型key已存在
provider_institution_data_association=该提供商与学校存在数据关联
institution_contract_data_association=该学校与合同存在数据关联
institution_contractformula_data_association=该学校与合同公式存在数据关联
provider_contact_person_data_association=该提供商与联系人存在数据关联
provider_contract_data_association=该提供商与合同存在数据关联
provider_contract_formula_data_association=该提供商与合同公式存在数据关联
provider_news_data_association=该提供商与合同公式存在数据关联
provider_item_data_association=该提供商与学习计划存在数据关联
provider_type_provider_data_association=该提供商类型与提供商存在数据关联
course_pathway_course_data_association=学校课程与桥梁学校课程存在数据关联
course_news_data_association=该课程与新闻存在数据关联
course_item_data_association=该课程与学习计划存在数据关联
course_student_character_collection_data_association=请检查课程是否被申请，被收藏，或者有特殊申请信息
types_not_allowed_modified=已有学校绑定此桥梁学校，类型不允许修改
institutionId_null=学校id不能为空
institutionTypeName_null=学校类型名称不能为空
zone_course_data_association=该校区与课程存在数据关联
majorLevelName_exists=专业等级名称已存在
Simplified_Chinese_not_translation=简体不需要翻译
student_offer_not_exists=学习申请方案不存在
client_offer_not_exists = 客户方案不存在
pay_plan_exists=已生成应收应付
plan_state_abnormal=计划状态异常,不能生成应收应付计划
region_has_association=该大区存在关联数据，不能删除
target_login_id_not_exists=目标账号不存在
student_offer_item_step_not_exists=该申请步骤不存在
agent_status_not_allow_renewal=当前合同已驳回/待审核中，不能发送续签邮件！
invalid_student_offer_not_exists=不存在无效的申请方案
INTERLLIGENT_CALSSIFCATION=MSO智选分类
PROFESSIONAL_CLASSIFCATION=MSO国内专业大类
OFFER_ITEM_CLASSIFCATION=BMS申请计划专业分类
MSO_SIMPLE_CLASSIFCATION=MSO简易分类
MSO_MODULE_CLASSIFCATION=MSO模块分类
AREA_COUNTRY_ID_NATIONALITY_IS_NULL=学生国籍为空，请补录学生个人信息
news_type_not_news_type_not_support_email=新闻类型不支持发送新闻邮件。
#财务中心
COMMISSION_PAYABLE_PLAN_NOT_EXIST=应付计划不存在，无法激活佣金
AGENT_STATEMENT_EXPORT_EXCEPTION=代理对账单导出异常，请联系管理员
PAYMENT_FORM_EXISTS=已绑定过付款单
UNCOLLECTED=未收
PARTIALLY_RECEIVED=部分已收
COLLECTION_COMPLETED=收款完成
provider_account_data_association=该供应商与结算账号存在数据关联
provider_contactPerson_data_association=该供应商与联系人存在数据关联
plan_collect_amount=该计划已收齐款项
RECEIVABLES_CANNOT_BE_SETTLED=收款单不可结算，无法激活佣金
bound_repeat=不能重复绑定
totalPay_greater_formAmount=实付总金额大于付款单金额
totalPay_greater_payablePlanAmount=实付总金额不能大于应付计划应付金额
totalReceipt_greater_formAmount=实收总金额大于收款单金额
totalReceipt_greater_receivablePlanAmount=实收总金额不能大于应收计划应收金额
fcy_not_count=外币无法获取汇率
receipt_fee_type_is_not_exists=收款费用类型不存在
service_fee_insufficient_balance=绑定手续费总额不能大于收款单总手续费
payment_form_amount_less_binding_amount=付款单金额小于已绑定的付款子单金额总和
receipt_form_amount_less_binding_amount=收款单金额小于已绑定的收款子单金额总和
receipt_fee_type_can_not_change=收款单已绑定无法修改费用类型
currency_type_num_can_not_change=收款单已绑定活动费用归口无法修改费用币种
invoice_num_is_exist=发票编号已存在
no_provider_found=找不到学习计划对应的提供商，请完善学习计划资料
invoice_bind_fail=发票所绑定的应收计划存在多条，绑定失败！请确认发票信息是否正确。
SELECT_RECEIPT_FORM_TO_MODIFY=请选择要修改的收款单
CURRENCY_MISMATCH_REAL_EXCHANGE_RATE_NOT_ONE=币种不一致实收汇率不能为1
NO_VOUCH_OPERATION_CONFIG=找不到凭证操作配置，请联系系统管理员完善凭证操作配置


#人才中心
industryType_resumeWork_data_association=该行业类型与简历工作经验存在数据关联
otherType_resumeOther_data_association=该附加信息与简历其他存在数据关联
resumeType_resume_data_association=该简历类型与简历存在数据关联
skillType_resumeSkill_data_association=该技能类型与简历技能存在数据关联
edit_resume_fail=当前员工状态不能编辑简历
resume_id_null=简历id不能为空
#工作流中心
start_process_fail_status=流程已经不是待发起状态，无法发起，请刷新
process_already_start_cannot_Invalid=流程已发起，不能作废
process_already_start_cannot_delete=流程已发起，不能删除
process_already_delete=流程已被删除，联系操作人员修改表单状态
redirect_fail=跳转失败
downLoad_bpmn_fail=下载失败，请先设计流程并成功保存
model_null_publish_fail=模型数据为空，请先设计流程并成功保存，再进行发布
import_model_fail=导入模型失败
deploy_model_fail=部署模型失败
create_model_fail=创建模型失败
file_type_error=文件格式错误
history_process_null=历史流程实例为空
process_instance_exist=该流程已经运行过，不能删除
task_obj_null=任务不存在
task_completed = 任务已完成，状态不可修改
insert_task_item_null=新增子任务为空
exist_processed_task_item_cannot_delete=存在已处理子任务，不能删除
task_item_obj_null=子任务不存在
id_or_deleteReason_null=id或者删除理由为空
deploy_process_fail=部署流程失败
Paging_error=分页参数错误
get_userInfo_fail=获取用户信息失败
model_error=数据模型无效，必须有一条主流程
update_false=更新失败
back_error=不是上一个审批用户，不允许撤回
return_the_task_error=当前登录人不是审批人也不是候选人或当前节点是修改资料节点，都不能归还
now_task_update_error=不是节点修改人或当前节点不能编辑
start_error=启动流程失败
not_get_or_sign=没有需要审批或者待签
default_agreement_message=同意
default_disagreement_message=驳回
form_not_found=无法找到对应工休申请单!
form_status_update_fail=工休单状态修改失败!
student_offer_not_found=无法找到对应申请方案
student_offer_status_update_fail=申请方案状态更新失败


#系统中心
resource_groupGrade_data_association=系统资源与组别等级存在数据关联
#文件中心
search_file_null=找不到文件
guid_null=GUID为空
file_name_null=文件名字为空
file_format_error=文件格式不允许上传
file_not_exist=文件不存在
picture_too_big=图片不能大于1M
video_too_big=视频不能大于50M
pdf_too_big=PDF不能大于20M
file_too_big=文件不能大于10M
competition_item_logo_file=竞赛项目Logo
competition_item_banner_file=竞赛项目横幅
app_agent_contract_account_statement_file=声明文件
app_agent_id_card=个人身份证
download_failed=下载失败：
FILE_UPLOAD_ERROR_PLEASE_RETRY=文件上传错误，请重试

#业务平台配置中心
info_type_or_component_name_least_one=资讯类型/页面组件名称 至少填一个
COUNTRY_CHANNEL=国家频道
INFO_CHANNEL_COUNTRY=资讯频道_国家
INFO_CHANNEL_NEWS=资讯频道_新闻
AGENT_INFO_EXITS=代理配置信息已存在
KEY_CODE_INFO_EXITS=系统key已存在
information_collection_configuration_attachment_data_association=该类型与信息收集配置存在数据关联
app_form_division_configure_data_association=该申请单模板和信息收集内容配置存在数据关联
institution_character_item_data_association=该课程动态表单配置与字段配置存在数据关联
app_form_config_attachment_division_data_association=该信息收集内容配置和资料板块或附件存在数据关联
IMPORT_FROM_OLD_DATA=旧数据导入
MANUAL_INPUT=手动录入
IMPORT_FROM_OFFICIAL_DATA=官方数据导入
IMPORT_FROM_OFFICIAL_DATA2=官方数据导入2
IMPORT_FROM_BUSINESS_DATA=业务数据导入
NEED_TO_ADD=需要补充
COMPLETE_COLLECTION=采集完整
menus_contain_subMenus=该菜单下含有子菜单
type_is_null=签到状态不能为空


#帮助中心
help_was_used_to_be_parent_help=该问题被其他问题设置为前置问题，不能删除
DIRECT_DISPLAY=直接显示
SMALL_LAYER_PLAIN_TEXT_DISPLAY=小图层纯文本显示
LARGE_LAYER_RICH_TEXT_DISPLAY=大图层富文本显示
keyCode_exists=系统标识已经存在
helpType_has_child_helpType=该帮助类型存在子帮助类型，不能删除
helpType_is_used=存在该帮助类型的帮助信息，不能删除
keyCode_not_exists=系统标识不存在
upload_failure=上传失败,请重试


#办公中心
start_time_end_time_error=开始时间或结束时间错误 
annualLeaveBase_not_enough=年假不足
compensatoryLeaveBase_not_enough=补休不足
disease_vacation_not_enough=病假不足
annualLeaveBase_not_enough_examine=年假不足（其中有年假在审批中）
compensatoryLeaveBase_not_enough_examine=补休不足（其中有补休在审批中）
disease_vacation_not_enough_examine=病假不足（其中有病假在审批中）
leaveApplicationForm_days_error=请假时间不能低于1个小时
leaveApplicationForm_is_revoke=该工休单已经被撤销成功
childLeaveApplicationForm_to_be_initiated=已存在撤销单处于待发起状态
childLeaveApplicationForm_is_approval_in_progress=已存在撤销单处于审核中
office_leave_application_form_file=工休单凭据
finance_expense_claim_form_file=费用报销单凭证票据
finance_prepay_application_form_file=借款申请单凭证票据
finance_travel_claim_form_file=差旅报销单凭证票据
finance_payment_application_form_file=支付申请单凭证票据
workScheduleDateConfig_duplication=该公司当前日已存在排班
stock_cannot_be_negative=扣减时长不能超过结余时长
workScheduleTimeConfig_not_set=相关部门未设定排班时间
attachedRangeTimeConfig_not_set=考勤时间范围未配置
workScheduleTimeConfig_duplication = 部门设定排班时间存在多个
effective_time_overlap = 配置有效时间与现有特殊人员排班时间配置有效时间重叠

ANNUAL_REFRESH_ANNUAL_LEAVE=年度刷新年假
OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF=加班增加补休
COMPENSATORY_LEAVE_DEDUCTION=补休请假扣减
ANNUAL_LEAVE_DEDUCTION=年假请假扣减
WORKFLOW_CANCELLATION=工作流撤单
HUMAN_RESOURCES_UPDATE=人事手动调整
HUMAN_RESOURCES_ADD=人事手动新增
EXPIRY_DATE_RESET=超有效期清零
UNPAID_LEAVE_DEDUCTION=无薪事假扣减
SICK_LEAVE_DEDUCTION=病假扣减
SICK_LEAVE_QUARTERLY_REFRESH=病假季度刷新
SYSTEM_ADD_STOCK=系统回补作废库存
TO_BE_INITIATED=待发起
APPROVAL_FINISHED=审批结束
APPROVAL_IN_PROGRESS=审批中
APPROVAL_REJECT=审批拒绝
APPROVAL_ABANDONED=申请放弃
CANCELLATION=作废
REVOKED=撤销
leave_form_miss_mediaAndAttached=缺少对应附件，请上传附件后再尝试发起申请
leave_form_overtime_no_revoke=已经超过撤单时间，禁止撤销，请联系管理人员。
work_schedule_time_config_null = 未找到排班时间设定
leave_form_cannot_be_void=该工休单是撤销单，不能作废！
get_accessToken_failed=企业凭证accessToken获取失败！
not_logged_in=用户未登录！
startup_exception=不能发起，发起时间已经晚于申请开始时间或系统允许的发起时间，需要补充附件说明因由。
approval_exception=不能通过审批，审批时间已经晚于系统允许的审批时间，请在批复意见中说明因由。
failed_to_send_message=发送消息失败！
type_error=请选择对应工休申请类型
date_error=当前申请时间为非工作日
void_exception=该工休单是撤销单，不能作废！
result_exceed_limit=搜索结果超出限制上限
no_immediate_superior= 当前用户尚未指定直属上司，因此无法提交工休申请。
WORKING_TIME_SETTING_EXISTS=工作时间设置已存在
FAILED_TO_GET_JSAPI_TICKET=获取企业JsApiTicket失败！
AUTHORIZATION_SETUP_FAILED=设置授权失败
USER_ID_FETCH_FAILED=userId获取失败！
THIRD_PARTY_GET_ENTERPRISE_ACCESS_TOKEN_FAILED=第三方获取企业AccessToken失败！


#通用信息
id_null=id不能为空
search_result_null=没有找到结果
tableId_null=表id是空的
upload_file_null=上传文件不能为空
upload_vo_null=上传对象不能为空
search_vo_null=查询参数不能为空
insert_vo_null=新增参数不能为空
update_vo_null=修改参数不能为空
delete_obj_null=不存在删除的对象
tableName_null=表名不能为空
delete_fail=删除失败
insert_fail=新增失败
update_fail=修改失败
help_fail=该帮助信息为根节点，不能设置前置问题
file_upload_fail=文件上传失败,请重试
file_export_fail=文件导出失败,请重试
num_exist=编号已存在
account_exist=账号已存在
record_exist=已存在相同记录
type_name_exists=类型名称已存在
parameter_missing=参数缺失
status_effective=有效
status_invalid=无效
illegal_type = 非法类型
search_fail=搜索失败
missing_required_configuration=缺少必要的配置信息
has_associated=存在关联子项数据，无法删除
exception_message=异常信息：
feign_execution_failed=feign执行失败
SYSTEM_BUSY=系统繁忙，请稍后再试



#考试中心
examination_has_been_associated=该考试已关联考卷
examinationPaper_has_been_associated=该考卷已关联答题记录
examination_not_active=考试未激活
examinationPaper_not_active=考卷未激活
examination_start_time=考试开始时间为
examination_end_time=考试结束时间为
examinationPaper_start_time=考卷开始时间为
examinationPaper_end_time=考卷结束时间为
please_try_again_later=请稍后重试
examination_is_over=考试已结束
complete_answer_examinationPaper=已在该考卷完成答题
no_examination_assigned=该考卷下未分配考题
answer_all_examination=已完成所有答题记录
examinationPaper_have_answer_records=该考卷已有答题记录,不可修改生成考题数
type_data_exists=当前类型已有题目数据，不可删除
answer_exist=答案已被作答不可删除
question_has_been_examinationPaper=考卷已分配此数据
question_has_been_assigned=考题已分配,不可删除
questionType_has_been_assigned=考题类型已分配,不可删除
exam_unassigned_applet=未分配小程序
question_count_no_zero=生成考题数不能为0
#投票中心
votingRule_associated=存在关联的投票规则，不能删除
VOTING_ITEM_NOT_STARTED=未开始
VOTING_ITEM_HAVE_IN_HAND=进行中
VOTING_ITEM_HAS_END=已结束
voting_theme_not_equal=主题名称不一致
voting_item_option_associated=存在关联的投票选项，不能删除
is_repeat_voting_can_not_null=是否重复投票不能为空
voting_item_null=投票项为空
voting_item_option_null=投票项为空
no_more_person_luck_draw=没有更多人员可参与抽奖
#提醒中心
remind_event_type_key_associated_template=类型Key已关联提醒模板
remind_event_type_key_exist=类型Key已存在
task_start_time_need_greater_now=任务开始时间需要大于当前时间
end_time_need_greater_start_time=任务结束时间需要大于等于任务开始时间
staff_email_null=邮箱为空,请在人员信息中完善邮箱信息
staff_mobile_or_mobileArea_null=手机号码或手机区号为空,请在人员信息中完善手机号码信息
remind_task_max=任务限制数为
remind_emil_send_fail_user=邮箱提醒发送失败，发送失败用户：
task_does_not_exist=任务不存在
task_not_open=任务未开启
task_receiver_is_empty=任务接收人为空
staff_is_null=人员信息为空
mailbox_is_empty=人员邮箱信息为空
mailbox_template_is_empty=邮箱模板不存在，需要配置邮箱模板
phone_number_is_empty=人员手机号码为空
phone_area_code_is_empty=人员手机区号为空
failed_to_send=发送短信失败，异常信息为：
result_null=发送短信失败，返回结果为空
template_is_empty=模板为空
INVALID_PARAMS_TYPE_FOR_EMAIL=电子邮件的参数类型无效


#竞赛中心
COMPETITION_ITEM_LOGO=竞赛项目Logo
COMPETITION_ITEM_BANNER=竞赛项目横幅
template_pic=简历模板图片
SG_TRANSCRIPT_PIC=SG简历成绩单
SG_USER_AVATAR=SG普通用户头像
SG_REPORT_PIC=SG简历活动单
SG_PERSONAL_ALBUM=SG个人相册/个人相册标志
full_fee=無津貼
half_fee=半津 - 半價報名
funding=全津 - 免費報名

#xxljob中心
login_failure=登录失败
get_job_list_fail=jobList获取失败

#SG联系人类型(枚举，父-1，母-2，监护人-3)',
APP_SCHOOLGATE_FATHER=父亲
APP_SCHOOLGATE_MOTHER=母亲
APP_SCHOOLGATE_GUARDIAN=监护人

APP_SCHOOLGATE_TRANSCRIPT=成绩单
APP_SCHOOLGATE_ACADEMIC=学术
APP_SCHOOLGATE_MUSIC=音乐
APP_SCHOOLGATE_ARTS=艺术
APP_SCHOOLGATE_SPORTS=运动
APP_SCHOOLGATE_LANGUAGE=语言
APP_SCHOOLGATE_SERVICE=服务
APP_SCHOOLGATE_SPEECH=演讲/辩论
APP_SCHOOLGATE_HOBBIES=组织/爱好
APP_SCHOOLGATE_OTHERS=其他

APP_SCHOOLGATE_BACKGROUND=背景/介绍
APP_SCHOOLGATE_GOALS=目标和社会贡献
APP_SCHOOLGATE_REASON=选择学校的原因
APP_SCHOOLGATE_LETTER=推荐信


//ClientStaff
fixed_amount_receivable_and_rate_ratio_cannot_be_empty_at_the_same_time= 应收固定金额和费率比率不能同时为空
id_is_null =ID为空
clientId_is_null = 学生资源ID不能为空
staffId_is_null = 员工ID不能为空
staff_is_exist = 员工已绑定
isActive_is_null = 激活状态不能为空

PUBLIC_PARTNER=华通伙伴
PUBLIC_PMP=PMP
institution_provider_do_not_hava_area_country=该业务提供商没有业务国家
news_type_mapping_not_found = 未找到新闻类型
institution_provider_not_found = 机构提供商不存在
no_valid_country_context = 没有有效的国家信息
file_not_found = 附件不存在
department_config_not_found = 部门配置不存在
department_nums_not_found = 部门不存在
news_emil_template_not_found = 邮件模板不存在
news_email_send_fail = 邮件发送失败
sale_student_file_from_rec = 识别资料
AP_STATUS_UNPAID_CREATED_PAYABLE_PLAN = 未付【已创建应付计划】
cannot_be_invalidated = 该服务费存在应收或者应付，作废失败
service_fee_does_not_exist = 服务费不存在
no_data_search = 查询结果为空
service_type_use = 该类型正在使用,删除失败
is_bind_null = 是否绑定，字段为空
agent_commission_type_id_is_null = 代理佣金类型ID为空


agent_label_request_null = 代理标签请求为空
label_list_null =代理标签列表为空
agent_relation_exist=代理标签已存在
email_format_error = 邮箱格式错误

delete_id_null = 删除id为空
delete_agent_commission_type_use = 代理佣金类型已绑定佣金方案，不能删除
inconsistent_data = 移动操作频繁，请重试
event_activity_fees_exceed_budget = 超出已分配活动费用{0}
currency_type_num_not_null = 币种类型参数为空
type_key_exists = 类型系统key已存在
SUBMITTED_TO_FINANCE = 已提交到财务
file_export_failed_no_service_fee = 无服务费，导出失败
group_name_null = 组别名称不能为空
institution_permission_group_id =学校权限组ID不能为空

#PMP
PMP_NO_PERMISSION=您没有权限操作
PMP_PLAN_RECORD_NOT_FOUND=方案审批记录不存在
PMP_NOT_APPROVER=审核失败,当前用户不是方案指定审批人
PMP_END_DATE_REQUIRED=方案结束时间不能为空
PMP_START_DATE_REQUIRED=方案开始时间不能为空
PMP_DUPLICATE_PLAN=已存在该类型的佣金方案，请勿重复添加
PMP_PLAN_LOCKED=方案已锁定,没有权限操作
PMP_PLAN_NOT_FOUND=方案不存在
PMP_SCHOOL_REQUIRED=学校不能为空,请先在合同端完善学校信息
PMP_COURSE_REQUIRED=请先上架至少一个课程
PMP_COURSE_LEVEL_MISSING=课程等级信息缺失
PMP_PLAN_IN_APPROVAL=操作失败,方案正在审批中
PMP_PLAN_SUBMITTED=方案已提交审核
PMP_NOT_ASSIGNED_APPROVER=审核失败,当前用户不是方案指定审批人
PMP_PLAN_STATE_CONFLICT=该合同下的佣金方案审核状态存在审核中,已通过,已驳回等状态,不能修改提供商
PMP_SUBMIT_PLAN_EMPTY=提审方案不能为空
PMP_TERRITORY_REQUIRED=territory不能为空
PMP_PENDING_PLAN_EXISTS=存在待审批方案,请勿提交待审批的方案
PMP_CONTRACT_FILE_MISSING=合同附件暂未上传,请先上传合同附件
PMP_PLAN_INHERITED=方案已被代理方案继承,无法删除
PMP_TERRITORY_RULE_CONFLICT=包括/除外/未指定区域的规则不能共存
PMP_DUPLICATE_COUNTRY=存在重复的国家/区域
PMP_CONTRACT_SCHOOL_MISSING=学校不能为空
PMP_CONTRACT_NOT_FOUND=合同不存在
PMP_CONTRACT_SUBMITTED=合同已提交审核
PMP_PROVIDER_CONTRACT_NOT_FOUND=供应商合同不存在
PMP_UPDATE_CONTRACT_STATUS_FAIL=更新供应商合同状态失败
PMP_ORIGINAL_CONTRACT_NOT_FOUND=原合同不存在
PMP_NEW_CONTRACT_DATE_OVERLAP=新合同时间范围与原合同时间范围重叠
PMP_CONTRACT_ID_REQUIRED=合同id不能为空
PMP_PLAN_EXIST_UNDER_CONTRACT=合同下存在佣金方案，不能删除
PMP_PARAM_REQUIRED=参数不能为空
PMP_APPROVER_NO_COUNTRY_PERMISSION=审批人没有对应的业务国家权限，无法审批，请重新选择
PMP_FILE_DOWNLOAD_FAILED=文件下载失败
PMP_SAVE_SUCCESS=保存成功
PMP_SUBMIT_SUCCESS=提交成功
PMP_APPROVE_SUCCESS=审批成功
PMP_PUBLISH_SUCCESS=上架成功
PMP_UNPUBLISH_SUCCESS=下架成功
PMP_REJECT_PROVIDER_PLAN_FAIL=合同佣金方案还在审核中，暂不能同时操作驳回，待审核结束后，再进行操作。


#PARTNER
PARTNER_EMAIL_PASSWORD_EMPTY=邮箱密码为空,请完善邮箱密码
PARTNER_ADMIN_ROLE_NOT_EXIST=管理员角色不存
PARTNER_SAVE_USER_FAILED=保存用户失败
PARTNER_EMAIL_TEMPLATE_TYPE_ERROR=邮件模板类型错误
PARTNER_EMAIL_TEMPLATE_NOT_EXIST=邮件模板不存在
PARTNER_EMAIL_INFO_EMPTY=邮件信息不能为空
PARTNER_CREATE_FAILED_USER_EXISTS=创建失败,以下账号已存在：

inaccurate_data = 数据不准确
application_plan_delay_date_not_set = 申请计划设置了延时入学标记，但并没有延时日期记录，请补充再提交！
this = 该
is_not_new_app_step = 不是新申请（New App）步骤！
study_plan_not_exist = 学习计划课程信息为空，请先补齐课程信息！
study_plan_provider_not_exist =学习计划提供商信息为空，请先补齐提供商信息！
sub_application_not_selected_course = 子申请还没选择课程，请选择后再进行该操作。
key_word_exist = 该关键词已存在
illegal_target_type = 非法目标类型
receipt_code_error = 回执码错误
FAIRMONT_SANYA_HAITANG_BAY_RESTRICTIONS=费尔蒙酒店最多可预订选一间房，请选择其他酒店。
receipt_code_null = 回执码为空
convention_hotel_room_person= 常规酒店房间人员为空
institution_name_is_null = 学校名称为空
provider_name_is_null = 机构名称为空
convention_registration_id_is_null = 峰会报名Id为空
delete_without_permission = 未经许可删除
convention_person_not_found = 峰会人员未找到
record_not_found =  记录未找到
parameter_is_empty = 参数为空
parameter_is_not_empty = 参数不能为空
email_send_success = 邮件已发送过，无法重新发送！
sign_encrypt_fail= 签名加密失败
agent_not_exist= 代理不存在
nature_is_empty = 性质参数为空
id_card_num_is_empty = 身份证号码为空
tax_code_is_empty = 税号参数为空
type_already_exists= 该类型已存在
not_find_information = 找不到相关信息
express_info_error = 快递信息错误
not_yet_configured= 尚未配置
entry_failed = 进入失败，无效邀请码！(Code error.)
students_do_not_meet_the_requirements =该学生不符合条件！
booth_num_is_exist = 展号已存在
phone_number_is_been_used = 电话号码已被使用
cannot_find_room_type = 找不到房型！
no_room_type_price_set= 没有设置房型价格！
wechat_unified_ordering_exception = 微信统一下单异常
failed_to_obtain_wechat_pay_order_status = 获取微信支付订单状态失败
failed_to_set_creator_creation_time = 设置创建人创建时间失败:
failed_to_set_the_update_person_update_time = 设置更新人更新时间失败：
the_cohabitant_does_not_exist = 该同住人不存在
room_type_name_not_found = 房间类型名为找到
student_statistics_failed = 学生统计失败，请稍后重试
the_strategy_does_not_exist = 该策略不存在
data_is_being_compiled =数据正在统计中,请稍后重试！
invalid_cancellation_of_actual_payment_received = 实收实付作废失败，请稍后重试
the_booth_has_been_ticked = 展位已被勾选
currency_is_not_unique = 币种不唯一
notification_information_cannot_be_empty = 发送的通知信息不能为空
lack_of_business_configuration = 缺少{0}业务配置
illegal_payment_order_type = 非法付款单类型
issue_merge_failed_operation_rollback = issue合并失败，操作回滚
issue_remote_interface_exception = issue远程接口异常,无法合并
step_does_not_exist = 步骤不存在，保存失败
the_receivable_amount_should_be_negative = 应收金额应为负数
the_payable_amount_should_be_negative = 应付金额应为负数
accounts_receivable_do_not_exist = 应收不存在
cancel_fixed_type_accounts_receivable_plans = 只能作废留学申请计划类型应收计划
no_delayed_enrollment_time = 请设置延迟入学时间再提交！
save_error = 保存失败，系统繁忙，请稍后再试
no_relevant_information_found = 找不到相关信息
all_operators_have_been_assigned_permissions = 选择的操作人员均已分配权限！
lack_of_configuration = 缺少配置
please_select_the_contract_to_renew =请选择要续约的合同
illegal_renewal_type = 非法续约类型
select_at_least_one_accounts_receivable_plan = 请至少选择一条应收计划
this_feature_is_not_currently_available = 该功能暂未提供
cannot_find_preferred_account = 找不到首选账户，生成失败
consistent_with_the_current_summit = 被复制的峰会和当前峰会一致，不需要复制！
no_replicable_configuration = 无可复制配置
copy_permission_configuration_failed = 复制权限配置失败
the_payment_cannot_be_settled_for_commission = 该应付不能进行佣金结算
the_agents_bound_to_the_students_are_inconsistent = 需要合并的学生所绑定的代理不一致，暂不支持合并
signature_verification_failed =签名校验失败
request_has_expired =请求已过期
the_invoice_has_been_bound_to_the_payment_receipt = 该发票已绑定收款单不可作废处理
please_select_supplier_and_channel = 请选择供应商和渠道
batch_matching_in_progress = 批量匹配中，勿重复点击，请稍后再查看结果！
project_members_cannot_be_empty = 项目成员不能为空
must_bind = 为必选项目成员，请绑定后再提交
data_exception = 数据异常
the_same_application_plan_has_been_created =相同的申请方案已经创建，提交过于频繁，请稍后再操作。
the_proportion_of_commission_payable_and_the_fixed_amount_payable_cannot_coexist_at_the_same_time = 应付佣金比例和应付定额金额不能同时存在
process_revocation_failed = 流程撤销失败
illegal_type_file = 非法类型文件
only_one_employee_can_be_bound_to_the_same_role_in_the_same_country = 相同国家下的相同角色只能绑定一个员工
illegal_student_data = 非法学生数据，合并失败
end_time_is_not_null = 结束时间不能为空
there_is_settlement_data_for_the_strategy = 该策略存在结算数据，无法操作
booth_is_null = 展位为空
save_failed = 保存失败，邮件服务异常，请联系工作人员
unable_to_find_lottery_ticket = 找不到奖券
no_valid_lottery_tickets_available = 无有效奖券，请联系工作人员
no_configured_email_found = 未找到配置的邮箱
this_phone_number_has_already_been_used = 该手机号已被使用，请换一个手机号或联系管理员修改！
lack_of_necessary_parameters = 缺少必要的参数
translation_error = 翻译报错
at_least_select_a_country = 至少选择国家
no_data_available_for_export = 没有可导出的数据
illegal_state = 非法状态
unable_to_find_corresponding_agent = 找不到对应代理
no_agent_reminder = 无代理提醒
user_information_does_not_exist = 用户信息不存在
user_deactivated =用户已停用
invalid_id_card = 无效身份证，请重新上传
name_is_null = 姓名为空
the_status_cannot_be_empty = 状态不能为空
date_format_conversion_exception = 日期格式转换异常
cannot_modify_order = 订单已支付或订单已取消，不能修改
unable_to_find_employee = 找不到员工 ！！
analysis_failed = 解析失败
authentication_failed = 鉴权失败，请联系管理员
same_target_type_information_already_exists = 已存在相同的目标类型信息
the_length_of_id_card_number_is_incorrect = 身份证号码长度不正确
non_corporate_agents_cannot_upload_their_business_license = 非公司性质代理不能上传营业执照
cannot_modify_course = 已经有实收实付财务数据，不能修改课程,开学时间以及课程长度信息！
non_target_type = 该新闻目标类型非【国家】或【学校】，不允许操作
user_does_not_exist = 用户不存在
the_corresponding_accounts_receivable_plan_has_been_linked_to_invoices = 对应创建的应收计划已经绑定发票，请解绑后再操作！
only_supports_types_student_application_and_study_abroad_insurance = 只支持留学生申请计划、留学保险类型应收计划
platform_authorization_code_has_not_been_configured_yet =尚未配置登录的平台授权码，请联系管理员
platform_type_not_yet_configured_for_login = 尚未配置登录的平台类型fkPlatformTypes，请联系管理员
this_platform_does_not_yet_support_cross_system_login = 该平台尚不支持跨系统登录
illegal_operation_the_plan_does_not_match_the_student = 非法操作，方案与学生不匹配
failed_to_send_sms_verification_code = 短信验证码发送失败
get_exchange_rate_error = 获取汇率错误
translate_fail = 翻译失败
failed_to_send_email_on_behalf_of_project_member = 【项目成员名义发送邮件失败】
email_password_resolution_failed= 邮箱密码解析失败！
the_registration_quota_is_full = 报名名额已满
as_a_mandatory_project_member = 为必选项目成员，无法移除该角色
already_been = 已经被
cannot_be_added_repeatedly = 添加为考核人员，不能再添加
a_signature_with_the_same_title_already_exists = 已经存在相同标题的签名
agent_company = 代理所属公司

contract_formula_read_only = 合同公式只读阶段业务场景不匹配
pre_school_conditions_do_not_match = 前置学校条件不匹配
students_have_prerequisite_school_conditions = 合同公式没有限制前置学校，学生有前置学校条件
pre_group_mismatch = 前置集团不匹配
students_have_prerequisite_group_conditions = 合同公式没有限制前置集团，学生有前置集团条件
study_plan_has_no_prerequisite_course_conditions = 合同公式有课程前置条件,学习计划无前置课程条件
pre_course_mismatch = 前置课程不匹配
students_have_prerequisite_course_requirements = 合同公式没有限制前置课程，学生有前置课程条件

the_current_reward_policy_does_not_exist = 当前奖励政策不存在
the_current_reward_policy_has_been_settled = 当前奖励政策已结算
repeated_selection_of_application_plan = 请不要重复选择已经设置手工计入的申请计划
no_application_plans_that_can_be_manually_deleted= 没有可手动删除的申请计划，请刷新后重试
the_application_plan_has_been_settled = 只有未结算的申请计划，才能设置手动删除，请不要选择已结算的申请计划。
duplicate_deletion_application_plan = 选择请不要重复选择已经设置手动删除的申请计划
reward_policy_does_not_exist = 奖励政策不存在，请刷新后重试
the_reward_policy_has_been_settled = 奖励政策已结算，请刷新后重试
no_unsettled_application_plans_available = 没有可结算的申请计划，请刷新后重试
no_cancellable_application_plans = 没有可取消的申请计划，请刷新后重试
only_unsettled_application_plans_can_be_cancelled_manually = 只有未结算的申请计划，才能取消手动设置，请不要选择已结算的申请计划
only_application_plans_that_are_not_included_in_the_system_can_cancel_manual_settings = 只有非系统计入的申请计划，才能取消手动设置，请不要选择系统计入的申请计划。
no_reward_rule_matching = 无奖励规则匹配
not_meeting_the_requirements_of_the_reward_rules = 统计总数不符合奖励规则的最低人数要求
there_is_data_being_settled = 有数据正在结算中,请稍后重试！
reward_rules_have_not_been_configured_yet = 奖励规则尚未配置，请配置奖励规则
the_prize_name_already_exists = 奖品名称已存在
the_prize_is_currently_in_use = 奖品正在使用中
full_seats_available = 席位满员，请选择其他培训桌
insufficient_seat_balance = 座位余额不足，不能切换活动场地

file_type_key_null = 文件类型不能为空
duplicate_name = 该名称重复，请修改后提交
sort_param_error = 排序参数错误
accounting_item_id_null = 科目id不能为空
accounting_item_code_null = 科目编码不能为空
accounting_item_code_name_null = 科目名称不能为空
accounting_item_type_null = 科目类型不能为空
accounting_item_grade_null =  科目等级不能为空
accounting_item_direction_null = 科目余额方向不能为空
accounting_item_is_active_null = 科目是否启用不能为空
accounting_item_bound_payment_method_type = 该科目绑定了付款方式类型，不能删除
accounting_item_bound_payment_fee_type = 该科目绑定了支付费用类型，不能删除
accounting_item_bound_receipt_fee_type = 该科目绑定了收款费用类型，不能删除
accounting_item_bound_expense_claim_fee_type = 该科目绑定了报销费用类型，不能删除
accounting_item_bound_travel_claim_fee_type = 该科目绑定了差旅报销费用类型，不能删除
accounting_item_bound_receipt_method_type = 该科目绑定了收款方式类型，不能删除
accounting_item_bound_expense_claim_agent_content = 该科目绑定了代理关联费用报销单，不能删除
the_name_or_code_already_exists = 名称或编码已存在
type_name_null = 类型名称不能为空
vouch_type_null = 凭证类型不能为空
receipt_method_type_id_null = 收款方式类型不能为空
receipt_amount_null = 收款金额不能为空
receipt_amount_must_be_greater_than_zero = 收款金额必须大于零！
receipt_date_null = 收款日期不能为空
business_date_null = 业务日期不能为空
vouch_item_null = 凭证明细不能为空 或 凭证明细 缺少借方、贷方信息（凭证明细应填写成对的借方和贷方信息）
delete_obj_used_by_vouch_receipt_register =  已被使用，不能删除 (使用对象：收款记录)
cancel_obj_used_by_vouch_receipt_register =  已被使用，不能作废 (使用对象：收款记录)
cancel_obj_used_by_travel_claim_form_item =  该类型已被使用，不能作废 （使用对象：差旅报销单）
character_limit_exceeded = 字符长度超出限制
no_replicable_template_data = 没有可复制的模板数据
company_profit_and_loss_item_data_already_exists = 公司损益表数据已创建,不能使用模版

REGISTRATION_CONFIRMATION = 参展商已报名，请确认是否继续报名？
TASK_UNFINISHED=待解决
TASK_FINISHED=已解决
TASK_NOT_FINISHED =未能完成
get_bms = BMS
get_issue = ISSUE
AEAS = AEAS

the_area_country_of_the_school_provider_cannot_be_modified = 学校提供商业务国家已绑定对应的学校,不能修改
# 添加新的国际化信息
agent_contact_person_count_invalid=代理申请联系人需要提供3个联系人
agent_new_contact_person_type_invalid=代理申请联系人类型无效，必须包含企业负责人(ADMIN)、佣金结算负责人(COMMISSION)和紧急联系人(EMERGENCY)
agent_emergency_contact_duplicate=【紧急联系人】不能与【企业负责人/小程序管理员】和【佣金结算负责人】同一个人！

staffBdId_is_null = BDID不能为空
contact_person_type_empty=联系人类型不能为空
contact_person_type_conflict=【紧急联系人】与【企业负责人/小程序管理员】或【佣金结算负责人】重复！
contact_person_type_already_exists=该联系人类型已存在
contact_person_already_exists=相同姓名和邮箱的联系人已存在
contact_person_empty=联系人不能为空
contact_type_duplicate=联系人类型重复，每种类型只能添加一个
emergency_contact_duplicate=紧急联系人只能添加一个
admin_contact_duplicate=企业负责人/小程序管理员只能添加一个
commission_contact_duplicate=佣金结算负责人只能添加一个
contact_person_type_exists=联系人类型"{0}"已存在
bd_staff_id_null=BD员工ID不能为空，无法获取发件人信息
agent_ids_null=代理ID集合为空，无法获取BD员工信息
agent_staff_not_found=代理员工关联信息不存在，无法获取BD员工信息
bd_staff_id_empty=BD员工ID集合为空，无法获取BD员工信息
bd_staff_info_get_failed=获取BD员工信息失败，员工ID: {0}
bd_staff_email_null=BD员工邮箱为空，无法作为发件人，员工ID: {0}
bd_staff_email_password_null=BD员工邮件密码为空，无法发送邮件，员工ID: {0}
unsupported_contact_person_type=不支持的联系人类型: {0}
partner_user_register_failed=注册合作伙伴用户失败: {0}

# 合同续签相关异常信息
contract_status_rejected_or_pending=当前合同已驳回/待审核中，不能发送续签邮件
save_agent_application_failed=保存代理申请失败

# 代理联系人异常信息
agent_contact_person_cannot_be_empty=代理联系人不能为空
agent_application_id_cannot_be_empty=代理申请id不能为空

# 邮件相关异常信息
email_recipient_not_empty=收件人邮箱不能为空
email_template_not_empty=邮件模板不能为空
email_format_invalid=邮箱格式不正确
email_parameter_parse_failed=邮件参数解析失败
email_template_process_failed=邮件模板处理失败

# 合同审批相关异常信息
approval_id_required=审批记录ID不能为空
approval_record_not_found=审批记录不存在，ID: {0}
approval_status_no_email_required=审批状态 {0} 不需要发送邮件
contract_id_required=合同ID不能为空
approval_status_required=审批状态不能为空
contract_not_found=合同不存在，ID: {0}
agent_not_found=代理不存在，ID: {0}
bd_staff_info_not_found=未找到代理对应的BD员工信息，代理ID: {0}
bd_staff_email_empty=BD员工邮箱地址为空，员工ID: {0}
partner_user_register_error=注册合作伙伴用户失败
contact_person_not_found=联系人不存在，ID: {0}

cannot_support_retrieving_attributes = 不支持检索属性
decryption_failed = 解密失败

agent_person_id_card_duplicate=该身份证号的个人代理在当前公司下已存在
agent_company_tax_code_duplicate=该税号的公司代理在当前公司下已存在
