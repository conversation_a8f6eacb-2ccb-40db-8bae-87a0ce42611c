package com.get.common.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 动态查询构建器
 * 基于注解自动构建MyBatis Plus查询条件
 * 
 * 使用方式：
 * 1. 在DTO字段上添加@QueryField注解
 * 2. 调用buildQueryWrapper方法自动构建查询条件
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Component
@Slf4j
public class DynamicQueryBuilder {
    
    /**
     * 字段信息缓存，避免重复反射
     * Key: DTO类的Class对象
     * Value: 该类中带有@QueryField注解的字段信息列表
     */
    private static final Map<Class<?>, List<FieldInfo>> FIELD_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 字段名到Lambda函数的映射缓存
     * 这里简化处理，实际项目中可以根据需要扩展
     */
    private static final Map<String, Map<String, SFunction<?, ?>>> LAMBDA_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 根据查询对象动态构建 QueryWrapper
     *
     * @param queryDto 查询DTO对象
     * @param entityClass 实体类Class
     * @param <T> 实体类型
     * @return 构建好的QueryWrapper
     */
    public <T> QueryWrapper<T> buildQueryWrapper(Object queryDto, Class<T> entityClass) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        
        if (queryDto == null) {
            return wrapper;
        }
        
        try {
            // 获取字段信息（带缓存）
            List<FieldInfo> fieldInfos = getFieldInfos(queryDto.getClass());
            
            // 按优先级排序
            fieldInfos = fieldInfos.stream()
                    .sorted(Comparator.comparingInt(f -> f.getQueryField().priority()))
                    .collect(Collectors.toList());
            
            // 构建查询条件
            for (FieldInfo fieldInfo : fieldInfos) {
                buildCondition(wrapper, fieldInfo, queryDto, entityClass);
            }
            
        } catch (Exception e) {
            log.error("构建动态查询条件失败", e);
            throw new RuntimeException("构建查询条件失败: " + e.getMessage(), e);
        }
        
        return wrapper;
    }
    
    /**
     * 构建具体的查询条件
     */
    private <T> void buildCondition(QueryWrapper<T> wrapper, FieldInfo fieldInfo,
                                   Object queryDto, Class<T> entityClass) {
        try {
            // 获取字段值
            Object value = fieldInfo.getField().get(queryDto);
            
            // 检查是否应该忽略该值
            if (shouldIgnoreValue(value, fieldInfo.getQueryField())) {
                return;
            }
            
            // 根据查询类型构建条件
            QueryType queryType = fieldInfo.getQueryField().type();
            String columnName = fieldInfo.getColumnName();
            
            // 这里简化处理，直接使用字符串作为字段名
            // 实际项目中可以根据需要实现字段名到Lambda函数的映射
            buildConditionByType(wrapper, queryType, columnName, value, fieldInfo);
            
        } catch (IllegalAccessException e) {
            log.error("访问字段失败: {}", fieldInfo.getField().getName(), e);
        }
    }
    
    /**
     * 根据查询类型构建具体的查询条件
     * 使用字符串字段名，兼容MyBatis Plus的字符串API
     */
    @SuppressWarnings("unchecked")
    private <T> void buildConditionByType(QueryWrapper<T> wrapper, QueryType queryType,
                                         String columnName, Object value, FieldInfo fieldInfo) {

        switch (queryType) {
            case EQ:
                wrapper.eq(columnName, value);
                break;
            case NE:
                wrapper.ne(columnName, value);
                break;
            case LIKE:
                wrapper.like(columnName, value);
                break;
            case LIKE_LEFT:
                wrapper.likeLeft(columnName, value);
                break;
            case LIKE_RIGHT:
                wrapper.likeRight(columnName, value);
                break;
            case GT:
                wrapper.gt(columnName, value);
                break;
            case GE:
                wrapper.ge(columnName, value);
                break;
            case LT:
                wrapper.lt(columnName, value);
                break;
            case LE:
                wrapper.le(columnName, value);
                break;
            case IN:
                if (value instanceof Collection) {
                    Collection<?> collection = (Collection<?>) value;
                    if (!collection.isEmpty()) {
                        wrapper.in(columnName, collection);
                    }
                }
                break;
            case NOT_IN:
                if (value instanceof Collection) {
                    Collection<?> collection = (Collection<?>) value;
                    if (!collection.isEmpty()) {
                        wrapper.notIn(columnName, collection);
                    }
                }
                break;
            case IS_NULL:
                wrapper.isNull(columnName);
                break;
            case IS_NOT_NULL:
                wrapper.isNotNull(columnName);
                break;
            case BETWEEN:
                if (value instanceof List) {
                    List<?> range = (List<?>) value;
                    if (range.size() == 2 && range.get(0) != null && range.get(1) != null) {
                        wrapper.between(columnName, range.get(0), range.get(1));
                    }
                }
                break;
            case NOT_BETWEEN:
                if (value instanceof List) {
                    List<?> range = (List<?>) value;
                    if (range.size() == 2 && range.get(0) != null && range.get(1) != null) {
                        wrapper.notBetween(columnName, range.get(0), range.get(1));
                    }
                }
                break;
            case ORDER_BY:
                if (fieldInfo.getQueryField().asc()) {
                    wrapper.orderByAsc(columnName);
                } else {
                    wrapper.orderByDesc(columnName);
                }
                break;
            case IGNORE:
                // 忽略该字段，不做任何处理
                break;
            default:
                log.warn("不支持的查询类型: {}", queryType);
                break;
        }
    }

    /**
     * 获取字段信息（带缓存）
     */
    private List<FieldInfo> getFieldInfos(Class<?> clazz) {
        return FIELD_CACHE.computeIfAbsent(clazz, this::parseFieldInfos);
    }

    /**
     * 解析字段信息
     */
    private List<FieldInfo> parseFieldInfos(Class<?> clazz) {
        List<FieldInfo> fieldInfos = new ArrayList<>();

        // 获取所有字段，包括父类字段
        List<Field> allFields = getAllFields(clazz);

        for (Field field : allFields) {
            QueryField queryField = field.getAnnotation(QueryField.class);
            if (queryField != null) {
                field.setAccessible(true);
                fieldInfos.add(new FieldInfo(field, queryField));
            }
        }

        return fieldInfos;
    }

    /**
     * 获取类的所有字段，包括父类字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields;
    }

    /**
     * 判断是否应该忽略该值
     */
    private boolean shouldIgnoreValue(Object value, QueryField queryField) {
        // 如果注解设置不忽略空值，则不忽略
        if (!queryField.ignoreEmpty()) {
            return false;
        }

        // null值忽略
        if (value == null) {
            return true;
        }

        // 空字符串忽略
        if (value instanceof String && ((String) value).trim().isEmpty()) {
            return true;
        }

        // 空集合忽略
        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
            return true;
        }

        // 空数组忽略
        if (value.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(value) == 0;
        }

        return false;
    }

    /**
     * 清空缓存（用于测试或特殊场景）
     */
    public void clearCache() {
        FIELD_CACHE.clear();
        LAMBDA_CACHE.clear();
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("fieldCacheSize", FIELD_CACHE.size());
        stats.put("lambdaCacheSize", LAMBDA_CACHE.size());
        stats.put("cachedClasses", FIELD_CACHE.keySet().stream()
                .map(Class::getSimpleName)
                .collect(Collectors.toList()));
        return stats;
    }
}
