package com.get.common.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.common.result.Page;

/**
 * 查询工具类
 * 提供静态方法简化动态查询的使用
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
public class QueryUtils {
    
    private static DynamicQueryBuilder queryBuilder = new DynamicQueryBuilder();
    
    /**
     * 构建查询条件
     *
     * @param queryDto 查询DTO
     * @param entityClass 实体类
     * @param <T> 实体类型
     * @return QueryWrapper
     */
    public static <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass) {
        return queryBuilder.buildQueryWrapper(queryDto, entityClass);
    }

    /**
     * 构建查询条件并添加自定义条件
     *
     * @param queryDto 查询DTO
     * @param entityClass 实体类
     * @param customizer 自定义查询条件
     * @param <T> 实体类型
     * @return QueryWrapper
     */
    public static <T> QueryWrapper<T> buildQuery(Object queryDto, Class<T> entityClass,
                                                      QueryCustomizer<T> customizer) {
        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass);
        if (customizer != null) {
            customizer.customize(wrapper);
        }
        return wrapper;
    }
    
    /**
     * 构建分页查询条件
     *
     * @param queryDto 查询DTO
     * @param entityClass 实体类
     * @param page 分页参数
     * @param <T> 实体类型
     * @return 包含分页信息的查询条件
     */
    public static <T> QueryResult<T> buildPageQuery(Object queryDto, Class<T> entityClass, Page page) {
        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass);
        return new QueryResult<>(wrapper, page);
    }

    /**
     * 构建分页查询条件并添加自定义条件
     */
    public static <T> QueryResult<T> buildPageQuery(Object queryDto, Class<T> entityClass,
                                                   Page page, QueryCustomizer<T> customizer) {
        QueryWrapper<T> wrapper = buildQuery(queryDto, entityClass, customizer);
        return new QueryResult<>(wrapper, page);
    }
    
    /**
     * 快速构建简单的等值查询
     */
    public static <T> QueryWrapper<T> buildSimpleQuery(Class<T> entityClass, String column, Object value) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (value != null) {
            wrapper.eq(column, value);
        }
        return wrapper;
    }

    /**
     * 快速构建模糊查询
     */
    public static <T> QueryWrapper<T> buildLikeQuery(Class<T> entityClass, String column, String value) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (value != null && !value.trim().isEmpty()) {
            wrapper.like(column, value);
        }
        return wrapper;
    }

    /**
     * 快速构建IN查询
     */
    public static <T> QueryWrapper<T> buildInQuery(Class<T> entityClass, String column, java.util.Collection<?> values) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        if (values != null && !values.isEmpty()) {
            wrapper.in(column, values);
        }
        return wrapper;
    }
    
    /**
     * 查询自定义器接口
     */
    @FunctionalInterface
    public interface QueryCustomizer<T> {
        void customize(QueryWrapper<T> wrapper);
    }

    /**
     * 查询结果封装类
     */
    public static class QueryResult<T> {
        private final QueryWrapper<T> wrapper;
        private final Page page;

        public QueryResult(QueryWrapper<T> wrapper, Page page) {
            this.wrapper = wrapper;
            this.page = page;
        }

        public QueryWrapper<T> getWrapper() {
            return wrapper;
        }

        public Page getPage() {
            return page;
        }
    }
    
    /**
     * 清空缓存
     */
    public static void clearCache() {
        queryBuilder.clearCache();
    }
    
    /**
     * 获取缓存统计
     */
    public static java.util.Map<String, Object> getCacheStats() {
        return queryBuilder.getCacheStats();
    }
}
