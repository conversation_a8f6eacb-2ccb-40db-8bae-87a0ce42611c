package com.get.workflowcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.activiti.engine.task.Comment;

import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/26 16:12
 * @verison: 1.0
 * @description:
 */
@Data
public class ActHiCommentVo extends BaseVoEntity {
    /**
     * 当前节点
     */
    @ApiModelProperty(value = "当前节点")
    private String currentNode;

    /**
     * 审批流程
     */
    @ApiModelProperty(value = "审批流程")
    private String currentFlow;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    private List<Comment> taskComments;

    /**
     * 审批开始时间
     */
    @ApiModelProperty(value = "审批开始时间")
    private Date handlingStartTime;

    /**
     * 审批结束时间
     */
    @ApiModelProperty(value = "审批结束时间")
    private Date handlingEndTime;

    /**
     * 任务耗时
     */
    @ApiModelProperty("任务耗时")
    private String totalTime;

    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    private String assignee;

    /**
     * 审批动作:0/1:驳回/同意
     */
    @ApiModelProperty(value = "审批动作:0/1:驳回/同意")
    private Integer approvalAction;

    /**
     * 审批动作名称：驳回/同意
     */
    @ApiModelProperty(value = "审批动作名称：驳回/同意")
    private String approvalActionName;

    private static final long serialVersionUID = 1L;

    @TableField("TYPE_")
    private String type;
    @TableField("TIME_")
    private Date time;
    @TableField("USER_ID_")
    private String userId;
    @TableField("TASK_ID_")
    private String taskId;
    @TableField("PROC_INST_ID_")
    private String procInstId;
    @TableField("ACTION_")
    private String action;
    @TableField("MESSAGE_")
    private String message;
    @TableField("FULL_MSG_")
    private byte[] fullMsg;


}
