package com.get.workflowcenter.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.workflowcenter.entity.ActHiProcinst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.activiti.engine.task.Comment;

import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/12/18 18:06
 */
@Data
public class ActHiProcinstVo extends BaseVoEntity {
    @ApiModelProperty("流程名称")
    private String procesName;
    @ApiModelProperty("流程版本")
    private int procesVersion;
    @ApiModelProperty("当前步骤")
    private String actName;
    @ApiModelProperty("流程完成进度")
    private String whetherComplete;
    @ApiModelProperty("流程完成进度标识")
    private Integer whetherCompleteState;
    @ApiModelProperty("任务耗时")
    private String totalTime;
    @ApiModelProperty("批复意见")
    private List<Comment> msg;
    @ApiModelProperty("执行人/审批人")
    private String assignee;
    /*    @ApiModelProperty("请假信息")
        private WholidayFlow wholidayFlow;*/
    @ApiModelProperty("开始时间")
    private String startTimeDto;
    @ApiModelProperty("流程定义的key")
    private String procdefKey;
    @ApiModelProperty("发起人")
    private String startByName;
    @ApiModelProperty("流程实例id")
    private String deployId;
    @ApiModelProperty("单条批复意见")
    private String singleMsg;

    @TableField("PROC_INST_ID_")
    private String procInstId;
    @TableField("BUSINESS_KEY_")
    private String businessKey;
    @TableField("PROC_DEF_ID_")
    private String procDefId;
    @TableField("START_TIME_")
    private Date startTime;
    @TableField("END_TIME_")
    private Date endTime;
    @TableField("DURATION_")
    private Long duration;
    @TableField("START_USER_ID_")
    private String startUserId;
    @TableField("START_ACT_ID_")
    private String startActId;
    @TableField("END_ACT_ID_")
    private String endActId;
    @TableField("SUPER_PROCESS_INSTANCE_ID_")
    private String superProcessInstanceId;
    @TableField("DELETE_REASON_")
    private String deleteReason;
    @TableField("TENANT_ID_")
    private String tenantId;
    @TableField("NAME_")
    private String name;

}
