package com.get.workflowcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/11/9 12:07
 * @verison: 1.0
 * @description:
 */
@Data
public class HiCommentVo {

    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    /**
     * 历史task
     */
    @ApiModelProperty(value = "历史task")
    private List<ActHiCommentVo> actHiCommentDs;

}
