package com.get.workflowcenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.institutioncenter.entity.Institution;
import com.get.workflowcenter.dto.ApprovalRecordDto;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.ApprovalRecordsService;
import com.get.workflowcenter.vo.ApprovalRecordListVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class ApprovalRecordsServiceImpl implements ApprovalRecordsService {

    @Resource
    private ActRuTaskMapper actRuTaskMapper;

    /**
     * 审批列表
     * @param approvalRecordDto
     * @return
     */
    @Override
    public List<ApprovalRecordListVo> datas(ApprovalRecordDto approvalRecordDto, Page page) {
        IPage<ApprovalRecordListVo> IPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ApprovalRecordListVo> approvalRecordListVoList = actRuTaskMapper.getApprovalRecordsList(approvalRecordDto, SecureUtil.getStaffId());
        page.setAll((int) IPage.getTotal());
        return approvalRecordListVoList;
    }

}
