package com.get.workflowcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ApprovalRecordListVo {

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "业务key")
    private String businessKey;

    @ApiModelProperty(value = "编号")
    private String num;

    @ApiModelProperty(value = "审批标题")
    private String title;

    @ApiModelProperty("申请人")
    private String applyName;

    @ApiModelProperty("部门名")
    private String departmentName;

    @ApiModelProperty("申请原因")
    private String reason;

    @ApiModelProperty("申请状态")
    private Integer status;

    @ApiModelProperty("当前审批人")
    private String currentApproverName;

    @ApiModelProperty("申请时间")
    private Date gmtCreate;

}
