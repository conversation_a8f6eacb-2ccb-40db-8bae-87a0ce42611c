package com.get.workflowcenter.listener;

import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * test1
 *
 * <AUTHOR>
 * @date 2023/7/3 10:20
 */
public class Test1Listerner  implements TaskListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private TaskService taskService;
    @Resource
    private ProcessEngine processEngine;

    @Override
    public void notify(DelegateTask delegateTask) {
        logger.info("进入test1监听--------------------------------");
        //查询流程定义的查询对象。可以使用该查询对象对已经部署的流程定义进行查询，如根据流程定义的key或者ID等条件进行查询。
        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateTask.getId())
                .singleResult();

        System.out.println("processDefinition.getKey(): " + processDefinition.getKey());
        //指派员工
        taskService.setAssignee(delegateTask.getId(), "762");


    }

}
